# Kafka Docker-Compose UUID 生成修复

## 问题描述

在 `deploy_kafka.sh` 脚本中，生成的 `docker-compose.yaml` 文件中的 `CLUSTER_ID` 环境变量包含了完整的日志输出，而不是纯净的 UUID 值。

### 原始问题示例

```yaml
environment:
  CLUSTER_ID: '[2025-06-19 18:07:43] [INFO] 在 10.81.12.47 上执行命令:             if command -v uuidgen >/dev/null 2>&1; then                uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]'            elif command -v python3 >/dev/null 2>&1; then                python3 -c 'import uuid; print(str(uuid.uuid4()).replace("-", ""))'            else                # 备用方法：使用随机数生成                echo $(date +%s)$(shuf -i 1000-9999 -n 1) | md5sum | cut -d' ' -f1            fi        c75a66fe582642e48a9d9f4a793b5abc'
```

## 问题原因

问题出现在 `generate_docker_compose_config` 函数中的 UUID 生成逻辑：

```bash
KAFKA_CLUSTER_ID=$(remote_execute "$host" "
    if command -v uuidgen >/dev/null 2>&1; then
        uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]'
    # ... 其他逻辑
" | tr -d '\r\n')
```

`remote_execute` 函数会记录执行的命令到日志，这些日志输出被一起捕获到了 `KAFKA_CLUSTER_ID` 变量中。

## 修复方案

### 1. 直接使用 SSH 命令

修改 UUID 生成逻辑，直接使用 SSH 命令而不是 `remote_execute` 函数，避免捕获日志输出：

```bash
# 直接通过SSH执行命令，避免remote_execute的日志输出
KAFKA_CLUSTER_ID=$(ssh -o ConnectTimeout=${SSH_TIMEOUT:-30} -o BatchMode=yes "$DEPLOY_USER@$host" "$uuid_command" 2>/dev/null | tr -d '\r\n')
```

### 2. 添加 UUID 格式验证

增加 UUID 格式验证，确保生成的 UUID 符合预期格式（32位十六进制字符）：

```bash
# 验证生成的UUID格式（应该是32位十六进制字符）
if [[ ! "$KAFKA_CLUSTER_ID" =~ ^[a-f0-9]{32}$ ]]; then
    log_warn "生成的集群ID格式不正确，使用备用方法"
    # 备用方法：本地生成UUID
    if command -v uuidgen >/dev/null 2>&1; then
        KAFKA_CLUSTER_ID=$(uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]')
    else
        # 最后备用：使用时间戳和随机数
        KAFKA_CLUSTER_ID=$(echo "$(date +%s)$(shuf -i 1000-9999 -n 1)" | md5sum | cut -d' ' -f1)
    fi
fi
```

### 3. 多层备用方案

实现多层备用方案，确保在各种环境下都能生成有效的 UUID：

1. **远程生成**：在目标主机上使用 `uuidgen` 或 `python3` 生成
2. **本地生成**：如果远程生成失败，在本地生成 UUID
3. **时间戳方案**：如果都不可用，使用时间戳和随机数生成

## 修复后的效果

### 正确的 docker-compose.yaml 示例

```yaml
environment:
  CLUSTER_ID: 'c75a66fe582642e48a9d9f4a793b5abc'
```

### 验证方法

运行测试脚本验证修复效果：

```bash
./test/test_kafka_uuid_fix.sh
```

测试包括：
- UUID 生成功能测试
- UUID 格式验证测试
- docker-compose 配置生成测试

## 相关文件

- **主要修复文件**: `scripts/auto_deploy/deploy_kafka.sh`
- **测试文件**: `test/test_kafka_uuid_fix.sh`
- **文档文件**: `docs/kafka_uuid_fix.md`

## 技术细节

### UUID 格式要求

Kafka KRaft 模式要求 `CLUSTER_ID` 为 32 位十六进制字符串（无连字符的 UUID）。

### SSH 连接配置

修复方案使用以下 SSH 配置：
- `ConnectTimeout`: 使用 `SSH_TIMEOUT` 配置（默认 30 秒）
- `BatchMode=yes`: 非交互模式
- 错误输出重定向到 `/dev/null`

### 兼容性考虑

修复方案保持与现有配置系统的兼容性：
- 使用现有的 `DEPLOY_USER` 和 `SSH_TIMEOUT` 配置
- 保持原有的备用方案逻辑
- 不影响其他功能的正常运行

## 总结

此修复解决了 Kafka docker-compose 配置中 `CLUSTER_ID` 包含日志输出的问题，确保生成的配置文件格式正确，Kafka 集群能够正常启动和运行。
