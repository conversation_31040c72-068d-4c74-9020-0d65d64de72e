# Redis集群部署文档

## 概述

本文档描述了Redis集群在工业环境中的部署方案，采用cluster模式，与NebulaGraph图数据库共享节点资源。Redis集群使用3主3从架构，每个主机运行一个Redis实例，使用标准6379端口。

## 部署架构

### 节点配置

Redis集群采用3主3从架构，与NebulaGraph共享以下节点：

| 主机IP | 主机名 | Redis端口 | 角色 | 共享服务 |
|--------|--------|-----------|------|----------|
| *********** | redis-master-01 | 6379 | Master-1 | NebulaGraph |
| *********** | redis-master-02 | 6379 | Master-2 | NebulaGraph |
| *********** | redis-master-03 | 6379 | Master-3 | NebulaGraph |
| *********** | redis-slave-01 | 6379 | Slave-1 | NebulaGraph |
| *********** | redis-slave-02 | 6379 | Slave-2 | NebulaGraph |
| *********** | redis-slave-03 | 6379 | Slave-3 | NebulaGraph |

### 主从关系

Redis集群使用自动分片和故障转移，主从关系由Redis集群自动管理：

- Slave-1 (***********:6379) -> Master-1 (***********:6379)
- Slave-2 (***********:6379) -> Master-2 (***********:6379)
- Slave-3 (***********:6379) -> Master-3 (***********:6379)

### 部署流程

1. **环境准备**: 创建Redis用户、目录结构、权限设置
2. **依赖安装**: 从本地虚拟机镜像仓库安装Redis 7.2.7
3. **配置生成**: 为每个主机生成Redis配置文件和systemd服务
4. **服务启动**: 启动所有Redis实例
5. **集群初始化**: 使用redis-cli创建3主3从集群
6. **状态验证**: 验证集群状态和节点连通性

## 安装要求

### 系统要求

- **操作系统**: openEuler-20.03-LTS (固定版本)
- **CPU架构**: x86_64
- **Redis版本**: 7.2.7 (优先从本地虚拟机镜像仓库安装)

### 硬件资源分配

#### 共享节点配置
- **总CPU**: 24核
- **总内存**: 48GB
- **总存储**: 700GB SSD
- **网络**: 1Gbps

#### Redis资源分配
- **CPU**: 8核 (共享节点分配)
- **内存**: 16GB (每实例限制4GB)
- **存储**: 200GB SSD
- **端口**: 6379 (标准Redis端口)

## 安装方式

### 安装策略

采用**纯本地虚拟机镜像仓库安装**策略：

1. **本地虚拟机镜像仓库** (唯一方式)
   - 目标版本: Redis 7.2.7 或相近版本
   - 自动恢复原始YUM仓库配置
   - 禁用可能冲突的local.repo配置

2. **安装失败处理**
   - 如果本地仓库不可用，直接放弃安装
   - 不使用外部网络源
   - 不进行源码编译安装

### 仓库管理机制

脚本会自动管理YUM仓库配置：

```bash
# 仓库恢复查找顺序
1. /etc/yum.repos.d/backup/net.repo
2. /etc/yum.repos.d/backup*/net.repo

# 冲突处理
- 禁用local.repo (备份为local.repo.disabled)
- 恢复原始net.repo配置
- 清理并重建YUM缓存
```

## 部署步骤

### 1. 基础部署

```bash
# 完整部署
./deploy_redis.sh

# 跳过安装，仅配置
./deploy_redis.sh --skip-install

# 强制重新安装
./deploy_redis.sh --force-reinstall

# 预览模式
./deploy_redis.sh --dry-run
```

### 2. 集群初始化

```bash
# 跳过集群初始化
./deploy_redis.sh --skip-init

# 仅初始化集群
./deploy_redis.sh --skip-install --skip-config
```

### 3. 集群创建命令

实际执行的集群创建命令：
```bash
echo 'yes' | REDISCLI_AUTH='<自动生成密码>' /apps/redis/bin/redis-cli \
  --cluster create \
  ***********:6379 ***********:6379 ***********:6379 \
  ***********:6379 ***********:6379 ***********:6379 \
  --cluster-replicas 1
```

### 4. 卸载

```bash
# 完整卸载（含数据备份）
./deploy_redis.sh --uninstall

# 卸载不备份数据
./deploy_redis.sh --uninstall --no-backup
```

## 配置说明

### 主要配置文件

- `config/hosts.conf` - 主机和端口配置
- `config/redis.conf` - Redis服务配置
- `/apps/redis/conf/redis-{port}.conf` - 各端口实例配置

### 关键配置参数

```bash
# 基础配置
port 6379
bind 0.0.0.0
protected-mode no
daemonize yes

# 内存配置（适应共享环境）
maxmemory 4gb
maxmemory-policy allkeys-lru

# 集群配置
cluster-enabled yes
cluster-node-timeout 15000
cluster-require-full-coverage no

# 持久化配置
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# 安全配置
requirepass <自动生成的密码>
masterauth <自动生成的密码>
```

## 目录结构

```
/apps/redis/                    # Redis安装目录
├── bin/                        # 二进制文件（符号链接到YUM安装的系统文件）
│   ├── redis-server -> /usr/bin/redis-server
│   ├── redis-cli -> /usr/bin/redis-cli
│   ├── redis-sentinel -> /usr/bin/redis-sentinel
│   └── redis-benchmark -> /usr/bin/redis-benchmark
├── conf/                       # 配置文件
│   ├── redis-template.conf     # 配置模板
│   └── redis-6379.conf         # 各主机的Redis配置
└── logs/                       # 日志文件

/apps/data/redis/               # Redis数据目录
├── data/                       # 通用数据文件
├── log/                        # 运行日志
├── backup/                     # 备份文件
└── 6379/                       # 端口6379数据目录（每个主机一个）

/var/run/redis/                 # 运行时文件
└── redis-6379.pid              # 进程ID文件
```

## 服务管理

### Systemd服务

每个主机运行一个Redis实例，使用独立的systemd服务：

```bash
# 服务状态检查
systemctl status redis-6379

# 启动/停止服务
systemctl start redis-6379
systemctl stop redis-6379

# 启用/禁用开机自启
systemctl enable redis-6379
systemctl disable redis-6379

# 查看所有Redis服务
systemctl list-units "redis-*"
```

### 集群管理

```bash
# 查看集群状态（使用任意节点）
redis-cli -p 6379 cluster info
redis-cli -p 6379 cluster nodes

# 集群健康检查
redis-cli -p 6379 --cluster check ***********:6379

# 重新分片（如需要）
redis-cli -p 6379 --cluster reshard ***********:6379

# 添加认证的集群操作
REDISCLI_AUTH='<密码>' redis-cli -p 6379 cluster info
```

## 监控和维护

### 日志文件

- **部署日志**: `/var/log/deploy/redis_deploy.log`
- **Redis日志**: `/apps/data/redis/log/redis-6379.log`
- **系统日志**: `journalctl -u redis-6379`

### 性能监控

```bash
# 内存使用情况
redis-cli -p 6379 info memory

# 连接数统计
redis-cli -p 6379 info clients

# 命令统计
redis-cli -p 6379 info commandstats

# 集群槽分布
redis-cli -p 6379 cluster slots

# 带认证的监控命令
REDISCLI_AUTH='<密码>' redis-cli -p 6379 info memory
```

### 备份策略

- **自动备份**: RDB快照 + AOF日志
- **手动备份**: `redis-cli -p 6379 BGSAVE`
- **卸载备份**: 自动备份到 `/backup/redis/YYYYMMDD_HHMMSS/`
- **备份内容**: 数据文件、配置文件、日志文件

## 故障排除

### 常见问题

1. **集群节点数量不足错误**
   ```bash
   # 错误: [ERR] Wrong number of arguments for specified --cluster sub command
   # 原因: 集群节点列表生成逻辑错误，只生成了一个节点
   # 解决: 检查hosts.conf中REDIS_HOSTS和REDIS_HOST_PORTS配置
   ```

2. **仓库配置丢失**
   ```bash
   # 手动恢复仓库
   cp /etc/yum.repos.d/backup*/net.repo /etc/yum.repos.d/
   yum clean all && yum makecache
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :6379
   ```

4. **集群初始化失败**
   ```bash
   # 清理集群状态
   redis-cli -p 6379 cluster reset

   # 带认证的集群重置
   REDISCLI_AUTH='<密码>' redis-cli -p 6379 cluster reset
   ```

5. **内存不足**
   ```bash
   # 检查内存使用
   redis-cli -p 6379 info memory
   # 调整maxmemory配置
   ```

6. **节点状态异常**
   ```bash
   # 检查所有节点状态
   for host in *********** *********** *********** *********** *********** ***********; do
       echo "检查 $host:6379"
       redis-cli -h $host -p 6379 ping
   done
   ```

### 日志分析

```bash
# 查看部署日志
tail -f /var/log/deploy/redis_deploy.log

# 查看Redis错误日志
grep ERROR /apps/data/redis/log/redis-6379.log

# 查看系统服务日志
journalctl -u redis-6379 -f

# 查看集群初始化日志
grep "cluster create" /var/log/deploy/redis_deploy.log
grep "集群创建" /var/log/deploy/redis_deploy.log
```

## 测试验证

### 运行测试套件

```bash
# 配置测试
./test/test_redis_cluster_config.sh

# 安装功能测试
./test/test_redis_yum_install.sh

# 卸载功能测试
./test/test_redis_uninstall.sh
```

### 手动验证

```bash
# 连接测试
redis-cli -p 6379 ping

# 带认证的连接测试
REDISCLI_AUTH='<密码>' redis-cli -p 6379 ping

# 集群测试
redis-cli -p 6379 -c set test_key "test_value"
redis-cli -h *********** -p 6379 -c get test_key

# 故障转移测试
systemctl stop redis-6379  # 在某个主节点上
redis-cli -h *********** -p 6379 cluster nodes  # 检查从节点状态

# 集群完整性测试
for host in *********** *********** *********** *********** *********** ***********; do
    echo "测试节点 $host:6379"
    redis-cli -h $host -p 6379 -c set "test_$host" "value_$host"
done
```

## 注意事项

1. **资源共享**: Redis与NebulaGraph共享节点，需要合理分配资源
2. **版本限制**: 仅支持本地虚拟机镜像仓库提供的Redis版本
3. **网络配置**: 确保集群节点间网络互通，特别是6379端口
4. **数据安全**: 卸载前务必备份重要数据
5. **服务依赖**: 注意与NebulaGraph的服务启动顺序
6. **集群端口**: 除了6379端口，Redis集群还会使用16379端口进行节点通信
7. **密码管理**: Redis密码自动生成，可在配置文件中查看
8. **故障恢复**: 集群模式下单节点故障不影响整体服务

## 集群初始化修复说明

### 问题描述
之前版本存在集群初始化失败的问题，错误信息：
```
[ERR] Wrong number of arguments for specified --cluster sub command
```

### 修复内容
1. **修复集群节点列表生成逻辑**: 确保生成所有6个节点而不是只有1个节点
2. **添加节点数量验证**: 在集群创建前验证节点数量是否满足要求
3. **增强错误处理**: 提供详细的错误信息和故障排查指导
4. **添加节点状态检查**: 在集群创建前验证所有节点是否正常运行

### 验证方法
运行测试脚本验证修复效果：
```bash
./test/test_redis_cluster_fix.sh
```
