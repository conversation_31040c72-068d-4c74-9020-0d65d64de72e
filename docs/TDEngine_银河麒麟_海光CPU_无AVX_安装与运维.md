# TDengine 在银河麒麟+海光CPU（无AVX）环境下安装与运维指南

## 一、环境要求
- 操作系统：银河麒麟高级服务器版（Kylin Linux Advanced Server）
- CPU：海光（Hygon）系列，不支持AVX指令集，支持aarch64架构
- 内存：建议≥4GB
- 磁盘：建议≥20GB
- 网络：节点间互通，端口6030（服务端）、6035（管理端）等开放

## 二、依赖准备
- GCC、g++、make、cmake（建议3.10及以上）
- Git、wget、unzip
- 其他依赖：openssl-devel、libcurl-devel、zlib-devel、readline-devel

依赖安装示例（银河麒麟）：
```bash
yum install -y gcc gcc-c++ make cmake git wget unzip openssl-devel libcurl-devel zlib-devel readline-devel
```

## 三、源码获取与编译（aarch64/无AVX）
1. 克隆TDengine源码：
```bash
git clone https://github.com/taosdata/TDengine.git
cd TDengine
```
2. 创建编译目录并配置：
```bash
mkdir debug && cd debug
cmake .. -DCPUTYPE=aarch64
```
3. 编译：
```bash
make -j$(nproc)
```
4. 编译成功后，`bin/`目录下为客户端和服务端可执行文件，`lib/`为库文件。

## 四、打包与安装（可选）
如需生成安装包：
```bash
cd ..
sudo ./packaging/release.sh -c aarch64
# 安装包生成于release/目录
```

## 五、部署与配置
1. 服务端配置文件：`/etc/taos/taos.cfg`，可根据实际内存、表数量等调整参数（tables、cache、ablocks等）。
2. 启动服务端：
```bash
./bin/taosd
```
3. 启动客户端：
```bash
./bin/taos
```

## 六、启动与验证
- 启动后可通过客户端执行SQL进行功能验证。
- 示例：
```sql
create database demo tables 100 cache 16384 ablocks 4;
show databases;
```

## 七、运维建议与常见问题
- 建议根据实际内存调整tables、cache、ablocks参数，避免资源浪费。
- 定期备份数据目录和配置文件。
- 监控服务端日志（默认/var/log/taos/），及时排查异常。
- 常见问题：
  - 编译失败：检查依赖版本、cmake参数、CPU架构。
  - 服务无法启动：检查端口占用、配置文件参数。
  - 性能异常：优化内存参数，合理分配表和缓存。

## 八、参考资料
- [TDengine官方文档](https://docs.taosdata.com/)
- [TDengine ARM编译与配置](https://www.taosdata.com/tdengine-engineering/970.html)
- [TDengine GitHub源码](https://github.com/taosdata/TDengine) 