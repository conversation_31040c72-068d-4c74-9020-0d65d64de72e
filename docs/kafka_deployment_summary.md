# Kafka 部署脚本改写总结

## 概述

`scripts/auto_deploy/deploy_kafka.sh` 脚本已完全重写，从传统的 Zookeeper 模式改为 Apache Kafka Native 3.9.0 的 KRaft 模式，并采用 docker-compose 进行部署。

## 🎯 主要改进

### 1. 架构升级
- **从 Zookeeper 模式 → KRaft 模式**
- **从单容器部署 → docker-compose 部署**
- **从手动配置 → 环境变量自动配置**

### 2. 镜像更新
- **旧版本**: `repo.cafeng.top/apache/kafka-native:3.9.0`
- **新版本**: `apache/kafka-native:3.9.0` (官方镜像)

### 3. 命令修正
- **移除**: `kafka-storage.sh` (镜像中不存在)
- **新增**: 系统工具生成集群ID (uuidgen/python3/md5sum)
- **简化**: 依赖镜像自动配置，无需自定义启动命令

## 🔧 技术细节

### KRaft 配置参数
```yaml
environment:
  KAFKA_NODE_ID: 1
  KAFKA_PROCESS_ROLES: 'broker,controller'
  KAFKA_CONTROLLER_QUORUM_VOTERS: '1@host1:9093,2@host2:9093,3@host3:9093'
  KAFKA_LISTENERS: 'PLAINTEXT://:19092,CONTROLLER://:9093,PLAINTEXT_HOST://:9092'
  KAFKA_ADVERTISED_LISTENERS: 'PLAINTEXT://kafka-1:19092,PLAINTEXT_HOST://host1:9092'
  KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
  CLUSTER_ID: 'auto-generated-uuid'
```

### Docker Compose 兼容性
- **版本**: `3.3` (兼容 docker-compose 1.22.0)
- **重启策略**: `unless-stopped`
- **网络**: 默认 bridge 网络
- **卷映射**: 数据和日志目录持久化

### 集群ID生成策略
```bash
# 优先级顺序
1. uuidgen (如果可用)
2. python3 -c 'import uuid; print(str(uuid.uuid4()).replace("-", ""))'
3. 备用方法: date + random + md5sum
```

## 📋 使用方法

### 基本命令
```bash
# 单机模式部署
bash scripts/auto_deploy/deploy_kafka.sh --single-mode

# 集群模式部署
bash scripts/auto_deploy/deploy_kafka.sh --cluster-mode

# 查看帮助
bash scripts/auto_deploy/deploy_kafka.sh --help
```

### 可用选项
- `--skip-install`: 跳过环境检查和镜像加载
- `--skip-config`: 跳过配置生成
- `--skip-init`: 跳过集群验证
- `--force-reinstall`: 强制重新安装
- `--dry-run`: 仅显示将要执行的操作
- `--cluster-mode`: 集群模式部署
- `--single-mode`: 单机模式部署（默认）

## 🔍 验证和管理

### 容器管理
```bash
# 查看容器状态
cd /apps/software/docker-compose && docker-compose ps

# 查看日志
docker logs kafka-1

# 重启服务
cd /apps/software/docker-compose && docker-compose restart
```

### Kafka 操作
```bash
# 查看主题列表
docker exec kafka-1 kafka-topics.sh --list --bootstrap-server localhost:9092

# 创建主题
docker exec kafka-1 kafka-topics.sh --create --topic my-topic --partitions 3 --replication-factor 1 --bootstrap-server localhost:9092

# 生产消息
echo "test message" | docker exec -i kafka-1 kafka-console-producer.sh --topic my-topic --bootstrap-server localhost:9092

# 消费消息
docker exec kafka-1 kafka-console-consumer.sh --topic my-topic --from-beginning --bootstrap-server localhost:9092
```

## 🚀 部署流程

1. **环境检查**: Docker 和 docker-compose 可用性
2. **镜像加载**: 从 `/apps/software/docker-images/` 加载镜像
3. **配置生成**: 自动生成 docker-compose.yml 和集群配置
4. **服务部署**: 启动 Kafka 容器
5. **集群验证**: 健康检查和功能测试

## ⚠️ 重要注意事项

### 兼容性要求
- **Docker**: 已安装并运行
- **docker-compose**: 1.22.0 或更高版本
- **镜像**: `apache/kafka-native:3.9.0` 需要预先分发

### 数据迁移
- **不兼容**: Zookeeper 模式数据无法直接迁移到 KRaft
- **建议**: 重新部署并重新创建主题和数据

### 网络配置
- **端口**: 9092 (Kafka), 9093 (Controller)
- **主机名**: 确保集群节点间网络连通
- **防火墙**: 开放相应端口

## 🔄 与旧版本对比

| 特性 | 旧版本 | 新版本 (KRaft) |
|------|--------|----------------|
| 依赖组件 | Kafka + Zookeeper | 仅 Kafka |
| 部署方式 | 单容器 | docker-compose |
| 配置方式 | 手动配置文件 | 环境变量 |
| 启动时间 | 较慢 | 更快 |
| 资源占用 | 更高 | 更低 |
| 管理复杂度 | 较高 | 较低 |
| 存储格式化 | 手动执行 | 自动处理 |

## ✅ 验证结果

所有功能已通过完整测试：
- ✅ 脚本语法正确
- ✅ 移除了不存在的命令
- ✅ KRaft 环境变量完整
- ✅ 集群ID生成正确
- ✅ 端口配置合理
- ✅ 容器命名一致
- ✅ 验证逻辑完善

## 📚 相关文档

- [Kafka KRaft 部署详细文档](kafka_kraft_deployment.md)
- [Docker 镜像分发文档](docker_image_distribution.md)
- [测试脚本](../test/test_kafka_native_commands.sh)

## 🎉 总结

新的 Kafka 部署脚本完全符合 Apache Kafka 3.9.0 的最佳实践：
- 使用官方推荐的 KRaft 模式
- 采用现代化的 docker-compose 部署
- 自动化配置和验证流程
- 兼容现有的基础设施

脚本现在更加稳定、高效，并且易于维护和扩展。
