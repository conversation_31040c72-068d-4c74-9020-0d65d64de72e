# Kafka Native 镜像增强方案

## 问题背景

Apache Kafka Native 镜像 (`apache/kafka:3.9.0`) 缺少必要的 shell 脚本，导致部署脚本中的以下命令无法执行：

- `kafka-topics.sh` - 主题管理
- `kafka-console-producer.sh` - 消息生产
- `kafka-console-consumer.sh` - 消息消费  
- `kafka-broker-api-versions.sh` - 服务状态检查

## 解决方案

创建一个基于 `apache/kafka:3.9.0` 的自定义镜像，添加必要的 Kafka shell 脚本支持。

### 文件结构

```
├── Dockerfile.kafka-native-with-scripts    # 自定义 Dockerfile
├── scripts/auto_deploy/
│   ├── build_kafka_image.sh               # 镜像构建脚本
│   └── deploy_kafka.sh                    # 更新后的部署脚本
├── software-repo/
│   └── kafka-bin.zip                      # Kafka shell 脚本包
└── docs/
    └── kafka_native_with_scripts.md       # 本文档
```

## 使用步骤

### 1. 构建自定义镜像

```bash
# 进入项目根目录
cd /path/to/project

# 构建镜像
bash scripts/auto_deploy/build_kafka_image.sh

# 或者使用参数
bash scripts/auto_deploy/build_kafka_image.sh --force-rebuild
```

### 2. 验证镜像构建

```bash
# 检查镜像是否存在
docker images | grep kafka-native-with-scripts

# 测试脚本是否可用
docker run --rm apache/kafka-native-with-scripts:3.9.0 kafka-topics.sh --version
docker run --rm apache/kafka-native-with-scripts:3.9.0 kafka-console-producer.sh --version
```

### 3. 部署 Kafka 集群

```bash
# 使用更新后的部署脚本
bash scripts/auto_deploy/deploy_kafka.sh --single-mode

# 或集群模式
bash scripts/auto_deploy/deploy_kafka.sh --cluster-mode
```

## 技术细节

### Dockerfile 特性

1. **基础镜像**: `apache/kafka:3.9.0`
2. **脚本来源**: `software-repo/kafka-bin.zip`
3. **安装位置**: `/opt/kafka/bin/`
4. **PATH 更新**: 脚本可直接调用
5. **权限设置**: 所有脚本具有执行权限
6. **健康检查**: 使用 `kafka-broker-api-versions.sh`

### 构建过程

1. **构建前准备**: 在构建机器上解压 `kafka-bin.zip` 到 `software-repo/kafka-bin/`
2. **验证脚本**: 检查关键脚本是否存在
3. **Docker 构建**: 直接复制 `software-repo/kafka-bin/bin/` 到镜像
4. **设置权限**: 设置所有脚本执行权限
5. **更新环境**: 更新 PATH 环境变量
6. **创建链接**: 创建符号链接到 `/usr/local/bin/`
7. **验证安装**: 验证关键脚本安装
8. **清理文件**: 清理构建过程中的临时文件

### 镜像验证

构建过程会自动验证以下脚本：
- ✓ kafka-topics.sh
- ✓ kafka-console-producer.sh  
- ✓ kafka-console-consumer.sh
- ✓ kafka-broker-api-versions.sh

## 部署脚本更新

### 主要变更

```bash
# 原配置
KAFKA_IMAGE="apache/kafka:3.9.0"

# 新配置  
KAFKA_IMAGE="apache/kafka-native-with-scripts:3.9.0"
```

### 功能保持

- KRaft 模式配置保持不变
- docker-compose 配置保持不变
- 环境变量配置保持不变
- 验证逻辑保持不变

## 故障排除

### 构建失败

1. **基础镜像不存在**
   ```bash
   docker pull apache/kafka:3.9.0
   ```

2. **kafka-bin.zip 缺失**
   ```bash
   # 确保文件存在
   ls -la software-repo/kafka-bin.zip
   ```

3. **权限问题**
   ```bash
   # 确保 Docker 服务运行
   sudo systemctl start docker
   ```

### 运行时问题

1. **脚本不可执行**
   ```bash
   # 检查容器内脚本权限
   docker run --rm apache/kafka-native-with-scripts:3.9.0 ls -la /opt/kafka/bin/
   ```

2. **PATH 问题**
   ```bash
   # 检查环境变量
   docker run --rm apache/kafka-native-with-scripts:3.9.0 echo $PATH
   ```

### 验证命令

```bash
# 检查镜像大小
docker images apache/kafka-native-with-scripts:3.9.0

# 测试关键功能
docker run --rm apache/kafka-native-with-scripts:3.9.0 \
  kafka-topics.sh --version

# 检查脚本位置
docker run --rm apache/kafka-native-with-scripts:3.9.0 \
  find /opt/kafka/bin -name "*.sh" | head -10
```

## 维护说明

### 版本更新

当 Apache Kafka 发布新版本时：

1. 更新 `Dockerfile.kafka-native-with-scripts` 中的基础镜像版本
2. 更新 `build_kafka_image.sh` 中的镜像标签
3. 更新 `deploy_kafka.sh` 中的镜像名称
4. 重新构建和测试

### 脚本更新

当需要添加新的 Kafka 脚本时：

1. 更新 `software-repo/kafka-bin.zip`
2. 重新构建镜像
3. 验证新脚本可用性

## 最佳实践

1. **版本管理**: 为每个 Kafka 版本构建对应的自定义镜像
2. **测试验证**: 构建后务必测试关键脚本功能
3. **文档更新**: 及时更新相关文档和配置
4. **备份镜像**: 保存构建好的镜像文件以备分发

## 相关文件

- `Dockerfile.kafka-native-with-scripts` - 自定义 Dockerfile
- `scripts/auto_deploy/build_kafka_image.sh` - 构建脚本
- `scripts/auto_deploy/deploy_kafka.sh` - 部署脚本
- `software-repo/kafka-bin.zip` - Kafka 脚本包
- `docs/kafka_kraft_deployment.md` - KRaft 部署文档
