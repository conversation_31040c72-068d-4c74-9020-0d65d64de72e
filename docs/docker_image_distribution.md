# Docker镜像分发功能

## 概述

在 `scripts/auto_deploy/distribute_packages.sh` 脚本中增加了对 `scripts/auto_deploy/download_offline_packages_china.sh` 中docker镜像分发的支持。

## 功能特性

### 1. 自动分发Docker镜像
- 自动检测并分发 `/apps/software/docker-images` 目录中的 `.tar` 格式Docker镜像文件
- 分发到目标主机的 `/apps/software/docker-images` 目录
- 支持多个镜像文件的批量分发

### 2. 智能处理
- 如果源目录不存在，会跳过Docker镜像分发（不报错）
- 如果目录中没有 `.tar` 文件，会跳过分发
- 支持 `--force-overwrite` 参数强制覆盖已存在的镜像
- 支持 `--dry-run` 参数预览分发操作

### 3. 自动化脚本生成
- 在目标主机上自动创建 `load_docker_images.sh` 脚本
- 脚本包含完整的Docker镜像加载逻辑
- 自动检查Docker服务状态并启动
- 提供详细的加载进度和结果报告

### 4. 安全保护机制
- **本机IP保护**：自动检测目标IP是否为本机IP
- 如果目标是本机IP，禁止自动清空目录，防止意外删除本地文件
- 支持多种IP检测方法（hostname、ip、ifconfig命令）
- 包含常见本机地址检测（127.0.0.1、localhost、::1等）
- 提供清晰的错误消息和手动操作指导

## 实现细节

### 修改的文件
- `scripts/auto_deploy/distribute_packages.sh`

### 主要修改内容

#### 1. 软件包信息配置
```bash
declare -A PACKAGE_INFO=(
    # ... 其他包 ...
    ["docker-images"]="docker-images"  # Docker镜像目录
)
```

#### 2. 目标目录映射
```bash
declare -A TARGET_DIRS=(
    # ... 其他目录 ...
    ["docker-images"]="/apps/software/docker-images"  # Docker镜像目标路径
)
```

#### 3. 新增函数
- `distribute_docker_images()` - Docker镜像分发函数
- `create_docker_load_script()` - 创建Docker镜像加载脚本
- `is_local_ip()` - 检测目标IP是否为本机IP的安全函数

#### 4. 校验和处理
- 在校验和生成中跳过docker镜像目录（类似yum-repo和pip-repo的处理）

#### 5. 分发逻辑集成
- 在 `distribute_to_host()` 函数中添加对docker镜像的特殊处理
- 默认将docker镜像添加到所有主机的分发列表中

## 使用方法

### 1. 准备Docker镜像
将Docker镜像文件（.tar格式）放入源主机的 `/apps/software/docker-images` 目录：
```bash
# 示例：保存Docker镜像
docker save apache/kafka-native:3.9.0 > /apps/software/docker-images/apache_kafka-native_3.9.0.tar
```

### 2. 执行分发
运行分发脚本：
```bash
# 分发到所有主机
bash scripts/auto_deploy/distribute_packages.sh

# 分发到指定主机
bash scripts/auto_deploy/distribute_packages.sh --hosts "***********,***********"

# 仅分发Docker镜像
bash scripts/auto_deploy/distribute_packages.sh --packages "docker-images"

# 预览分发操作
bash scripts/auto_deploy/distribute_packages.sh --dry-run
```

### 3. 加载Docker镜像
在目标主机上运行自动生成的加载脚本：
```bash
bash /apps/software/docker-images/load_docker_images.sh
```

## 生成的加载脚本功能

自动生成的 `load_docker_images.sh` 脚本包含以下功能：

1. **环境检查**
   - 检查Docker是否安装
   - 检查Docker服务是否运行，如果未运行则自动启动

2. **镜像加载**
   - 自动发现目录中的所有 `.tar` 文件
   - 逐个加载Docker镜像
   - 显示加载进度和结果

3. **结果报告**
   - 统计加载成功和失败的镜像数量
   - 显示当前系统中的所有Docker镜像

## 兼容性

### 与现有功能的兼容性
- 完全兼容现有的软件包分发功能
- 不影响其他类型包的分发逻辑
- 保持原有的命令行参数和选项

### 错误处理
- Docker镜像分发失败不会影响其他包的分发
- 如果Docker镜像目录不存在，会记录警告但继续执行
- 部分镜像分发失败时，会继续处理其他镜像

### 安全机制
- **本机IP检测**：在执行任何清空目录操作前，自动检测目标IP是否为本机IP
- **安全阻止**：如果检测到目标是本机IP，会阻止自动清空操作并显示错误消息
- **手动指导**：提供手动清空命令，让用户明确知道操作的风险
- **多重检测**：使用多种方法检测本机IP，包括hostname、ip、ifconfig命令
- **全面覆盖**：保护所有可能的清空操作，包括Docker镜像和普通目录分发

## 测试

项目包含完整的测试脚本 `test/test_docker_distribution.sh`，验证以下功能：
- 脚本语法正确性
- 配置项完整性
- 函数存在性
- 逻辑正确性

运行测试：
```bash
bash test/test_docker_distribution.sh
```

## 注意事项

1. **权限要求**
   - 需要对目标主机的SSH访问权限
   - 目标主机需要有创建目录和文件的权限

2. **磁盘空间**
   - Docker镜像文件通常较大，确保目标主机有足够的磁盘空间
   - 建议在分发前检查可用空间

3. **网络传输**
   - Docker镜像文件传输可能需要较长时间
   - 建议在网络条件良好时进行分发

4. **Docker环境**
   - 目标主机需要安装Docker
   - 确保Docker服务可以正常启动和运行
