# NebulaGraph 在银河麒麟+海光CPU（无AVX）环境下安装与运维指南

## 一、环境要求
- 操作系统：银河麒麟高级服务器版（Kylin Linux Advanced Server）
- CPU：海光（Hygon）系列，不支持AVX指令集
- 内存：建议≥8GB
- 磁盘：建议≥50GB
- 网络：节点间互通，端口9669/9779/9780开放

## 二、依赖准备
- GCC 7.1.0及以上（建议GCC 9.x或更高）
- CMake 3.5.0及以上
- make、m4、git、wget、unzip、xz、readline-devel、ncurses-devel、zlib-devel、gettext、curl、redhat-lsb-core
- 建议提前准备好所有依赖rpm包，离线安装

依赖安装示例（银河麒麟）：
```bash
yum install -y gcc gcc-c++ make cmake m4 git wget unzip xz readline-devel ncurses-devel zlib-devel gettext curl redhat-lsb-core
```

## 三、源码获取与编译
1. 克隆NebulaGraph源码：
```bash
git clone https://github.com/vesoft-inc/nebula.git
cd nebula
```
2. 创建构建目录并配置：
```bash
mkdir build && cd build
cmake -DENABLE_TESTING=OFF -DCMAKE_BUILD_TYPE=Release ..
```
3. 编译（如内存较小建议-j1）：
```bash
make -j1
```
4. 安装到指定目录（如/apps/nebula）：
```bash
make install
```

> **注意：**
> - 若编译环境无外网，需提前准备GCC、CMake、第三方依赖包（可参考nebula-third-party项目：https://github.com/vesoft-inc/nebula-third-party）。
> - NebulaGraph源码及依赖对x86_64和aarch64均有支持，海光CPU建议优先尝试aarch64分支或源码编译。

## 四、部署与配置
1. 复制配置文件：
```bash
cd /usr/local/nebula
cp etc/nebula-storaged.conf.production etc/nebula-storaged.conf
cp etc/nebula-metad.conf.production etc/nebula-metad.conf
cp etc/nebula-graphd.conf.production etc/nebula-graphd.conf
```
2. 编辑各节点配置文件，设置meta、storage、graph节点IP、端口、数据目录等参数。
3. 创建数据与日志目录，赋权：
```bash
mkdir -p /data/nebula/{data,log}
chown -R nebula:nebula /data/nebula
```

## 五、启动与验证
1. 启动服务（分别在各节点）：
```bash
/usr/local/nebula/scripts/nebula.service start all
```
2. 验证服务状态：
```bash
/usr/local/nebula/scripts/nebula.service status all
```
3. 使用nebula-console连接验证：
```bash
nebula-console -u <user> -p <password> --address=<graphd_ip> --port=9669
```

## 六、运维建议
- 定期备份数据与配置文件
- 监控磁盘、内存、CPU资源
- 关注日志（/data/nebula/log）异常
- 建议通过systemd配置服务自启动
- 升级或迁移前务必全量备份
- 建议使用NebulaGraph官方工具（如nebula-gears）管理依赖和环境

## 七、常见问题与排查
- **依赖缺失/版本不符**：检查GCC、CMake、第三方依赖包版本
- **编译失败**：关注CMake输出和日志，必要时关闭-Werror（ENABLE_WERROR=OFF）
- **端口未开放/网络不通**：检查防火墙与安全组
- **服务无法启动**：检查配置文件、主机名/IP、数据目录权限
- **集群节点无法加入**：检查meta、storage、graph配置一致性及网络连通性

---

> 参考资料：
> - https://docs.nebula-graph.io/1.2.1/manual-EN/3.build-develop-and-administration/1.build/1.build-source-code/
> - https://github.com/vesoft-inc/nebula-third-party
> - NebulaGraph官方文档
> - 银河麒麟/海光CPU兼容性说明 