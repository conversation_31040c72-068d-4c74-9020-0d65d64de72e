# Kafka部署配置修复说明

## 问题描述

在使用apache/kafka-native:3.9.0镜像部署Kafka集群时，遇到以下错误：

```
Error creating broker listeners from 'PLAINTEXT://:19092,CONTROLLER://:9093,PLAINTEXT_HOST://:9092': 
No security protocol defined for listener PLAINTEXT_HOST
```

## 问题原因

Kafka 3.9版本要求明确定义所有监听器的安全协议映射。在原配置中，`PLAINTEXT_HOST`监听器没有在`KAFKA_LISTENER_SECURITY_PROTOCOL_MAP`中定义对应的安全协议。

## 解决方案

### 1. 添加监听器安全协议映射

在docker-compose配置中添加了`KAFKA_LISTENER_SECURITY_PROTOCOL_MAP`环境变量：

```yaml
KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT'
```

### 2. 优化集群配置

针对3节点集群，调整了复制因子配置：

```yaml
KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
```

## 修复内容

### 主要修改文件

- `scripts/auto_deploy/deploy_kafka.sh` - 主部署脚本

### 具体修改

1. **添加监听器安全协议映射**
   ```yaml
   KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT'
   ```

2. **调整集群复制因子**
   - 将复制因子从1调整为3，适配3节点集群
   - 将最小ISR从1调整为2，提高数据可靠性

3. **更新测试主题配置**
   - 测试主题的复制因子也调整为3

## 验证步骤

### 1. 运行配置测试

在部署前，可以运行配置测试脚本验证配置正确性：

```bash
# 运行完整测试（包括网络连通性）
./test/test_kafka_config.sh

# 跳过网络连通性测试
./test/test_kafka_config.sh --skip-network
```

### 2. 部署Kafka集群

```bash
# 完整部署
./scripts/auto_deploy/deploy_kafka.sh --cluster-mode

# 仅更新配置（跳过镜像加载）
./scripts/auto_deploy/deploy_kafka.sh --cluster-mode --skip-install

# 干运行模式（查看将要执行的操作）
./scripts/auto_deploy/deploy_kafka.sh --cluster-mode --dry-run
```

### 3. 验证部署结果

部署完成后，检查容器状态：

```bash
# 在任一Kafka节点上执行
cd /apps/software/docker-compose
docker-compose ps

# 查看容器日志
docker logs kafka-1 --tail 50

# 测试Kafka功能
docker exec kafka-1 kafka-topics.sh --list --bootstrap-server localhost:9092
```

## 集群配置说明

### 监听器配置

- `PLAINTEXT://:19092` - 内部集群通信监听器
- `CONTROLLER://:9093` - KRaft控制器监听器  
- `PLAINTEXT_HOST://:9092` - 外部客户端访问监听器

### 端口映射

- `9092` - 外部客户端访问端口
- `9093` - KRaft控制器端口
- `19092` - 内部集群通信端口

### 集群主机

根据`config/hosts.conf`配置：
- `***********` - kafka-01 (节点ID: 1)
- `***********` - kafka-02 (节点ID: 2)  
- `***********` - kafka-03 (节点ID: 3)

## 常见问题

### Q: 容器启动失败，提示端口被占用

**A:** 检查端口占用情况：
```bash
netstat -tlnp | grep -E ':(9092|9093)'
```

如果端口被占用，停止相关服务或修改端口配置。

### Q: 集群节点无法相互发现

**A:** 检查以下配置：
1. 确认`KAFKA_CONTROLLER_QUORUM_VOTERS`包含所有节点
2. 验证网络连通性
3. 检查防火墙设置

### Q: 主题创建失败

**A:** 确认集群状态：
```bash
docker exec kafka-1 kafka-broker-api-versions.sh --bootstrap-server localhost:9092
```

如果集群未就绪，等待更长时间或检查日志。

## 性能优化建议

### 1. 内存配置

根据实际负载调整JVM内存：
```yaml
environment:
  KAFKA_HEAP_OPTS: "-Xmx4g -Xms4g"
```

### 2. 存储优化

使用SSD存储并调整日志配置：
```yaml
environment:
  KAFKA_LOG_SEGMENT_BYTES: 1073741824
  KAFKA_LOG_RETENTION_HOURS: 168
```

### 3. 网络优化

调整网络缓冲区大小：
```yaml
environment:
  KAFKA_SOCKET_SEND_BUFFER_BYTES: 102400
  KAFKA_SOCKET_RECEIVE_BUFFER_BYTES: 102400
```

## 监控建议

### 1. 关键指标

- 消息吞吐量
- 延迟指标
- 磁盘使用率
- 网络I/O

### 2. 日志监控

定期检查Kafka日志：
```bash
# 查看错误日志
docker logs kafka-1 2>&1 | grep -i error

# 监控实时日志
docker logs kafka-1 -f
```

### 3. 健康检查

定期执行健康检查：
```bash
# 检查集群状态
docker exec kafka-1 kafka-topics.sh --list --bootstrap-server localhost:9092

# 检查消费者组
docker exec kafka-1 kafka-consumer-groups.sh --list --bootstrap-server localhost:9092
```

## 总结

通过添加`KAFKA_LISTENER_SECURITY_PROTOCOL_MAP`配置和优化集群参数，成功解决了Kafka 3.9版本的监听器配置问题。建议在生产环境部署前，先在测试环境验证配置的正确性。
