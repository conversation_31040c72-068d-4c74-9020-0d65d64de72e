# Apache Kafka 在银河麒麟+海光CPU（无AVX）环境下安装与运维指南

## 一、环境要求
- 操作系统：银河麒麟高级服务器版（Kylin Linux Advanced Server）
- CPU：海光（Hygon）系列，不支持AVX指令集
- 内存：建议≥8GB
- 磁盘：建议≥50GB
- 网络：节点间互通，端口9092（Kafka）、2181（Zookeeper）开放

## 二、依赖准备
- OpenJDK 8/11（需确保为银河麒麟/海光CPU兼容版本，避免JVM依赖AVX）
- Gradle 4.5及以上
- Git
- Zookeeper（可用Kafka自带或独立部署）

依赖安装示例（银河麒麟）：
```bash
yum install -y java-1.8.0-openjdk-devel git wget unzip
# Gradle建议源码安装或下载官方二进制包
```

## 三、JDK选择与验证
- 推荐使用银河麒麟官方源或OpenJDK社区提供的国产CPU兼容JDK。
- 验证JVM不依赖AVX：
```bash
java -version
# 可通过strings $(readlink -f $(which java)) | grep avx 检查是否有AVX指令依赖
```

## 四、Kafka源码获取与编译
1. 克隆Kafka源码：
```bash
git clone https://github.com/apache/kafka.git
cd kafka
```
2. 编译Kafka（建议指定Scala版本，避免兼容性问题）：
```bash
./gradlew -PscalaVersion=2.13 jar
```
3. 编译完成后，二进制文件位于`core/build/libs/`和`bin/`目录。

## 五、部署与配置
1. 配置Zookeeper（可用Kafka自带zookeeper.properties）：
```bash
./bin/zookeeper-server-start.sh config/zookeeper.properties
```
2. 配置Kafka（修改config/server.properties）：
- broker.id、log.dirs、zookeeper.connect、listeners等参数根据实际环境调整。

## 六、启动与验证
1. 启动Zookeeper：
```bash
./bin/zookeeper-server-start.sh config/zookeeper.properties &
```
2. 启动Kafka：
```bash
./bin/kafka-server-start.sh config/server.properties &
```
3. 创建Topic并测试：
```bash
./bin/kafka-topics.sh --create --bootstrap-server localhost:9092 --replication-factor 1 --partitions 3 --topic test
./bin/kafka-console-producer.sh --topic test --bootstrap-server localhost:9092
./bin/kafka-console-consumer.sh --topic test --bootstrap-server localhost:9092
```

## 七、运维建议与常见问题
- 建议Kafka与Zookeeper分离部署，提升稳定性。
- 定期备份配置文件与数据目录。
- 监控JVM内存、磁盘空间、网络延迟。
- 常见问题：
  - JVM启动报错：检查JDK版本与AVX依赖。
  - 端口冲突：确认9092/2181未被占用。
  - 日志报错：查看logs/目录下日志文件，定位问题。

## 八、参考资料
- [Kafka官方文档](https://kafka.apache.org/documentation/)
- [银河麒麟官方JDK下载](https://www.kylinos.cn/)
- [Kafka源码编译说明](https://github.com/apache/kafka) 