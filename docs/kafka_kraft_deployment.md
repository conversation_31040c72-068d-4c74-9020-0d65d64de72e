# Kafka KRaft 模式部署

## 概述

`scripts/auto_deploy/deploy_kafka.sh` 脚本已完全重写，采用 Apache Kafka Native 3.9.0 镜像和 KRaft 模式进行部署，无需 Zookeeper。

## 主要改进

### 1. 采用 KRaft 模式
- **移除 Zookeeper 依赖**：Kafka 3.9.0 使用 KRaft 模式，不再需要 Zookeeper
- **简化架构**：减少组件复杂性，提高可靠性
- **更好的性能**：KRaft 模式提供更好的元数据管理性能

### 2. 使用官方镜像
- **镜像**：`apache/kafka-native:3.9.0`
- **官方支持**：使用 Apache 官方维护的镜像
- **最新特性**：支持 Kafka 3.9.0 的所有新特性
- **自动配置**：镜像会根据环境变量自动配置 KRaft 模式

### 3. Docker Compose 部署
- **版本兼容**：使用 `version: '3.3'` 兼容 docker-compose 1.22.0
- **统一管理**：通过 docker-compose 统一管理容器
- **易于维护**：简化容器生命周期管理

## 配置说明

### KRaft 关键配置

```yaml
environment:
  KAFKA_NODE_ID: 1
  KAFKA_PROCESS_ROLES: 'broker,controller'
  KAFKA_CONTROLLER_QUORUM_VOTERS: '1@host1:9093,2@host2:9093,3@host3:9093'
  KAFKA_LISTENERS: 'PLAINTEXT://:19092,CONTROLLER://:9093,PLAINTEXT_HOST://:9092'
  KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
  CLUSTER_ID: 'auto-generated-cluster-id'
```

### 端口配置
- **9092**: Kafka 对外服务端口
- **9093**: KRaft Controller 端口
- **19092**: 容器内部通信端口

### 目录映射
- **数据目录**: `/apps/data/kafka` -> `/var/lib/kafka/data`
- **日志目录**: `/apps/logs/kafka` -> `/var/log/kafka`

## 使用方法

### 基本部署
```bash
# 单机模式部署
bash scripts/auto_deploy/deploy_kafka.sh --single-mode

# 集群模式部署
bash scripts/auto_deploy/deploy_kafka.sh --cluster-mode
```

### 可用选项
```bash
--skip-install      # 跳过环境检查和镜像加载
--skip-config       # 跳过配置生成
--skip-init         # 跳过集群验证
--force-reinstall   # 强制重新安装
--dry-run          # 仅显示将要执行的操作
--cluster-mode     # 集群模式部署
--single-mode      # 单机模式部署（默认）
```

### 管理命令

```bash
# 查看容器状态
cd /apps/software/docker-compose && docker-compose ps

# 查看日志
docker logs kafka-1

# 重启服务
cd /apps/software/docker-compose && docker-compose restart

# 停止服务
cd /apps/software/docker-compose && docker-compose down

# 启动服务
cd /apps/software/docker-compose && docker-compose up -d
```

### Kafka 操作命令

```bash
# 查看主题列表
docker exec kafka-1 kafka-topics.sh --list --bootstrap-server localhost:9092

# 创建主题
docker exec kafka-1 kafka-topics.sh --create --topic my-topic --partitions 3 --replication-factor 1 --bootstrap-server localhost:9092

# 查看主题详情
docker exec kafka-1 kafka-topics.sh --describe --topic my-topic --bootstrap-server localhost:9092

# 生产消息
echo "test message" | docker exec -i kafka-1 kafka-console-producer.sh --topic my-topic --bootstrap-server localhost:9092

# 消费消息
docker exec kafka-1 kafka-console-consumer.sh --topic my-topic --from-beginning --bootstrap-server localhost:9092
```

## 部署流程

### 1. 环境检查
- 检查 Docker 是否安装并运行
- 检查 docker-compose 是否可用
- 验证版本兼容性

### 2. 镜像加载
- 从 `/apps/software/docker-images/` 加载 Kafka 镜像
- 验证镜像完整性

### 3. 配置生成
- 生成 docker-compose.yml 配置文件
- 自动生成集群 ID
- 配置 quorum voters

### 4. 服务部署
- 创建必要的数据和日志目录
- 启动 Kafka 容器
- 等待服务就绪

### 5. 集群验证
- 检查容器运行状态
- 验证 Kafka API 可用性
- 创建和删除测试主题
- 测试消息生产和消费

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看容器日志
   docker logs kafka-1
   
   # 检查配置文件
   cat /apps/software/docker-compose/docker-compose.yml
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :9092
   
   # 修改端口配置
   vim /apps/software/docker-compose/docker-compose.yml
   ```

3. **存储权限问题**
   ```bash
   # 检查目录权限
   ls -la /apps/data/kafka
   
   # 修复权限
   chmod -R 755 /apps/data/kafka /apps/logs/kafka
   ```

4. **集群连接问题**
   ```bash
   # 检查网络连通性
   docker exec kafka-1 ping kafka-2
   
   # 验证 quorum voters 配置
   docker exec kafka-1 cat /opt/kafka/config/kraft/server.properties
   ```

## 与旧版本的区别

| 特性 | 旧版本 | 新版本 (KRaft) |
|------|--------|----------------|
| 依赖组件 | Kafka + Zookeeper | 仅 Kafka |
| 部署方式 | 单容器 | docker-compose |
| 配置管理 | 手动配置文件 | 环境变量 |
| 集群管理 | Zookeeper 协调 | KRaft 内置 |
| 启动时间 | 较慢 | 更快 |
| 资源占用 | 更高 | 更低 |

## 注意事项

1. **版本兼容性**：确保使用 docker-compose 1.22.0 或更高版本
2. **数据迁移**：从 Zookeeper 模式迁移到 KRaft 需要重新部署
3. **集群规模**：建议奇数个节点（1、3、5等）
4. **网络配置**：确保节点间网络连通性
5. **存储空间**：预留足够的磁盘空间用于日志存储
6. **镜像特性**：`apache/kafka-native` 镜像会自动处理 KRaft 初始化，无需手动格式化存储
7. **集群ID生成**：脚本会自动生成唯一的集群ID，确保集群一致性

## 性能优化

1. **JVM 参数调优**：可通过环境变量设置 JVM 参数
2. **网络优化**：使用高速网络连接
3. **存储优化**：使用 SSD 存储提高 I/O 性能
4. **监控配置**：启用 JMX 监控端口
