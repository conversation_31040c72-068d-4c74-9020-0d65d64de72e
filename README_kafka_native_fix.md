# Kafka Native 镜像脚本缺失解决方案

## 问题描述

Apache Kafka Native 镜像 (`apache/kafka:3.9.0`) 缺少必要的 shell 脚本，导致 `deploy_kafka.sh` 中的状态检查和管理命令无法执行：

- ❌ `kafka-topics.sh` - 主题管理
- ❌ `kafka-console-producer.sh` - 消息生产  
- ❌ `kafka-console-consumer.sh` - 消息消费
- ❌ `kafka-broker-api-versions.sh` - 服务状态检查

## 解决方案

创建一个自定义 Docker 镜像，基于 `apache/kafka:3.9.0` 添加必要的 Kafka shell 脚本。

## 快速使用

### 1. 构建自定义镜像

```bash
# 构建包含 shell 脚本的 Kafka 镜像
bash scripts/auto_deploy/build_kafka_image.sh

# 验证构建结果
docker images | grep kafka-native-with-scripts
```

### 2. 测试镜像功能

```bash
# 测试关键脚本
docker run --rm apache/kafka-native-with-scripts:3.9.0 kafka-topics.sh --version
docker run --rm apache/kafka-native-with-scripts:3.9.0 kafka-console-producer.sh --version
```

### 3. 部署 Kafka 集群

```bash
# 使用更新后的部署脚本（已自动使用新镜像）
bash scripts/auto_deploy/deploy_kafka.sh --single-mode

# 或集群模式
bash scripts/auto_deploy/deploy_kafka.sh --cluster-mode
```

## 文件说明

### 新增文件

| 文件 | 说明 |
|------|------|
| `Dockerfile.kafka-native-with-scripts` | 自定义 Dockerfile |
| `scripts/auto_deploy/build_kafka_image.sh` | 镜像构建脚本 |
| `test/test_kafka_native_dockerfile.sh` | 测试验证脚本 |
| `docs/kafka_native_with_scripts.md` | 详细技术文档 |
| `README_kafka_native_fix.md` | 本使用说明 |

### 修改文件

| 文件 | 修改内容 |
|------|----------|
| `scripts/auto_deploy/deploy_kafka.sh` | 更新镜像名称为 `apache/kafka-native-with-scripts:3.9.0` |

### 依赖文件

| 文件 | 说明 |
|------|------|
| `software-repo/kafka-bin.zip` | Kafka shell 脚本包（需要存在） |

## 技术原理

### Dockerfile 工作流程

1. **基础镜像**: 使用 `apache/kafka:3.9.0`
2. **脚本准备**: 构建前在本地解压 `software-repo/kafka-bin.zip`
3. **直接复制**: 复制 `software-repo/kafka-bin/bin/` 到 `/opt/kafka/bin/`
4. **权限设置**: 设置所有脚本执行权限
5. **环境配置**: 更新 PATH 环境变量
6. **符号链接**: 创建到 `/usr/local/bin/` 的链接
7. **健康检查**: 使用 `kafka-broker-api-versions.sh`

### 构建过程

```bash
# 1. 构建脚本自动解压 kafka-bin.zip
unzip software-repo/kafka-bin.zip -d software-repo/kafka-bin/

# 2. Dockerfile 直接复制解压后的脚本
COPY software-repo/kafka-bin/bin/ /opt/kafka/bin/

# 3. 设置执行权限
RUN chmod +x /opt/kafka/bin/*.sh

# 4. 更新环境变量
ENV PATH="/opt/kafka/bin:${PATH}"

# 5. 构建完成后自动清理临时文件
rm -rf software-repo/kafka-bin/
```

## 验证步骤

### 1. 运行测试脚本

```bash
# 完整验证
bash test/test_kafka_native_dockerfile.sh

# 包括实际构建测试（可选）
# 在提示时选择 'y' 进行实际构建
```

### 2. 手动验证

```bash
# 检查镜像是否存在
docker images apache/kafka-native-with-scripts:3.9.0

# 测试脚本可用性
docker run --rm apache/kafka-native-with-scripts:3.9.0 \
  kafka-topics.sh --version

# 检查脚本位置
docker run --rm apache/kafka-native-with-scripts:3.9.0 \
  ls -la /opt/kafka/bin/ | grep kafka-
```

### 3. 部署验证

```bash
# 部署单机模式
bash scripts/auto_deploy/deploy_kafka.sh --single-mode --dry-run

# 检查配置中的镜像名称
grep "KAFKA_IMAGE" scripts/auto_deploy/deploy_kafka.sh
```

## 故障排除

### 构建失败

```bash
# 检查基础镜像
docker pull apache/kafka:3.9.0

# 检查脚本包
ls -la software-repo/kafka-bin.zip

# 强制重新构建
bash scripts/auto_deploy/build_kafka_image.sh --force-rebuild
```

### 脚本不可用

```bash
# 检查容器内脚本
docker run --rm apache/kafka-native-with-scripts:3.9.0 \
  find /opt/kafka/bin -name "*.sh"

# 检查权限
docker run --rm apache/kafka-native-with-scripts:3.9.0 \
  ls -la /opt/kafka/bin/kafka-topics.sh
```

### 部署问题

```bash
# 检查镜像配置
grep "apache/kafka-native-with-scripts" scripts/auto_deploy/deploy_kafka.sh

# 验证镜像加载
docker load < /apps/software/docker-images/apache_kafka-native-with-scripts_3.9.0.tar.gz
```

## 优势特点

✅ **完全兼容**: 基于官方 apache/kafka:3.9.0 镜像  
✅ **功能完整**: 包含所有必要的管理脚本  
✅ **自动化**: 一键构建和部署  
✅ **可验证**: 完整的测试验证流程  
✅ **文档齐全**: 详细的使用和技术文档  
✅ **向后兼容**: 不影响现有的 KRaft 配置  

## 相关命令

```bash
# 构建镜像
bash scripts/auto_deploy/build_kafka_image.sh

# 测试验证
bash test/test_kafka_native_dockerfile.sh

# 部署 Kafka
bash scripts/auto_deploy/deploy_kafka.sh --single-mode

# 查看文档
cat docs/kafka_native_with_scripts.md
```

## 注意事项

1. **kafka-bin.zip**: 确保 `software-repo/kafka-bin.zip` 文件存在且包含必要脚本
2. **Docker 环境**: 需要 Docker 服务运行且有构建权限
3. **镜像大小**: 自定义镜像会比原镜像稍大（增加脚本文件）
4. **版本一致**: 确保脚本版本与 Kafka 版本兼容

## 支持

如有问题，请参考：
- 详细文档: `docs/kafka_native_with_scripts.md`
- 测试脚本: `test/test_kafka_native_dockerfile.sh`
- 构建日志: 运行构建脚本查看详细输出
