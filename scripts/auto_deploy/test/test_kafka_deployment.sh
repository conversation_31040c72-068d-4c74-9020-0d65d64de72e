#!/bin/bash
# Kafka部署测试脚本
# 用于验证Kafka部署修复是否有效

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_SCRIPT="$SCRIPT_DIR/../deploy_kafka.sh"

# 测试函数
test_kafka_deployment() {
    echo "=== Kafka部署测试 ==="
    echo "测试时间: $(date)"
    echo ""
    
    # 检查部署脚本是否存在
    if [[ ! -f "$DEPLOY_SCRIPT" ]]; then
        echo "❌ 部署脚本不存在: $DEPLOY_SCRIPT"
        exit 1
    fi
    
    echo "✓ 部署脚本存在"
    
    # 测试1: 检查帮助信息
    echo ""
    echo "=== 测试1: 检查帮助信息 ==="
    if "$DEPLOY_SCRIPT" --help | grep -q "force-reinstall"; then
        echo "✓ 帮助信息包含 --force-reinstall 参数"
    else
        echo "❌ 帮助信息缺少 --force-reinstall 参数"
        exit 1
    fi
    
    # 测试2: 干运行模式测试
    echo ""
    echo "=== 测试2: 干运行模式测试 ==="
    if "$DEPLOY_SCRIPT" --dry-run --force-reinstall; then
        echo "✓ 干运行模式执行成功"
    else
        echo "❌ 干运行模式执行失败"
        exit 1
    fi
    
    echo ""
    echo "=== 测试完成 ==="
    echo "所有测试通过！可以进行实际部署。"
    echo ""
    echo "建议的部署命令:"
    echo "  # 强制重新安装（清理数据）"
    echo "  $DEPLOY_SCRIPT --force-reinstall"
    echo ""
    echo "  # 检查容器状态"
    echo "  docker ps --filter name=kafka"
    echo ""
    echo "  # 查看容器日志"
    echo "  docker logs kafka-1 --tail 50"
}

# 执行测试
test_kafka_deployment
