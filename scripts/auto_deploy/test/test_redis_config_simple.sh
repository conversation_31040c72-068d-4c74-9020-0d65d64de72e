#!/bin/bash
# Redis配置简单测试脚本

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
CONFIG_DIR="$PROJECT_ROOT/config"

echo "=== Redis配置测试 ==="
echo "配置目录: $CONFIG_DIR"
echo

# 加载配置文件
echo "1. 加载hosts.conf..."
if [[ -f "$CONFIG_DIR/hosts.conf" ]]; then
    source "$CONFIG_DIR/hosts.conf"
    echo "✓ hosts.conf 加载成功"
else
    echo "✗ hosts.conf 不存在"
    exit 1
fi

echo "2. 加载redis.conf..."
if [[ -f "$CONFIG_DIR/redis.conf" ]]; then
    source "$CONFIG_DIR/redis.conf"
    echo "✓ redis.conf 加载成功"
else
    echo "✗ redis.conf 不存在"
    exit 1
fi

echo
echo "=== 配置验证 ==="

# 检查Redis主机配置
echo "3. Redis主机配置:"
echo "   Redis主机数量: ${#REDIS_HOSTS[@]}"
echo "   Redis主机列表: ${REDIS_HOSTS[*]}"

# 检查Redis端口配置
echo "4. Redis端口配置:"
if [[ -n "${REDIS_CLUSTER_PORTS:-}" ]]; then
    echo "   集群端口数量: ${#REDIS_CLUSTER_PORTS[@]}"
    echo "   集群端口列表: ${REDIS_CLUSTER_PORTS[*]}"
else
    echo "   ✗ REDIS_CLUSTER_PORTS 未定义"
fi

# 检查主机端口映射
echo "5. 主机端口映射:"
unique_hosts=($(printf '%s\n' "${REDIS_HOSTS[@]}" | sort -u))
for host in "${unique_hosts[@]}"; do
    if [[ -n "${REDIS_HOST_PORTS[$host]:-}" ]]; then
        echo "   $host: ${REDIS_HOST_PORTS[$host]}"
    else
        echo "   ✗ $host: 端口配置缺失"
    fi
done

# 检查主从关系
echo "6. 主从关系配置:"
if [[ ${#REDIS_MASTER_SLAVE_MAP[@]} -gt 0 ]]; then
    echo "   主从关系数量: ${#REDIS_MASTER_SLAVE_MAP[@]}"
    for slave_node in "${!REDIS_MASTER_SLAVE_MAP[@]}"; do
        master_node="${REDIS_MASTER_SLAVE_MAP[$slave_node]}"
        echo "   $slave_node -> $master_node"
    done
else
    echo "   ✗ REDIS_MASTER_SLAVE_MAP 未定义或为空"
fi

# 检查共享硬件配置
echo "7. 共享硬件配置:"
if [[ -n "${SHARED_NEBULA_REDIS_HARDWARE[cpu]:-}" ]]; then
    echo "   CPU: ${SHARED_NEBULA_REDIS_HARDWARE[cpu]}"
    echo "   内存: ${SHARED_NEBULA_REDIS_HARDWARE[memory]}"
    echo "   存储: ${SHARED_NEBULA_REDIS_HARDWARE[storage]}"
else
    echo "   ✗ SHARED_NEBULA_REDIS_HARDWARE 未定义"
fi

# 检查Redis服务配置
echo "8. Redis服务配置:"
if [[ -n "${REDIS_MAX_MEMORY:-}" ]]; then
    echo "   最大内存: $REDIS_MAX_MEMORY"
else
    echo "   ✗ REDIS_MAX_MEMORY 未定义"
fi

if [[ -n "${REDIS_VERSION:-}" ]]; then
    echo "   Redis版本: $REDIS_VERSION"
else
    echo "   ✗ REDIS_VERSION 未定义"
fi

echo
echo "=== 功能测试 ==="

# 测试工具函数
echo "9. 测试工具函数:"

if command -v get_redis_unique_hosts >/dev/null 2>&1; then
    unique_hosts_result=$(get_redis_unique_hosts)
    echo "   唯一主机: $unique_hosts_result"
else
    echo "   ✗ get_redis_unique_hosts 函数未定义"
fi

if command -v generate_redis_cluster_nodes >/dev/null 2>&1; then
    cluster_nodes=$(generate_redis_cluster_nodes)
    echo "   集群节点: $cluster_nodes"
else
    echo "   ✗ generate_redis_cluster_nodes 函数未定义"
fi

if command -v get_redis_master_nodes >/dev/null 2>&1; then
    master_nodes=$(get_redis_master_nodes)
    echo "   主节点: $master_nodes"
else
    echo "   ✗ get_redis_master_nodes 函数未定义"
fi

if command -v get_redis_slave_nodes >/dev/null 2>&1; then
    slave_nodes=$(get_redis_slave_nodes)
    echo "   从节点: $slave_nodes"
else
    echo "   ✗ get_redis_slave_nodes 函数未定义"
fi

echo
echo "=== 验证函数测试 ==="

# 测试验证函数
echo "10. 配置验证:"

if command -v validate_hosts_config >/dev/null 2>&1; then
    if validate_hosts_config; then
        echo "    ✓ 主机配置验证通过"
    else
        echo "    ✗ 主机配置验证失败"
    fi
else
    echo "    ✗ validate_hosts_config 函数未定义"
fi

if command -v validate_redis_config >/dev/null 2>&1; then
    if validate_redis_config; then
        echo "    ✓ Redis配置验证通过"
    else
        echo "    ✗ Redis配置验证失败"
    fi
else
    echo "    ✗ validate_redis_config 函数未定义"
fi

echo
echo "=== 测试完成 ==="
echo "Redis配置测试完成，请检查上述输出中的任何错误。"
