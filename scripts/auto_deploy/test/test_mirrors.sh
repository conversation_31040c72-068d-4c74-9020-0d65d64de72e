#!/bin/bash

# =============================================================================
# 测试国内镜像源连接性
# =============================================================================

echo "=== 测试国内镜像源连接性 ==="

# 镜像源列表
mirrors=(
    "mirrors.aliyun.com"
    "mirrors.tuna.tsinghua.edu.cn"
    "mirrors.huaweicloud.com" 
    "mirrors.tencent.com"
    "mirrors.163.com"
    "mirrors.ustc.edu.cn"
    "repo.openeuler.org"
)

echo "1. 测试基础连接性..."
for mirror in "${mirrors[@]}"; do
    echo -n "测试 $mirror ... "
    if ping -c 2 "$mirror" >/dev/null 2>&1; then
        echo "✓ 连接正常"
    else
        echo "✗ 连接失败"
    fi
done

echo ""
echo "2. 测试openEuler-20.03-LTS仓库可用性..."

# openEuler-20.03-LTS仓库URL
repo_urls=(
    "https://mirrors.aliyun.com/openeuler/20.03-LTS/OS/x86_64/"
    "https://mirrors.tuna.tsinghua.edu.cn/openeuler/20.03-LTS/OS/x86_64/"
    "https://mirrors.huaweicloud.com/openeuler/20.03-LTS/OS/x86_64/"
    "https://mirrors.tencent.com/openeuler/20.03-LTS/OS/x86_64/"
    "https://repo.openeuler.org/openEuler-20.03-LTS/OS/x86_64/"
)

for url in "${repo_urls[@]}"; do
    echo -n "测试 $(echo $url | cut -d'/' -f3) ... "
    if curl -s --head --max-time 10 "$url" | head -1 | grep -q "200 OK"; then
        echo "✓ 仓库可访问"
    else
        echo "✗ 仓库不可访问"
    fi
done

echo ""
echo "3. 测试包下载速度..."

# 测试下载一个小包的速度
test_package_urls=(
    "https://mirrors.aliyun.com/openeuler/20.03-LTS/OS/x86_64/Packages/which-2.21-12.oe1.x86_64.rpm"
    "https://mirrors.tuna.tsinghua.edu.cn/openeuler/20.03-LTS/OS/x86_64/Packages/which-2.21-12.oe1.x86_64.rpm"
    "https://mirrors.huaweicloud.com/openeuler/20.03-LTS/OS/x86_64/Packages/which-2.21-12.oe1.x86_64.rpm"
)

for url in "${test_package_urls[@]}"; do
    mirror_name=$(echo $url | cut -d'/' -f3)
    echo -n "测试下载速度 $mirror_name ... "
    
    start_time=$(date +%s.%N)
    if curl -s --max-time 30 -o /tmp/test_package.rpm "$url" 2>/dev/null; then
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "N/A")
        file_size=$(stat -c%s /tmp/test_package.rpm 2>/dev/null || echo "0")
        
        if [[ $file_size -gt 1024 ]]; then
            echo "✓ 下载成功 (${duration}s, ${file_size}字节)"
        else
            echo "✗ 下载失败 (文件太小)"
        fi
        rm -f /tmp/test_package.rpm 2>/dev/null
    else
        echo "✗ 下载失败"
    fi
done

echo ""
echo "4. 推荐使用的镜像源..."

# 根据测试结果给出建议
echo "基于连接性测试，推荐使用顺序："
echo "1. 阿里云镜像 (mirrors.aliyun.com) - 通常速度最快"
echo "2. 清华大学镜像 (mirrors.tuna.tsinghua.edu.cn) - 教育网友好"
echo "3. 华为云镜像 (mirrors.huaweicloud.com) - 企业网络友好"
echo "4. 腾讯云镜像 (mirrors.tencent.com) - 备用选择"

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果所有镜像都无法访问，请检查："
echo "1. 网络连接是否正常"
echo "2. 防火墙设置"
echo "3. DNS解析是否正常"
