#!/bin/bash

# =============================================================================
# YUM仓库包检测脚本测试工具
# 用于测试check_yum_repo_packages.sh脚本的功能
# =============================================================================

set -euo pipefail

# 脚本路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CHECK_SCRIPT="$SCRIPT_DIR/check_yum_repo_packages.sh"

# 日志函数
log_info() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*"; }
log_warn() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $*"; }
log_error() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*"; }

# 显示使用说明
show_usage() {
    cat << EOF
用法: $0 [测试类型]

测试类型:
  help        - 测试帮助功能
  syntax      - 测试脚本语法
  default     - 测试默认仓库URL
  custom      - 测试自定义仓库URL
  all         - 运行所有测试

示例:
  $0 help
  $0 syntax
  $0 default
  $0 custom
  $0 all

说明:
  - help: 测试脚本的帮助功能是否正常
  - syntax: 检查脚本语法是否正确
  - default: 使用默认仓库URL进行测试
  - custom: 使用指定的自定义仓库URL进行测试
  - all: 依次运行所有测试项目
EOF
}

# 测试脚本语法
test_syntax() {
    log_info "测试脚本语法..."
    
    if [[ ! -f "$CHECK_SCRIPT" ]]; then
        log_error "脚本文件不存在: $CHECK_SCRIPT"
        return 1
    fi
    
    if bash -n "$CHECK_SCRIPT"; then
        log_info "✓ 脚本语法检查通过"
        return 0
    else
        log_error "✗ 脚本语法检查失败"
        return 1
    fi
}

# 测试帮助功能
test_help() {
    log_info "测试帮助功能..."
    
    if [[ ! -x "$CHECK_SCRIPT" ]]; then
        log_warn "脚本没有执行权限，尝试添加..."
        chmod +x "$CHECK_SCRIPT"
    fi
    
    log_info "执行帮助命令..."
    if "$CHECK_SCRIPT" --help >/dev/null 2>&1; then
        log_info "✓ 帮助功能正常"
        return 0
    else
        log_error "✗ 帮助功能异常"
        return 1
    fi
}

# 测试默认仓库
test_default_repo() {
    log_info "测试默认仓库URL..."
    
    local default_url="http://***********/YUM/KYLIN10SP3_x86_64/base/Packages/"
    log_info "默认仓库URL: $default_url"
    
    # 首先测试网络连接
    local repo_host=$(echo "$default_url" | cut -d'/' -f3)
    log_info "测试网络连接到: $repo_host"
    
    if ping -c 2 "$repo_host" >/dev/null 2>&1; then
        log_info "✓ 网络连接正常"
        
        # 测试HTTP访问
        if curl -s --head --max-time 10 "$default_url" | head -1 | grep -q "200 OK"; then
            log_info "✓ HTTP访问正常"
            
            # 运行脚本（但不等待完成，只测试启动）
            log_info "启动脚本测试（5秒后终止）..."
            timeout 5s "$CHECK_SCRIPT" || true
            log_info "✓ 脚本启动正常"
            return 0
        else
            log_warn "✗ HTTP访问失败，但这可能是正常的（仓库可能不存在）"
            return 0
        fi
    else
        log_warn "✗ 网络连接失败，但这可能是正常的（仓库主机可能不可达）"
        return 0
    fi
}

# 测试自定义仓库
test_custom_repo() {
    log_info "测试自定义仓库URL..."
    
    # 使用一个公开的测试URL
    local test_urls=(
        "http://mirrors.aliyun.com/centos/7/os/x86_64/Packages/"
        "http://mirrors.tuna.tsinghua.edu.cn/centos/7/os/x86_64/Packages/"
    )
    
    for url in "${test_urls[@]}"; do
        log_info "测试仓库: $url"
        
        local repo_host=$(echo "$url" | cut -d'/' -f3)
        if ping -c 2 "$repo_host" >/dev/null 2>&1; then
            log_info "✓ 网络连接正常: $repo_host"
            
            if curl -s --head --max-time 10 "$url" | head -1 | grep -q "200 OK"; then
                log_info "✓ HTTP访问正常: $url"
                
                # 运行脚本测试（5秒后终止）
                log_info "启动脚本测试（5秒后终止）..."
                timeout 5s "$CHECK_SCRIPT" "$url" || true
                log_info "✓ 自定义仓库脚本启动正常"
                return 0
            else
                log_warn "HTTP访问失败: $url"
            fi
        else
            log_warn "网络连接失败: $repo_host"
        fi
    done
    
    log_warn "所有测试仓库都无法访问，但这可能是网络环境问题"
    return 0
}

# 测试脚本功能完整性
test_functionality() {
    log_info "测试脚本功能完整性..."
    
    # 检查脚本中的关键函数
    local required_functions=(
        "show_usage"
        "create_output_dir"
        "test_repo_connectivity"
        "get_available_packages"
        "define_required_packages"
        "compare_packages"
        "generate_report"
        "main"
    )
    
    local missing_functions=()
    for func in "${required_functions[@]}"; do
        if ! grep -q "^$func()" "$CHECK_SCRIPT"; then
            missing_functions+=("$func")
        fi
    done
    
    if [[ ${#missing_functions[@]} -eq 0 ]]; then
        log_info "✓ 所有必需函数都存在"
        return 0
    else
        log_error "✗ 缺失函数: ${missing_functions[*]}"
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    log_info "开始运行所有测试..."
    
    local tests=(
        "test_syntax"
        "test_functionality"
        "test_help"
        "test_default_repo"
        "test_custom_repo"
    )
    
    local passed=0
    local failed=0
    
    for test in "${tests[@]}"; do
        log_info "运行测试: $test"
        if $test; then
            ((passed++))
            log_info "✓ $test 通过"
        else
            ((failed++))
            log_error "✗ $test 失败"
        fi
        echo ""
    done
    
    log_info "测试结果汇总:"
    log_info "  通过: $passed"
    log_info "  失败: $failed"
    log_info "  总计: $((passed + failed))"
    
    if [[ $failed -eq 0 ]]; then
        log_info "✓ 所有测试都通过了！"
        return 0
    else
        log_warn "有 $failed 个测试失败"
        return 1
    fi
}

# 主函数
main() {
    case "${1:-all}" in
        "help"|"-h"|"--help")
            show_usage
            exit 0
            ;;
        "syntax")
            test_syntax
            ;;
        "functionality")
            test_functionality
            ;;
        "help-test")
            test_help
            ;;
        "default")
            test_default_repo
            ;;
        "custom")
            test_custom_repo
            ;;
        "all")
            run_all_tests
            ;;
        *)
            log_error "未知的测试类型: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
