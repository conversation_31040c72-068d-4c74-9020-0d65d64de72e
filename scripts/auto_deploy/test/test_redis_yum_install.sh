#!/bin/bash
# Redis YUM安装测试脚本
# 测试Redis优先使用本地仓库安装的功能

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
DEPLOY_SCRIPT="$PROJECT_ROOT/deploy_redis.sh"

# 测试配置
TEST_LOG_FILE="/tmp/redis_yum_install_test.log"

# 日志函数
log_test() {
    echo "[TEST $(date '+%H:%M:%S')] $*" | tee -a "$TEST_LOG_FILE"
}

log_success() {
    echo "[SUCCESS $(date '+%H:%M:%S')] ✓ $*" | tee -a "$TEST_LOG_FILE"
}

log_error() {
    echo "[ERROR $(date '+%H:%M:%S')] ✗ $*" | tee -a "$TEST_LOG_FILE"
}

# 清理测试日志
cleanup_test_log() {
    > "$TEST_LOG_FILE"
}

# 测试仓库恢复功能
test_repo_restore_function() {
    log_test "测试仓库恢复功能..."

    # 检查脚本中是否包含仓库恢复函数
    if grep -q "restore_original_yum_repos" "$DEPLOY_SCRIPT"; then
        log_success "仓库恢复函数存在"
    else
        log_error "仓库恢复函数不存在"
        return 1
    fi

    # 检查是否查找backup目录
    if grep -q "/etc/yum.repos.d/backup" "$DEPLOY_SCRIPT"; then
        log_success "包含backup目录查找逻辑"
    else
        log_error "缺少backup目录查找逻辑"
        return 1
    fi

    # 检查是否查找backup前缀目录
    if grep -q "backup.*net.repo" "$DEPLOY_SCRIPT"; then
        log_success "包含backup前缀目录查找逻辑"
    else
        log_error "缺少backup前缀目录查找逻辑"
        return 1
    fi

    # 检查是否包含local.repo禁用逻辑
    if grep -q "local.repo.disabled" "$DEPLOY_SCRIPT"; then
        log_success "包含local.repo禁用逻辑"
    else
        log_error "缺少local.repo禁用逻辑"
        return 1
    fi

    # 检查是否包含local.repo恢复逻辑
    if grep -q "重新启用local.repo" "$DEPLOY_SCRIPT"; then
        log_success "包含local.repo恢复逻辑"
    else
        log_error "缺少local.repo恢复逻辑"
        return 1
    fi
}

# 测试Redis版本检查
test_redis_version_check() {
    log_test "测试Redis版本检查..."
    
    # 检查是否包含7.2.7版本检查
    if grep -q "7.2.7" "$DEPLOY_SCRIPT"; then
        log_success "包含Redis 7.2.7版本检查"
    else
        log_error "缺少Redis 7.2.7版本检查"
        return 1
    fi
    
    # 检查版本选择逻辑
    if grep -q "7\.2\." "$DEPLOY_SCRIPT"; then
        log_success "包含7.2.x系列版本选择逻辑"
    else
        log_error "缺少7.2.x系列版本选择逻辑"
        return 1
    fi
}

# 测试YUM安装逻辑
test_yum_install_logic() {
    log_test "测试YUM安装逻辑..."
    
    # 检查是否优先使用YUM安装
    if grep -q "install_redis_from_local_repo" "$DEPLOY_SCRIPT"; then
        log_success "包含本地仓库安装函数"
    else
        log_error "缺少本地仓库安装函数"
        return 1
    fi
    
    # 检查是否有YUM安装失败的处理
    if grep -q "放弃安装" "$DEPLOY_SCRIPT"; then
        log_success "包含YUM安装失败处理"
    else
        log_error "缺少YUM安装失败处理"
        return 1
    fi
    
    # 检查是否有YUM安装检测
    if grep -q "yum install.*redis" "$DEPLOY_SCRIPT"; then
        log_success "包含YUM安装命令"
    else
        log_error "缺少YUM安装命令"
        return 1
    fi
}

# 测试环境兼容性
test_environment_compatibility() {
    log_test "测试环境兼容性..."
    
    # 检查是否创建符号链接
    if grep -q "ln -sf.*redis-server" "$DEPLOY_SCRIPT"; then
        log_success "包含符号链接创建逻辑"
    else
        log_error "缺少符号链接创建逻辑"
        return 1
    fi
    
    # 检查是否兼容YUM和二进制安装
    if grep -q "YUM安装的Redis" "$DEPLOY_SCRIPT"; then
        log_success "包含YUM安装检测逻辑"
    else
        log_error "缺少YUM安装检测逻辑"
        return 1
    fi
}

# 测试local.repo处理逻辑
test_local_repo_handling() {
    log_test "测试local.repo处理逻辑..."

    # 检查是否包含local.repo检测
    if grep -q "/etc/yum.repos.d/local.repo" "$DEPLOY_SCRIPT"; then
        log_success "包含local.repo检测逻辑"
    else
        log_error "缺少local.repo检测逻辑"
        return 1
    fi

    # 检查是否包含local.repo禁用
    if grep -q "local.repo.disabled" "$DEPLOY_SCRIPT"; then
        log_success "包含local.repo禁用逻辑"
    else
        log_error "缺少local.repo禁用逻辑"
        return 1
    fi

    # 检查是否包含local.repo备份
    if grep -q "cp.*local.repo.*local.repo.disabled" "$DEPLOY_SCRIPT"; then
        log_success "包含local.repo备份逻辑"
    else
        log_error "缺少local.repo备份逻辑"
        return 1
    fi

    # 检查卸载时是否恢复local.repo
    if grep -q "恢复local.repo" "$DEPLOY_SCRIPT"; then
        log_success "包含卸载时local.repo恢复逻辑"
    else
        log_error "缺少卸载时local.repo恢复逻辑"
        return 1
    fi
}

# 测试卸载功能
test_uninstall_yum_support() {
    log_test "测试卸载YUM支持..."

    # 检查是否包含YUM卸载
    if grep -q "yum remove.*redis" "$DEPLOY_SCRIPT"; then
        log_success "包含YUM卸载命令"
    else
        log_error "缺少YUM卸载命令"
        return 1
    fi

    # 检查是否检测YUM安装
    if grep -q "rpm -q redis" "$DEPLOY_SCRIPT"; then
        log_success "包含YUM安装检测"
    else
        log_error "缺少YUM安装检测"
        return 1
    fi
}

# 测试dry-run模式
test_dry_run_mode() {
    log_test "测试dry-run模式..."

    # 检查脚本中是否包含dry-run参数处理
    if grep -q "DRY_RUN.*true" "$DEPLOY_SCRIPT"; then
        log_success "包含dry-run模式处理"
    else
        log_error "缺少dry-run模式处理"
        return 1
    fi

    # 检查帮助信息中是否包含dry-run选项
    if grep -A 20 "echo.*选项:" "$DEPLOY_SCRIPT" | grep -q "dry-run"; then
        log_success "帮助信息包含dry-run选项"
    else
        log_error "帮助信息缺少dry-run选项"
        return 1
    fi
}

# 测试配置文件语法
test_config_syntax() {
    log_test "测试配置文件语法..."
    
    # 检查脚本语法
    if bash -n "$DEPLOY_SCRIPT"; then
        log_success "脚本语法检查通过"
    else
        log_error "脚本语法检查失败"
        return 1
    fi
    
    # 检查配置文件语法
    local config_files=(
        "$PROJECT_ROOT/config/hosts.conf"
        "$PROJECT_ROOT/config/redis.conf"
    )
    
    for config_file in "${config_files[@]}"; do
        if [[ -f "$config_file" ]]; then
            if bash -n "$config_file"; then
                log_success "配置文件语法正确: $(basename "$config_file")"
            else
                log_error "配置文件语法错误: $(basename "$config_file")"
                return 1
            fi
        else
            log_error "配置文件不存在: $(basename "$config_file")"
            return 1
        fi
    done
}

# 测试函数完整性
test_function_completeness() {
    log_test "测试函数完整性..."
    
    # 检查必要的函数是否存在
    local required_functions=(
        "restore_original_yum_repos"
        "install_redis_from_local_repo"
        "install_redis_dependencies"
        "install_redis_binary"
    )
    
    for func in "${required_functions[@]}"; do
        if grep -q "^$func()" "$DEPLOY_SCRIPT"; then
            log_success "函数存在: $func"
        else
            log_error "函数缺失: $func"
            return 1
        fi
    done
}

# 主测试函数
main() {
    echo "Redis YUM安装功能测试开始..."
    echo "测试时间: $(date)"
    echo "测试脚本: $DEPLOY_SCRIPT"
    echo "测试日志: $TEST_LOG_FILE"
    echo

    # 清理测试日志
    cleanup_test_log

    # 检查部署脚本是否存在
    if [[ ! -f "$DEPLOY_SCRIPT" ]]; then
        log_error "部署脚本不存在: $DEPLOY_SCRIPT"
        exit 1
    fi

    # 运行测试
    local tests_passed=0
    local tests_total=0

    # 测试列表
    local test_functions=(
        "test_config_syntax"
        "test_function_completeness"
        "test_repo_restore_function"
        "test_redis_version_check"
        "test_yum_install_logic"
        "test_environment_compatibility"
        "test_local_repo_handling"
        "test_uninstall_yum_support"
        "test_dry_run_mode"
    )

    for test_func in "${test_functions[@]}"; do
        tests_total=$((tests_total + 1))
        echo
        if $test_func; then
            tests_passed=$((tests_passed + 1))
        fi
    done

    echo
    echo "=== 测试总结 ==="
    echo "总测试数: $tests_total"
    echo "通过测试: $tests_passed"
    echo "失败测试: $((tests_total - tests_passed))"
    echo "测试日志: $TEST_LOG_FILE"

    if [[ $tests_passed -eq $tests_total ]]; then
        echo "✓ 所有测试通过！Redis YUM安装功能实现正确。"
        echo
        echo "=== 功能摘要 ==="
        echo "1. ✓ 优先使用本地虚拟机镜像仓库安装Redis 7.2.7"
        echo "2. ✓ 自动恢复被覆盖的原始仓库配置"
        echo "3. ✓ 支持YUM和二进制安装的兼容性"
        echo "4. ✓ 完整的卸载功能（包括YUM卸载）"
        echo "5. ✓ 如果本地仓库无Redis则放弃安装"
        exit 0
    else
        echo "✗ 部分测试失败，请检查实现。"
        exit 1
    fi
}

# 执行测试
main "$@"
