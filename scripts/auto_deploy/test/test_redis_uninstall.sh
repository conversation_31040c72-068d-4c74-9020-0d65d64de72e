#!/bin/bash
# Redis卸载功能测试脚本
# 用于验证Redis部署脚本的卸载功能

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
DEPLOY_SCRIPT="$PROJECT_ROOT/deploy_redis.sh"

# 测试配置
TEST_LOG_FILE="/tmp/redis_uninstall_test.log"

# 日志函数
log_test() {
    echo "[TEST $(date '+%H:%M:%S')] $*" | tee -a "$TEST_LOG_FILE"
}

log_success() {
    echo "[SUCCESS $(date '+%H:%M:%S')] ✓ $*" | tee -a "$TEST_LOG_FILE"
}

log_error() {
    echo "[ERROR $(date '+%H:%M:%S')] ✗ $*" | tee -a "$TEST_LOG_FILE"
}

# 清理测试日志
cleanup_test_log() {
    > "$TEST_LOG_FILE"
}

# 测试帮助信息
test_help_message() {
    log_test "测试帮助信息显示..."
    
    if "$DEPLOY_SCRIPT" --help | grep -q "uninstall"; then
        log_success "帮助信息包含卸载选项"
    else
        log_error "帮助信息缺少卸载选项"
        return 1
    fi
}

# 测试dry-run模式的卸载
test_dry_run_uninstall() {
    log_test "测试dry-run模式的卸载..."
    
    if "$DEPLOY_SCRIPT" --uninstall --dry-run 2>&1 | grep -q "DRY RUN"; then
        log_success "dry-run模式卸载功能正常"
    else
        log_error "dry-run模式卸载功能异常"
        return 1
    fi
}

# 测试参数解析
test_parameter_parsing() {
    log_test "测试参数解析..."
    
    # 测试--uninstall参数
    if "$DEPLOY_SCRIPT" --uninstall --dry-run 2>&1 | grep -q "开始卸载Redis集群"; then
        log_success "--uninstall参数解析正确"
    else
        log_error "--uninstall参数解析失败"
        return 1
    fi
    
    # 测试--no-backup参数
    if "$DEPLOY_SCRIPT" --uninstall --no-backup --dry-run 2>&1 | grep -q "数据备份: 否"; then
        log_success "--no-backup参数解析正确"
    else
        log_error "--no-backup参数解析失败"
        return 1
    fi
}

# 测试配置文件加载
test_config_loading() {
    log_test "测试配置文件加载..."
    
    # 检查是否能正确加载Redis配置
    if "$DEPLOY_SCRIPT" --uninstall --dry-run 2>&1 | grep -q "Redis集群主机"; then
        log_success "配置文件加载正常"
    else
        log_error "配置文件加载失败"
        return 1
    fi
}

# 测试卸载函数存在性
test_uninstall_functions() {
    log_test "测试卸载函数存在性..."
    
    # 检查脚本中是否包含必要的卸载函数
    local required_functions=(
        "backup_redis_data"
        "stop_redis_services"
        "remove_redis_services"
        "remove_redis_files"
        "remove_redis_user"
        "cleanup_redis_environment"
        "uninstall_redis"
    )
    
    for func in "${required_functions[@]}"; do
        if grep -q "^$func()" "$DEPLOY_SCRIPT"; then
            log_success "函数 $func 存在"
        else
            log_error "函数 $func 不存在"
            return 1
        fi
    done
}

# 测试脚本语法
test_script_syntax() {
    log_test "测试脚本语法..."
    
    if bash -n "$DEPLOY_SCRIPT"; then
        log_success "脚本语法检查通过"
    else
        log_error "脚本语法检查失败"
        return 1
    fi
}

# 测试锁机制
test_lock_mechanism() {
    log_test "测试锁机制..."
    
    if "$DEPLOY_SCRIPT" --uninstall --dry-run 2>&1 | grep -q "获取锁成功"; then
        log_success "锁机制工作正常"
    else
        log_error "锁机制异常"
        return 1
    fi
}

# 主测试函数
main() {
    echo "Redis卸载功能测试开始..."
    echo "测试时间: $(date)"
    echo "测试脚本: $DEPLOY_SCRIPT"
    echo "测试日志: $TEST_LOG_FILE"
    echo

    # 清理测试日志
    cleanup_test_log

    # 检查部署脚本是否存在
    if [[ ! -f "$DEPLOY_SCRIPT" ]]; then
        log_error "部署脚本不存在: $DEPLOY_SCRIPT"
        exit 1
    fi

    # 运行测试
    local tests_passed=0
    local tests_total=0

    # 测试列表
    local test_functions=(
        "test_script_syntax"
        "test_help_message"
        "test_parameter_parsing"
        "test_uninstall_functions"
        "test_config_loading"
        "test_dry_run_uninstall"
        "test_lock_mechanism"
    )

    for test_func in "${test_functions[@]}"; do
        tests_total=$((tests_total + 1))
        echo
        if $test_func; then
            tests_passed=$((tests_passed + 1))
        fi
    done

    echo
    echo "=== 测试总结 ==="
    echo "总测试数: $tests_total"
    echo "通过测试: $tests_passed"
    echo "失败测试: $((tests_total - tests_passed))"
    echo "测试日志: $TEST_LOG_FILE"

    if [[ $tests_passed -eq $tests_total ]]; then
        echo "✓ 所有测试通过！Redis卸载功能实现正确。"
        exit 0
    else
        echo "✗ 部分测试失败，请检查实现。"
        exit 1
    fi
}

# 执行测试
main "$@"
