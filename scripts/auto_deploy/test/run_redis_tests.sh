#!/bin/bash
# Redis测试套件主入口脚本
# 运行所有Redis相关的测试

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 测试结果
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志配置
TEST_LOG_DIR="/tmp/redis_tests"
SUMMARY_LOG="$TEST_LOG_DIR/test_summary.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$SUMMARY_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$SUMMARY_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$SUMMARY_LOG"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*" | tee -a "$SUMMARY_LOG"
}

# 初始化测试环境
init_test_environment() {
    # 创建测试日志目录
    mkdir -p "$TEST_LOG_DIR"

    # 清理之前的日志
    > "$SUMMARY_LOG"

    log_info "初始化Redis测试环境..."
    log_info "测试日志目录: $TEST_LOG_DIR"
    log_info "测试开始时间: $(date)"
    log_info "项目根目录: $PROJECT_ROOT"
}

# 运行单个测试脚本
run_test() {
    local test_script="$1"
    local test_name="$2"
    
    log_info "运行测试: $test_name"
    log_info "测试脚本: $test_script"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 检查测试脚本是否存在
    if [[ ! -f "$test_script" ]]; then
        log_error "测试脚本不存在: $test_script"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 确保脚本可执行
    chmod +x "$test_script"
    
    # 运行测试
    local test_log="$TEST_LOG_DIR/$(basename "$test_script" .sh).log"
    local start_time=$(date +%s)
    
    if "$test_script" > "$test_log" 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_success "✓ $test_name 通过 (${duration}s)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_error "✗ $test_name 失败 (${duration}s)"
        log_error "详细日志: $test_log"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        
        # 显示失败的测试的最后几行日志
        echo "--- 失败测试的最后10行日志 ---"
        tail -10 "$test_log" 2>/dev/null || echo "无法读取日志文件"
        echo "--- 日志结束 ---"
        
        return 1
    fi
}

# 运行所有Redis测试
run_all_redis_tests() {
    log_info "开始运行Redis测试套件..."
    
    # 测试列表
    local tests=(
        "$SCRIPT_DIR/test_redis_cluster_config.sh:Redis集群配置测试"
        "$SCRIPT_DIR/test_redis_yum_install.sh:Redis YUM安装测试"
        "$SCRIPT_DIR/test_redis_uninstall.sh:Redis卸载功能测试"
    )
    
    # 运行每个测试
    for test_entry in "${tests[@]}"; do
        IFS=':' read -r test_script test_name <<< "$test_entry"
        echo
        echo "========================================"
        run_test "$test_script" "$test_name"
        echo "========================================"
    done
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    echo
    echo "========================================"
    echo "           Redis测试套件报告"
    echo "========================================"
    echo "测试时间: $(date)"
    echo "项目路径: $PROJECT_ROOT"
    echo "测试日志: $TEST_LOG_DIR"
    echo
    echo "测试统计:"
    echo "  总测试数: $TOTAL_TESTS"
    echo "  通过测试: $PASSED_TESTS"
    echo "  失败测试: $FAILED_TESTS"
    echo "  成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    echo
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "🎉 所有Redis测试通过！"
        echo
        echo "Redis部署功能验证完成："
        echo "  ✓ 集群配置正确"
        echo "  ✓ YUM安装功能正常"
        echo "  ✓ 卸载功能完整"
        echo "  ✓ 与NebulaGraph节点共享配置正确"
        echo
    else
        log_error "❌ 部分Redis测试失败"
        echo
        echo "失败的测试需要检查："
        echo "  - 检查配置文件语法"
        echo "  - 验证函数实现完整性"
        echo "  - 确认脚本逻辑正确性"
        echo
        echo "详细日志位置: $TEST_LOG_DIR"
    fi
    
    echo "========================================"
}

# 清理测试环境
cleanup_test_environment() {
    log_info "清理测试环境..."
    
    # 保留日志文件，不删除
    log_info "测试日志已保留在: $TEST_LOG_DIR"
}

# 显示帮助信息
show_help() {
    echo "Redis测试套件"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -v, --verbose       详细输出模式"
    echo "  -c, --config-only   仅运行配置测试"
    echo "  -i, --install-only  仅运行安装测试"
    echo "  -u, --uninstall-only 仅运行卸载测试"
    echo
    echo "示例:"
    echo "  $0                  # 运行所有测试"
    echo "  $0 -c               # 仅运行配置测试"
    echo "  $0 -v               # 详细输出模式"
}

# 主函数
main() {
    local config_only=false
    local install_only=false
    local uninstall_only=false
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -c|--config-only)
                config_only=true
                shift
                ;;
            -i|--install-only)
                install_only=true
                shift
                ;;
            -u|--uninstall-only)
                uninstall_only=true
                shift
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 初始化测试环境
    init_test_environment
    
    # 根据参数运行相应的测试
    if [[ "$config_only" == "true" ]]; then
        run_test "$SCRIPT_DIR/test_redis_cluster_config.sh" "Redis集群配置测试"
    elif [[ "$install_only" == "true" ]]; then
        run_test "$SCRIPT_DIR/test_redis_yum_install.sh" "Redis YUM安装测试"
    elif [[ "$uninstall_only" == "true" ]]; then
        run_test "$SCRIPT_DIR/test_redis_uninstall.sh" "Redis卸载功能测试"
    else
        # 运行所有测试
        run_all_redis_tests
    fi
    
    # 生成测试报告
    generate_test_report
    
    # 清理测试环境
    cleanup_test_environment
    
    # 返回适当的退出码
    if [[ $FAILED_TESTS -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
