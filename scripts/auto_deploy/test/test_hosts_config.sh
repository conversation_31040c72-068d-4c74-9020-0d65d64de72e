#!/bin/bash
# 测试主机配置文件

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PARENT_DIR="$(dirname "$SCRIPT_DIR")"

# 测试主机配置加载
test_hosts_config_loading() {
    echo "=== 测试主机配置加载 ==="
    
    local hosts_config="$PARENT_DIR/config/hosts.conf"
    if [[ ! -f "$hosts_config" ]]; then
        echo "✗ 主机配置文件不存在: $hosts_config"
        return 1
    fi
    
    # 加载配置文件
    if source "$hosts_config"; then
        echo "✓ 主机配置文件加载成功"
    else
        echo "✗ 主机配置文件加载失败"
        return 1
    fi
}

# 测试主机数组
test_host_arrays() {
    echo "=== 测试主机数组 ==="
    
    # 检查各个服务的主机数组
    echo "MongoDB主机数量: ${#MONGODB_HOSTS[@]}"
    echo "Redis主机数量: ${#REDIS_HOSTS[@]}"
    echo "Kafka主机数量: ${#KAFKA_HOSTS[@]}"
    echo "TDEngine主机数量: ${#TDENGINE_HOSTS[@]}"
    echo "NebulaGraph主机数量: ${#NEBULA_HOSTS[@]}"
    
    # 验证主机数量
    if [[ ${#MONGODB_HOSTS[@]} -eq 3 ]]; then
        echo "✓ MongoDB主机数量正确"
    else
        echo "✗ MongoDB主机数量错误，期望3个，实际${#MONGODB_HOSTS[@]}个"
        return 1
    fi
    
    if [[ ${#REDIS_HOSTS[@]} -eq 6 ]]; then
        echo "✓ Redis主机数量正确"
    else
        echo "✗ Redis主机数量错误，期望6个，实际${#REDIS_HOSTS[@]}个"
        return 1
    fi
    
    if [[ ${#KAFKA_HOSTS[@]} -eq 3 ]]; then
        echo "✓ Kafka主机数量正确"
    else
        echo "✗ Kafka主机数量错误，期望3个，实际${#KAFKA_HOSTS[@]}个"
        return 1
    fi
}

# 测试工具函数
test_utility_functions() {
    echo "=== 测试工具函数 ==="
    
    # 测试get_all_hosts函数
    local all_hosts
    mapfile -t all_hosts < <(get_all_hosts)
    echo "总主机数量: ${#all_hosts[@]}"
    
    if [[ ${#all_hosts[@]} -gt 0 ]]; then
        echo "✓ get_all_hosts函数工作正常"
    else
        echo "✗ get_all_hosts函数返回空结果"
        return 1
    fi
    
    # 测试get_hostname_by_ip函数
    local test_ip="***********"
    local hostname
    hostname=$(get_hostname_by_ip "$test_ip")
    
    if [[ -n "$hostname" ]]; then
        echo "✓ get_hostname_by_ip函数工作正常: $test_ip -> $hostname"
    else
        echo "✗ get_hostname_by_ip函数返回空结果"
        return 1
    fi
    
    # 测试get_service_by_ip函数
    local services
    mapfile -t services < <(get_service_by_ip "$test_ip")
    
    if [[ ${#services[@]} -gt 0 ]]; then
        echo "✓ get_service_by_ip函数工作正常: $test_ip -> ${services[*]}"
    else
        echo "✗ get_service_by_ip函数返回空结果"
        return 1
    fi
}

# 测试配置验证
test_config_validation() {
    echo "=== 测试配置验证 ==="
    
    if validate_hosts_config; then
        echo "✓ 主机配置验证通过"
    else
        echo "✗ 主机配置验证失败"
        return 1
    fi
}

# 测试配置摘要
test_config_summary() {
    echo "=== 测试配置摘要 ==="
    
    print_hosts_summary
    echo "✓ 配置摘要显示正常"
}

# 验证达梦数据库和监控配置已移除
test_removed_configs() {
    echo "=== 验证已移除的配置 ==="
    
    # 检查是否还有达梦数据库相关的变量
    if declare -p | grep -i dameng >/dev/null 2>&1; then
        echo "✗ 仍然存在达梦数据库相关配置"
        return 1
    else
        echo "✓ 达梦数据库配置已完全移除"
    fi
    
    # 检查是否还有监控相关的变量
    if declare -p | grep -i monitor >/dev/null 2>&1; then
        echo "✗ 仍然存在监控相关配置"
        return 1
    else
        echo "✓ 监控配置已完全移除"
    fi
    
    # 检查是否还有负载均衡器相关的变量
    if declare -p | grep -i "LB_" >/dev/null 2>&1; then
        echo "✗ 仍然存在负载均衡器相关配置"
        return 1
    else
        echo "✓ 负载均衡器配置已完全移除"
    fi
}

# 主测试函数
main() {
    echo "开始测试主机配置文件..."
    echo "测试时间: $(date)"
    echo
    
    # 加载配置文件
    local hosts_config="$PARENT_DIR/config/hosts.conf"
    source "$hosts_config"
    
    # 运行各项测试
    test_hosts_config_loading
    echo
    
    test_host_arrays
    echo
    
    test_utility_functions
    echo
    
    test_config_validation
    echo
    
    test_config_summary
    echo
    
    test_removed_configs
    echo
    
    echo "=== 测试总结 ==="
    echo "✓ 主机配置文件加载正常"
    echo "✓ 主机数组配置正确"
    echo "✓ 工具函数工作正常"
    echo "✓ 配置验证通过"
    echo "✓ 达梦数据库和监控配置已完全移除"
    echo
    echo "当前配置包含的服务："
    echo "- MongoDB集群 (3节点)"
    echo "- Redis集群 (6节点)"
    echo "- Kafka集群 (3节点)"
    echo "- TDEngine集群 (3节点)"
    echo "- NebulaGraph集群 (3节点)"
    echo
    echo "已移除的服务："
    echo "- 达梦数据库集群"
    echo "- 监控服务"
    echo "- 负载均衡器"
}

# 执行测试
main "$@"
