#!/bin/bash
# Redis集群配置测试脚本
# 验证Redis与NebulaGraph共享节点的配置

set -eo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
CONFIG_DIR="$PROJECT_ROOT/config"

# 测试日志
TEST_LOG_FILE="/tmp/redis_cluster_config_test.log"

# 日志函数
log_test() {
    echo "[TEST $(date '+%H:%M:%S')] $*" | tee -a "$TEST_LOG_FILE"
}

log_success() {
    echo "[SUCCESS $(date '+%H:%M:%S')] ✓ $*" | tee -a "$TEST_LOG_FILE"
}

log_error() {
    echo "[ERROR $(date '+%H:%M:%S')] ✗ $*" | tee -a "$TEST_LOG_FILE"
}

# 清理测试日志
cleanup_test_log() {
    > "$TEST_LOG_FILE"
}

# 加载配置文件
load_configs() {
    log_test "加载配置文件..."

    if [[ -f "$CONFIG_DIR/hosts.conf" ]]; then
        source "$CONFIG_DIR/hosts.conf"
        log_success "hosts.conf 加载成功"

        # 验证关键变量是否加载
        if [[ ${#REDIS_CLUSTER_PORTS[@]} -gt 0 ]]; then
            log_success "REDIS_CLUSTER_PORTS 加载成功: ${REDIS_CLUSTER_PORTS[*]}"
        else
            log_error "REDIS_CLUSTER_PORTS 加载失败"
        fi

        if [[ ${#REDIS_MASTER_SLAVE_MAP[@]} -gt 0 ]]; then
            log_success "REDIS_MASTER_SLAVE_MAP 加载成功"
        else
            log_error "REDIS_MASTER_SLAVE_MAP 加载失败"
        fi
    else
        log_error "hosts.conf 不存在"
        return 1
    fi

    if [[ -f "$CONFIG_DIR/redis.conf" ]]; then
        source "$CONFIG_DIR/redis.conf"
        log_success "redis.conf 加载成功"
    else
        log_error "redis.conf 不存在"
        return 1
    fi
}

# 测试Redis与NebulaGraph主机共享
test_host_sharing() {
    log_test "测试Redis与NebulaGraph主机共享..."
    
    # 获取Redis唯一主机
    local redis_unique_hosts=($(printf '%s\n' "${REDIS_HOSTS[@]}" | sort -u))
    local nebula_hosts_sorted=($(printf '%s\n' "${NEBULA_HOSTS[@]}" | sort))
    local redis_hosts_sorted=($(printf '%s\n' "${redis_unique_hosts[@]}" | sort))
    
    log_test "Redis唯一主机: ${redis_hosts_sorted[*]}"
    log_test "NebulaGraph主机: ${nebula_hosts_sorted[*]}"
    
    if [[ "${redis_hosts_sorted[*]}" == "${nebula_hosts_sorted[*]}" ]]; then
        log_success "Redis与NebulaGraph主机列表匹配"
    else
        log_error "Redis与NebulaGraph主机列表不匹配"
        return 1
    fi
}

# 测试端口配置
test_port_configuration() {
    log_test "测试Redis端口配置..."
    
    # 检查REDIS_HOST_PORTS配置
    local unique_hosts=($(printf '%s\n' "${REDIS_HOSTS[@]}" | sort -u))
    
    for host in "${unique_hosts[@]}"; do
        if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
            log_success "主机 $host 端口配置: ${REDIS_HOST_PORTS[$host]}"
        else
            log_error "主机 $host 缺少端口配置"
            return 1
        fi
    done
    
    # 检查集群端口总数（应该在hosts.conf中定义）
    if [[ -n "${REDIS_CLUSTER_PORTS:-}" && ${#REDIS_CLUSTER_PORTS[@]} -gt 0 ]]; then
        if [[ ${#REDIS_CLUSTER_PORTS[@]} -eq 6 ]]; then
            log_success "Redis集群端口数量正确: ${REDIS_CLUSTER_PORTS[*]}"
        else
            log_error "Redis集群端口数量错误，期望6个，实际${#REDIS_CLUSTER_PORTS[@]}个"
            log_error "REDIS_CLUSTER_PORTS内容: ${REDIS_CLUSTER_PORTS[*]}"
            return 1
        fi
    else
        log_error "REDIS_CLUSTER_PORTS变量未定义或为空（应该在hosts.conf中定义）"
        return 1
    fi
}

# 测试集群拓扑
test_cluster_topology() {
    log_test "测试Redis集群拓扑..."
    
    # 验证主从关系配置
    local master_ports=("7001" "7002" "7003")
    local slave_ports=("7004" "7005" "7006")
    
    log_test "主节点端口: ${master_ports[*]}"
    log_test "从节点端口: ${slave_ports[*]}"
    
    # 检查主从映射（应该在hosts.conf中定义）
    if [[ -n "${REDIS_MASTER_SLAVE_MAP:-}" ]]; then
        local map_count=${#REDIS_MASTER_SLAVE_MAP[@]}
        if [[ $map_count -gt 0 ]]; then
            for slave_node in "${!REDIS_MASTER_SLAVE_MAP[@]}"; do
                local master_node="${REDIS_MASTER_SLAVE_MAP[$slave_node]}"
                log_success "从节点 $slave_node -> 主节点 $master_node"
            done

            if [[ $map_count -eq 3 ]]; then
                log_success "主从关系配置正确"
            else
                log_error "主从关系配置数量错误，期望3个，实际${map_count}个"
                return 1
            fi
        else
            log_error "REDIS_MASTER_SLAVE_MAP变量为空"
            return 1
        fi
    else
        log_error "REDIS_MASTER_SLAVE_MAP变量未定义（应该在hosts.conf中定义）"
        return 1
    fi
}

# 测试资源分配
test_resource_allocation() {
    log_test "测试资源分配..."
    
    # 检查共享节点硬件配置（应该在hosts.conf中定义）
    if [[ -n "${SHARED_NEBULA_REDIS_HARDWARE[cpu]:-}" ]]; then
        log_success "共享节点CPU配置: ${SHARED_NEBULA_REDIS_HARDWARE[cpu]}"
    else
        log_error "缺少共享节点CPU配置（应该在hosts.conf中定义）"
        return 1
    fi

    if [[ -n "${SHARED_NEBULA_REDIS_HARDWARE[memory]:-}" ]]; then
        log_success "共享节点内存配置: ${SHARED_NEBULA_REDIS_HARDWARE[memory]}"
    else
        log_error "缺少共享节点内存配置（应该在hosts.conf中定义）"
        return 1
    fi
    
    # 检查Redis内存限制（应该在redis.conf中定义）
    if [[ -n "${REDIS_MAX_MEMORY:-}" ]]; then
        if [[ "$REDIS_MAX_MEMORY" == "4gb" ]]; then
            log_success "Redis内存限制配置正确: $REDIS_MAX_MEMORY"
        else
            log_success "Redis内存限制配置: $REDIS_MAX_MEMORY"
        fi
    else
        log_error "Redis内存限制未配置（应该在redis.conf中定义）"
        return 1
    fi
}

# 测试配置验证函数
test_config_validation() {
    log_test "测试配置验证函数..."
    
    if validate_hosts_config; then
        log_success "主机配置验证通过"
    else
        log_error "主机配置验证失败"
        return 1
    fi
    
    if validate_redis_config; then
        log_success "Redis配置验证通过"
    else
        log_error "Redis配置验证失败"
        return 1
    fi
}

# 测试集群节点生成
test_cluster_node_generation() {
    log_test "测试集群节点生成..."
    
    local cluster_nodes=$(generate_redis_cluster_nodes)
    log_test "生成的集群节点: $cluster_nodes"
    
    # 检查节点数量
    local node_count=$(echo "$cluster_nodes" | wc -w)
    if [[ $node_count -eq 6 ]]; then
        log_success "集群节点数量正确: $node_count"
    else
        log_error "集群节点数量错误，期望6个，实际${node_count}个"
        return 1
    fi
    
    # 检查主节点
    local master_nodes=$(get_redis_master_nodes)
    log_test "主节点: $master_nodes"
    
    local master_count=$(echo "$master_nodes" | wc -w)
    if [[ $master_count -eq 3 ]]; then
        log_success "主节点数量正确: $master_count"
    else
        log_error "主节点数量错误，期望3个，实际${master_count}个"
        return 1
    fi
    
    # 检查从节点
    local slave_nodes=$(get_redis_slave_nodes)
    log_test "从节点: $slave_nodes"
    
    local slave_count=$(echo "$slave_nodes" | wc -w)
    if [[ $slave_count -eq 3 ]]; then
        log_success "从节点数量正确: $slave_count"
    else
        log_error "从节点数量错误，期望3个，实际${slave_count}个"
        return 1
    fi
}

# 主测试函数
main() {
    echo "Redis集群配置测试开始..."
    echo "测试时间: $(date)"
    echo "配置目录: $CONFIG_DIR"
    echo "测试日志: $TEST_LOG_FILE"
    echo

    # 清理测试日志
    cleanup_test_log

    # 首先加载配置文件
    if ! load_configs; then
        echo "配置加载失败，退出测试"
        exit 1
    fi

    # 运行测试
    local tests_passed=0
    local tests_total=0

    # 测试列表（移除load_configs，因为已经在上面执行了）
    local test_functions=(
        "test_host_sharing"
        "test_port_configuration"
        "test_cluster_topology"
        "test_resource_allocation"
        "test_config_validation"
        "test_cluster_node_generation"
    )

    for test_func in "${test_functions[@]}"; do
        tests_total=$((tests_total + 1))
        echo
        if $test_func; then
            tests_passed=$((tests_passed + 1))
        fi
    done

    echo
    echo "=== 测试总结 ==="
    echo "总测试数: $tests_total"
    echo "通过测试: $tests_passed"
    echo "失败测试: $((tests_total - tests_passed))"
    echo "测试日志: $TEST_LOG_FILE"

    if [[ $tests_passed -eq $tests_total ]]; then
        echo "✓ 所有测试通过！Redis集群配置正确。"
        echo
        echo "=== 配置摘要 ==="
        print_hosts_summary
        exit 0
    else
        echo "✗ 部分测试失败，请检查配置。"
        exit 1
    fi
}

# 执行测试
main "$@"
