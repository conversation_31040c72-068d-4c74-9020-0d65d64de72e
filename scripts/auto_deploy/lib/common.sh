#!/bin/bash
# 通用函数库 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，提供可重用的函数和错误处理机制

# =============================================================================
# 全局变量
# =============================================================================

# 生成随机密码函数
generate_password() {
    local length=${1:-16}
    local complexity=${2:-true}

    if [[ "$complexity" == "true" ]]; then
        # 生成复杂密码（包含字母、数字、特殊字符）
        openssl rand -base64 $((length * 3 / 4)) | tr -d "=+/" | cut -c1-${length}
    else
        # 生成简单密码（仅十六进制）
        openssl rand -hex $((length / 2))
    fi
}

# 脚本基础路径
SCRIPT_BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CONFIG_DIR="$SCRIPT_BASE_DIR/config"
LIB_DIR="$SCRIPT_BASE_DIR/lib"
TEMPLATES_DIR="$SCRIPT_BASE_DIR/templates"
IS_HYGON_CPU=false

# 加载配置文件（安全模式）
if [[ -f "$CONFIG_DIR/global.conf" ]]; then
    source "$CONFIG_DIR/global.conf" 2>/dev/null || {
        echo "警告: 全局配置文件加载失败，使用默认配置" >&2
        # 设置默认配置
        export DEPLOY_USER="${DEPLOY_USER:-root}"
        export LOG_DIR="${LOG_DIR:-/var/log/deploy}"
        export OFFLINE_MODE="${OFFLINE_MODE:-false}"
        export LOCAL_YUM_REPO="${LOCAL_YUM_REPO:-/apps/offline-prep/yum-repo}"
    }
else
    echo "警告: 全局配置文件不存在，使用默认配置" >&2
    # 设置默认配置
    export DEPLOY_USER="${DEPLOY_USER:-root}"
    export LOG_DIR="${LOG_DIR:-/var/log/deploy}"
    export OFFLINE_MODE="${OFFLINE_MODE:-false}"
    export LOCAL_YUM_REPO="${LOCAL_YUM_REPO:-/apps/offline-prep/yum-repo}"
fi

if [[ -f "$CONFIG_DIR/hosts.conf" ]]; then
    source "$CONFIG_DIR/hosts.conf" 2>/dev/null || {
        echo "警告: 主机配置文件加载失败" >&2
    }
else
    echo "警告: 主机配置文件不存在" >&2
fi

# =============================================================================
# 错误处理和陷阱
# =============================================================================

# 错误处理函数
handle_error() {
    local exit_code=$?
    local line_number=$1
    log_error "脚本在第 $line_number 行发生错误，退出码: $exit_code"
    cleanup_on_exit
    exit $exit_code
}

# 清理函数
cleanup_on_exit() {
    log_info "执行清理操作..."
    
    # 清理临时文件
    if [[ -n "$TEMP_DIR" && -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
        log_info "清理临时目录: $TEMP_DIR"
    fi
    
    # 清理锁文件
    if [[ -n "$LOCK_FILE" && -f "$LOCK_FILE" ]]; then
        rm -f "$LOCK_FILE"
        log_info "清理锁文件: $LOCK_FILE"
    fi
}

# 设置错误陷阱（仅在非source模式下）
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 只有直接执行脚本时才设置陷阱
    trap 'handle_error $LINENO' ERR
    trap 'cleanup_on_exit' EXIT
fi

# =============================================================================
# 日志函数
# =============================================================================

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 日志函数
log_info() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [INFO] $*" | tee -a "$LOG_DIR/deploy.log"
}

log_error() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [ERROR] $*" | tee -a "$LOG_DIR/deploy.log" >&2
}

log_warn() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [WARN] $*" | tee -a "$LOG_DIR/deploy.log"
}

log_debug() {
    if [[ "$LOG_LEVEL" == "DEBUG" ]]; then
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        echo "[$timestamp] [DEBUG] $*" | tee -a "$LOG_DIR/deploy.log"
    fi
}

# 进度条函数
show_progress() {
    local current=$1
    local total=$2
    local message=${3:-"处理中"}
    local width=50
    local percentage=$((current * 100 / total))
    local completed=$((current * width / total))
    
    printf "\r%s [" "$message"
    printf "%*s" $completed | tr ' ' '='
    printf "%*s" $((width - completed)) | tr ' ' '-'
    printf "] %d%% (%d/%d)" $percentage $current $total
    
    if [[ $current -eq $total ]]; then
        echo
    fi
}

# =============================================================================
# 系统检查函数
# =============================================================================

# 检查操作系统
check_os() {
    local os_info
    if [[ -f /etc/os-release ]]; then
        os_info=$(grep "^NAME=" /etc/os-release | cut -d'"' -f2)
        log_info "检测到操作系统: $os_info"
        
        if [[ "$os_info" =~ "Kylin" ]]; then
            log_info "银河麒麟操作系统检测通过"
            return 0
        else
            log_warn "当前操作系统不是银河麒麟，可能存在兼容性问题"
            return 0
        fi
    else
        log_error "无法检测操作系统信息"
        return 1
    fi
}

# 检查CPU架构
check_cpu() {
    local cpu_arch=$(uname -m)
    local cpu_info=$(lscpu | grep "Model name" | cut -d':' -f2 | xargs)

    log_info "CPU架构: $cpu_arch"
    log_info "CPU信息: $cpu_info"

    if [[ "$cpu_arch" == "x86_64" ]]; then
        log_info "CPU架构检测通过"

        # 检查是否为海光CPU
        if [[ "$cpu_info" =~ "Hygon" ]]; then
            log_info "检测到海光CPU"
            export IS_HYGON_CPU=true
        else
            log_warn "未检测到海光CPU，请确认兼容性"
            export IS_HYGON_CPU=false
        fi

        # 记录CPU信息用于二进制包选择
        export CPU_INFO="$cpu_info"
        log_info "使用二进制包部署，无需检查编译相关特性"

        return 0
    else
        log_error "不支持的CPU架构: $cpu_arch"
        return 1
    fi
}

# 检查内存
check_memory() {
    local total_mem=$(free -g | awk '/^Mem:/{print $2}')
    local available_mem=$(free -g | awk '/^Mem:/{print $7}')
    
    log_info "总内存: ${total_mem}GB"
    log_info "可用内存: ${available_mem}GB"
    
    if [[ $total_mem -ge 8 ]]; then
        log_info "内存检测通过"
        return 0
    else
        log_error "内存不足，至少需要8GB"
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    local required_space=${1:-50}  # GB
    local mount_point=${2:-"/"}
    
    local available_space=$(df -BG "$mount_point" | awk 'NR==2 {print $4}' | sed 's/G//')
    
    log_info "挂载点 $mount_point 可用空间: ${available_space}GB"
    
    if [[ $available_space -ge $required_space ]]; then
        log_info "磁盘空间检测通过"
        return 0
    else
        log_error "磁盘空间不足，需要至少 ${required_space}GB，当前可用 ${available_space}GB"
        return 1
    fi
}

# 检查网络连通性
check_network_connectivity() {
    local test_hosts=("*******" "***************")
    
    for host in "${test_hosts[@]}"; do
        if ping -c 3 -W 5 "$host" >/dev/null 2>&1; then
            log_info "网络连通性检测通过 ($host)"
            return 0
        fi
    done
    
    log_error "网络连通性检测失败"
    return 1
}

# =============================================================================
# SSH和远程执行函数
# =============================================================================

# 检查SSH连接
check_ssh_connection() {
    local host=$1
    local user=${2:-$DEPLOY_USER}
    local timeout=${3:-$SSH_TIMEOUT}
    
    if ssh -o ConnectTimeout=$timeout -o BatchMode=yes "$user@$host" "echo 'SSH连接测试成功'" >/dev/null 2>&1; then
        log_debug "SSH连接到 $host 成功"
        return 0
    else
        log_error "SSH连接到 $host 失败"
        return 1
    fi
}

# 远程执行命令
remote_execute() {
    local host=$1
    local command=$2
    local user=${3:-$DEPLOY_USER}
    local timeout=${4:-$DEPLOY_TIMEOUT}
    log_info "在 $host 上执行命令: $command"
    ssh -o ConnectTimeout=$timeout -o BatchMode=yes "$user@$host" "$command"
    local exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        log_debug "命令在 $host 上执行成功"
    else
        log_debug "命令在 $host 上执行失败，退出码: $exit_code"
    fi
    
    return $exit_code
}

# 远程复制文件
remote_copy() {
    local source=$1
    local destination=$2
    local host=$3
    local user=${4:-$DEPLOY_USER}
    
    log_debug "复制文件到 $host: $source -> $destination"
    
    scp -o ConnectTimeout=$SSH_TIMEOUT -o BatchMode=yes "$source" "$user@$host:$destination"
    local exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        log_debug "文件复制到 $host 成功"
    else
        log_error "文件复制到 $host 失败，退出码: $exit_code"
    fi
    
    return $exit_code
}

remote_copy_dir() {
    local source=$1
    local destination=$2
    local host=$3
    local user=${4:-$DEPLOY_USER}

    log_debug "复制文件到 $host: $source -> $destination"

    scp -o ConnectTimeout=$SSH_TIMEOUT -o BatchMode=yes "$source"/* "$user@$host:$destination"
    local exit_code=$?

    if [[ $exit_code -eq 0 ]]; then
        log_debug "目录复制到 $host 成功"
    else
        log_error "目录复制到 $host 失败，退出码: $exit_code"
    fi

    return 0
}

# 批量远程执行
batch_remote_execute() {
    local hosts_array=("$@")
    local command="${hosts_array[-1]}"
    unset hosts_array[-1]
    
    local success_count=0
    local total_count=${#hosts_array[@]}
    
    for host in "${hosts_array[@]}"; do
        if remote_execute "$host" "$command"; then
             success_count=$((success_count + 1))
        fi
        show_progress $((success_count + total_count - ${#hosts_array[@]})) $total_count "执行命令"
    done
    
    log_info "批量执行完成: $success_count/$total_count 成功"
    
    if [[ $success_count -eq $total_count ]]; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# 服务管理函数
# =============================================================================

# 检查服务状态
check_service_status() {
    local host=$1
    local service=$2
    
    if remote_execute "$host" "systemctl is-active $service" >/dev/null 2>&1; then
        log_debug "服务 $service 在 $host 上运行正常"
        return 0
    else
        log_debug "服务 $service 在 $host 上未运行"
        return 1
    fi
}

# 启动服务
start_service() {
    local host=$1
    local service=$2
    
    log_info "在 $host 上启动服务 $service"
    
    if remote_execute "$host" "systemctl start $service"; then
        log_info "服务 $service 在 $host 上启动成功"
        return 0
    else
        log_error "服务 $service 在 $host 上启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    local host=$1
    local service=$2
    
    log_info "在 $host 上停止服务 $service"
    
    if remote_execute "$host" "systemctl stop $service"; then
        log_info "服务 $service 在 $host 上停止成功"
        return 0
    else
        log_error "服务 $service 在 $host 上停止失败"
        return 1
    fi
}

# 重启服务
restart_service() {
    local host=$1
    local service=$2
    
    log_info "在 $host 上重启服务 $service"
    
    if remote_execute "$host" "systemctl restart $service"; then
        log_info "服务 $service 在 $host 上重启成功"
        return 0
    else
        log_error "服务 $service 在 $host 上重启失败"
        return 1
    fi
}

# =============================================================================
# 包管理函数 - 专注于二进制包部署
# =============================================================================

# 安装系统包（运行时依赖）
install_system_packages() {
    local host=$1
    shift
    local packages=("$@")

    log_info "在 $host 上安装系统运行时依赖包: ${packages[*]}"

    # 根据部署模式选择安装方式
    if [[ "$OFFLINE_MODE" == "true" ]]; then
        install_packages_offline "$host" "${packages[@]}"
    else
        install_packages_online "$host" "${packages[@]}"
    fi
}

# 在线安装系统包（运行时依赖）
install_packages_online() {
    local host=$1
    shift
    local packages=("$@")

    log_info "使用在线模式安装运行时依赖包"

    local install_cmd="yum install -y ${packages[*]}"

    if remote_execute "$host" "$install_cmd"; then
        log_info "运行时依赖包安装成功"
        return 0
    else
        log_error "运行时依赖包安装失败"
        return 1
    fi
}

# 离线安装系统包（运行时依赖）
install_packages_offline() {
    local host=$1
    shift
    local packages=("$@")

    log_info "使用离线模式安装运行时依赖包"

    # 首先尝试配置离线YUM仓库
    if setup_offline_yum_repo "$host"; then
        log_info "尝试使用YUM仓库安装..."

        # 尝试使用YUM安装
        local install_cmd="yum install -y --disablerepo=* --enablerepo=local-* ${packages[*]}"
        if remote_execute "$host" "$install_cmd" 2>/dev/null; then
            log_info "离线YUM仓库安装成功"
            return 0
        else
            log_warn "YUM仓库安装失败，尝试直接RPM安装..."
        fi
    else
        log_warn "离线YUM仓库配置失败，尝试直接RPM安装..."
    fi

    # 备选方案：直接使用RPM文件安装
    log_info "使用直接RPM安装方式..."
    install_packages_direct_rpm "$host" "${packages[@]}"
}

# 直接使用RPM文件安装包
install_packages_direct_rpm() {
    local host=$1
    shift
    local packages=("$@")

    log_info "使用直接RPM安装方式安装: ${packages[*]}"

    # 检查本地RPM目录
    local rpm_dirs=(
        "$LOCAL_YUM_REPO"
        "$LOCAL_YUM_REPO/Packages"
        "/apps/offline-prep/yum-repo"
        "/apps/offline-prep/yum-repo/Packages"
    )

    local rpm_dir=""
    for dir in "${rpm_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            local rpm_count=$(find "$dir" -name "*.rpm" 2>/dev/null | wc -l)
            if [[ $rpm_count -gt 0 ]]; then
                rpm_dir="$dir"
                log_info "找到RPM目录: $rpm_dir (包含 $rpm_count 个RPM文件)"
                break
            fi
        fi
    done

    if [[ -z "$rpm_dir" ]]; then
        log_error "未找到包含RPM文件的目录"
        return 1
    fi

    # 安装每个包
    local successful_packages=()
    local failed_packages=()

    for package in "${packages[@]}"; do
        log_info "查找并安装包: $package"

        # 检查包是否已安装
        if remote_execute "$host" "rpm -q $package" >/dev/null 2>&1; then
            log_info "✓ 包已安装: $package"
            successful_packages+=("$package")
            continue
        fi

        # 查找匹配的RPM文件
        local rpm_files
        rpm_files=$(find "$rpm_dir" -name "*${package}*.rpm" 2>/dev/null)

        if [[ -z "$rpm_files" ]]; then
            log_warn "✗ 未找到包 $package 的RPM文件"
            failed_packages+=("$package")
            continue
        fi

        # 选择最合适的RPM文件（优先选择精确匹配）
        local selected_rpm=""
        while IFS= read -r rpm_file; do
            local rpm_name=$(basename "$rpm_file" .rpm)
            if [[ "$rpm_name" =~ ^${package}-[0-9] ]]; then
                selected_rpm="$rpm_file"
                break
            fi
        done <<< "$rpm_files"

        # 如果没有精确匹配，选择第一个
        if [[ -z "$selected_rpm" ]]; then
            selected_rpm=$(echo "$rpm_files" | head -1)
        fi

        log_info "选择RPM文件: $(basename "$selected_rpm")"

        # 复制RPM文件到目标主机并安装
        if remote_copy "$selected_rpm" "/tmp/$(basename "$selected_rpm")" "$host"; then
            if remote_execute "$host" "rpm -Uvh /tmp/$(basename "$selected_rpm") --nodeps --force" 2>/dev/null; then
                log_info "✓ 直接RPM安装成功: $package"
                successful_packages+=("$package")
                # 清理临时文件
                remote_execute "$host" "rm -f /tmp/$(basename "$selected_rpm")"
            else
                log_warn "✗ 直接RPM安装失败: $package"
                failed_packages+=("$package")
            fi
        else
            log_warn "✗ 复制RPM文件失败: $package"
            failed_packages+=("$package")
        fi
    done

    # 输出安装结果
    log_info "直接RPM安装结果:"
    log_info "  成功: ${#successful_packages[@]} 个包"
    log_info "  失败: ${#failed_packages[@]} 个包"

    if [[ ${#successful_packages[@]} -gt 0 ]]; then
        log_info "  成功的包: ${successful_packages[*]}"
    fi

    if [[ ${#failed_packages[@]} -gt 0 ]]; then
        log_warn "  失败的包: ${failed_packages[*]}"
    fi

    # 如果至少有一半的包安装成功，认为安装成功
    if [[ ${#successful_packages[@]} -ge $((${#packages[@]} / 2)) ]]; then
        return 0
    else
        return 1
    fi
}

# 检查包是否已安装
check_package_installed() {
    local host=$1
    local package=$2

    if remote_execute "$host" "rpm -q $package" >/dev/null 2>&1; then
        log_debug "包 $package 在 $host 上已安装"
        return 0
    else
        log_debug "包 $package 在 $host 上未安装"
        return 1
    fi
}

# =============================================================================
# 二进制包部署函数
# =============================================================================

# 部署二进制包
deploy_binary_package() {
    local host=$1
    local package_name=$2
    local package_file=$3
    local install_dir=$4
    local service_user=${5:-"root"}
    local service_group=${6:-"root"}

    log_info "在 $host 上部署二进制包: $package_name"

    if [[ ! -f "$package_file" ]]; then
        log_error "二进制包文件不存在: $package_file"
        return 1
    fi

    # 复制二进制包到目标主机
    if ! remote_copy "$package_file" "/tmp/$(basename $package_file)" "$host"; then
        log_error "复制二进制包到 $host 失败"
        return 1
    fi

    # 解压并安装二进制包
    remote_execute "$host" "
        # 创建安装目录
        mkdir -p '$install_dir'

        # 备份现有安装（如果存在）
        if [[ -d '$install_dir' && -n \"\$(ls -A '$install_dir' 2>/dev/null)\" ]]; then
            backup_dir='$install_dir.backup.\$(date +%s)'
            echo \"备份现有安装到: \$backup_dir\"
            mv '$install_dir' \"\$backup_dir\"
            mkdir -p '$install_dir'
        fi

        # 解压二进制包
        cd /tmp
        package_file=\$(basename '$package_file')

        if [[ \"\$package_file\" =~ \\.tar\\.gz$ || \"\$package_file\" =~ \\.tgz$ ]]; then
            tar -zxf \"\$package_file\" -C '$install_dir' --strip-components=1
        elif [[ \"\$package_file\" =~ \\.tar\\.bz2$ ]]; then
            tar -jxf \"\$package_file\" -C '$install_dir' --strip-components=1
        elif [[ \"\$package_file\" =~ \\.tar\\.xz$ ]]; then
            tar -Jxf \"\$package_file\" -C '$install_dir' --strip-components=1
        elif [[ \"\$package_file\" =~ \\.zip$ ]]; then
            unzip -q \"\$package_file\" -d '$install_dir'
        else
            echo '错误: 不支持的包格式'
            exit 1
        fi

        # 设置权限
        chown -R '$service_user:$service_group' '$install_dir'

        # 清理临时文件
        rm -f \"\$package_file\"

        echo '二进制包部署完成'
    "

    if [[ $? -eq 0 ]]; then
        log_info "二进制包 $package_name 部署成功: $host"
        return 0
    else
        log_error "二进制包 $package_name 部署失败: $host"
        return 1
    fi
}

# 验证二进制包部署
verify_binary_deployment() {
    local host=$1
    local install_dir=$2
    local expected_files=("${@:3}")

    log_info "验证 $host 上的二进制包部署..."

    local verification_failed=false

    # 检查安装目录
    if ! remote_execute "$host" "test -d '$install_dir'"; then
        log_error "安装目录不存在: $install_dir"
        return 1
    fi

    # 检查关键文件
    for file in "${expected_files[@]}"; do
        if ! remote_execute "$host" "test -f '$install_dir/$file'"; then
            log_error "关键文件不存在: $install_dir/$file"
            verification_failed=true
        else
            log_debug "✓ 关键文件存在: $install_dir/$file"
        fi
    done

    if [[ "$verification_failed" == "true" ]]; then
        log_error "二进制包部署验证失败: $host"
        return 1
    else
        log_info "✓ 二进制包部署验证成功: $host"
        return 0
    fi
}

# 混合安装策略：优先使用自定义YUM仓库，然后使用离线包
install_packages_hybrid() {
    local host=$1
    shift
    local packages=("$@")

    log_info "使用离线包在 $host 上安装依赖包: ${packages[*]}"

    # 检查哪些包还没有安装
    local missing_packages=()
    for package in "${packages[@]}"; do
        if ! check_package_installed "$host" "$package"; then
            missing_packages+=("$package")
        fi
    done

    if [[ ${#missing_packages[@]} -gt 0 ]]; then
        log_info "步骤2: 使用离线包安装缺失的依赖: ${missing_packages[*]}"
        install_missing_packages_offline "$host" "${missing_packages[@]}"
    else
        log_info "✓ 所有依赖包都已成功安装"
    fi
}



# 使用离线包安装缺失的依赖
install_missing_packages_offline() {
    local host=$1
    shift
    local packages=("$@")

    log_info "使用离线包安装缺失的依赖: ${packages[*]}"

    # 直接使用RPM安装方式（更可靠）
    install_packages_direct_rpm "$host" "${packages[@]}"
}

# =============================================================================
# 配置文件处理函数
# =============================================================================

# 从模板生成配置文件
generate_config_from_template() {
    local template_file=$1
    local output_file=$2
    local -A variables=()
    
    # 读取变量参数
    shift 2
    while [[ $# -gt 0 ]]; do
        local key=$1
        local value=$2
        variables["$key"]="$value"
        shift 2
    done
    
    log_debug "从模板生成配置文件: $template_file -> $output_file"
    
    if [[ ! -f "$template_file" ]]; then
        log_error "模板文件不存在: $template_file"
        return 1
    fi
    
    # 复制模板文件
    cp "$template_file" "$output_file"
    
    # 替换变量
    for key in "${!variables[@]}"; do
        local value="${variables[$key]}"
        sed -i "s|{{$key}}|$value|g" "$output_file"
    done
    
    log_debug "配置文件生成完成: $output_file"
    return 0
}

# 备份配置文件
backup_config_file() {
    local host=$1
    local config_file=$2
    local backup_suffix=$(date +%Y%m%d_%H%M%S)
    
    log_info "备份 $host 上的配置文件: $config_file"
    
    if remote_execute "$host" "cp $config_file ${config_file}.backup_${backup_suffix}"; then
        log_info "配置文件备份成功"
        return 0
    else
        log_error "配置文件备份失败"
        return 1
    fi
}

# =============================================================================
# 健康检查函数
# =============================================================================

# 检查端口是否开放
check_port_open() {
    local host=$1
    local port=$2
    local timeout=${3:-5}
    
    if timeout $timeout bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        log_debug "端口 $port 在 $host 上开放"
        return 0
    else
        log_debug "端口 $port 在 $host 上未开放"
        return 1
    fi
}

# 等待端口开放
wait_for_port() {
    local host=$1
    local port=$2
    local timeout=${3:-60}
    local interval=${4:-5}
    
    log_info "等待 $host:$port 端口开放..."
    
    local elapsed=0
    while [[ $elapsed -lt $timeout ]]; do
        if check_port_open "$host" "$port"; then
            log_info "端口 $host:$port 已开放"
            return 0
        fi
        
        sleep $interval
        elapsed=$((elapsed + interval))
        log_debug "等待端口开放... ($elapsed/$timeout 秒)"
    done
    
    log_error "等待端口 $host:$port 开放超时"
    return 1
}

# 检查服务健康状态
check_service_health() {
    local host=$1
    local service=$2
    local health_check_cmd=$3
    
    log_debug "检查 $host 上服务 $service 的健康状态"
    
    if remote_execute "$host" "$health_check_cmd"; then
        log_debug "服务 $service 在 $host 上健康"
        return 0
    else
        log_debug "服务 $service 在 $host 上不健康"
        return 1
    fi
}

# =============================================================================
# 锁机制
# =============================================================================

# 获取锁
acquire_lock() {
    local lock_name=$1
    local timeout=${2:-300}
    
    LOCK_FILE="/tmp/${lock_name}.lock"
    
    local elapsed=0
    while [[ $elapsed -lt $timeout ]]; do
        if (set -C; echo $$ > "$LOCK_FILE") 2>/dev/null; then
            log_info "获取锁成功: $lock_name"
            return 0
        fi
        
        sleep 1
        elapsed=$((elapsed + 1))
    done
    
    log_error "获取锁超时: $lock_name"
    return 1
}

# 释放锁
release_lock() {
    if [[ -n "$LOCK_FILE" && -f "$LOCK_FILE" ]]; then
        rm -f "$LOCK_FILE"
        log_info "释放锁: $LOCK_FILE"
        LOCK_FILE=""
    fi
}

# =============================================================================
# 初始化
# =============================================================================

# 创建临时目录
TEMP_DIR=$(mktemp -d)
export TEMP_DIR

# =============================================================================
# 离线安装支持函数 - 专注于二进制包部署
# =============================================================================

# 检查离线安装环境
check_offline_environment() {
    local host=$1

    log_info "检查 $host 的离线二进制包部署环境..."

    if [[ "$OFFLINE_MODE" != "true" ]]; then
        log_debug "当前为在线模式，跳过离线环境检查"
        return 0
    fi

    local errors=0

    # 检查本地软件仓库（二进制包）
    if [[ ! -d "$SOFTWARE_REPO" ]]; then
        log_error "二进制包仓库目录不存在: $SOFTWARE_REPO"
        errors=$((errors + 1))
    else
        log_info "✓ 二进制包仓库存在: $SOFTWARE_REPO"
        # 检查仓库中的二进制包
        local package_count=$(find "$SOFTWARE_REPO" -name "*.tar.gz" -o -name "*.tgz" -o -name "*.tar.bz2" -o -name "*.tar.xz" | wc -l)
        log_info "发现 $package_count 个二进制包"
    fi

    # 检查本地YUM仓库（运行时依赖）
    if [[ "$USE_LOCAL_REPO" == "true" && ! -d "$LOCAL_YUM_REPO" ]]; then
        log_error "本地YUM仓库不存在: $LOCAL_YUM_REPO"
        errors=$((errors + 1))
    else
        log_info "✓ 本地YUM仓库存在: $LOCAL_YUM_REPO"
    fi

    # 检查Python包仓库（如果需要）
    if [[ ! -d "$LOCAL_PIP_REPO" ]]; then
        log_warn "本地PIP仓库不存在: $LOCAL_PIP_REPO（某些服务可能需要）"
    fi

    if [[ $errors -gt 0 ]]; then
        log_error "离线环境检查失败，请确保所有必需的离线资源已准备"
        return 1
    fi

    log_info "离线二进制包部署环境检查通过"
    return 0
}

# 安装Python包（运行时需求，支持离线）
install_python_packages() {
    local host=$1
    shift
    local packages=("$@")

    log_info "在 $host 上安装Python运行时包: ${packages[*]}"

    if [[ "$OFFLINE_MODE" == "true" ]]; then
        install_python_packages_offline "$host" "${packages[@]}"
    else
        install_python_packages_online "$host" "${packages[@]}"
    fi
}

# 在线安装Python包（运行时需求）
install_python_packages_online() {
    local host=$1
    shift
    local packages=("$@")

    log_info "在线安装Python运行时包"
    local install_cmd="pip3 install ${packages[*]}"

    if remote_execute "$host" "$install_cmd"; then
        log_info "Python运行时包在线安装成功"
        return 0
    else
        log_error "Python运行时包在线安装失败"
        return 1
    fi
}

# 离线安装Python包（运行时需求）
install_python_packages_offline() {
    local host=$1
    shift
    local packages=("$@")

    if [[ ! -d "$LOCAL_PIP_REPO" ]]; then
        log_error "离线模式下本地PIP仓库不存在: $LOCAL_PIP_REPO"
        return 1
    fi

    log_info "配置离线Python包仓库"
    # 配置pip使用本地仓库
    remote_execute "$host" "
        mkdir -p ~/.pip
        cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = file://$LOCAL_PIP_REPO/simple
trusted-host = localhost
EOF
    "

    local install_cmd="pip3 install --no-index --find-links $LOCAL_PIP_REPO ${packages[*]}"

    if remote_execute "$host" "$install_cmd"; then
        log_info "Python运行时包离线安装成功"
        return 0
    else
        log_error "Python运行时包离线安装失败"
        return 1
    fi
}

# 下载二进制包（支持离线模式）
download_binary_package() {
    local url=$1
    local dest_file=$2
    local host=${3:-"localhost"}

    if [[ "$OFFLINE_MODE" == "true" ]]; then
        log_info "离线模式下跳过二进制包下载，使用本地仓库: $url"
        return 0
    fi

    log_info "下载二进制包: $url -> $dest_file"

    if [[ "$host" == "localhost" ]]; then
        wget -O "$dest_file" "$url" || curl -o "$dest_file" "$url"
    else
        remote_execute "$host" "wget -O '$dest_file' '$url' || curl -o '$dest_file' '$url'"
    fi

    # 验证下载的文件
    if [[ "$host" == "localhost" ]]; then
        if [[ -f "$dest_file" ]]; then
            log_info "✓ 二进制包下载成功: $dest_file"
            return 0
        else
            log_error "二进制包下载失败: $dest_file"
            return 1
        fi
    else
        if remote_execute "$host" "test -f '$dest_file'"; then
            log_info "✓ 二进制包下载成功: $dest_file"
            return 0
        else
            log_error "二进制包下载失败: $dest_file"
            return 1
        fi
    fi
}

# 检查网络连通性（适配离线模式）
check_network_connectivity_adaptive() {
    if [[ "$OFFLINE_MODE" == "true" ]]; then
        log_info "离线模式下跳过外网连通性检查"
        return 0
    fi

    check_network_connectivity
}

# 配置离线YUM仓库
setup_offline_yum_repo() {
    local host=$1

    if [[ "$OFFLINE_MODE" != "true" ]]; then
        return 0
    fi

    log_info "在 $host 上配置离线YUM仓库"

    remote_execute "$host" "
        # 备份原始仓库配置
        mkdir -p /etc/yum.repos.d/backup
        mv /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/ 2>/dev/null || true

        # 创建本地仓库配置
        cat > /etc/yum.repos.d/local.repo << 'EOF'
[local-base]
name=Local Base Repository
baseurl=file://$LOCAL_YUM_REPO
enabled=1
gpgcheck=0
EOF

        # 清理缓存并重建
        yum clean all
    "
    # 检查本地YUM仓库目录
    if [[ ! -d "$LOCAL_YUM_REPO" ]]; then
        log_warn "本地YUM仓库目录不存在: $LOCAL_YUM_REPO"
        log_info "将尝试直接从RPM文件安装依赖包"
        return 0
    fi

    # 更新YUM缓存
    if remote_execute "$host" "yum clean all && yum makecache"; then
        log_info "离线YUM仓库配置成功"
        return 0
    else
        log_warn "YUM缓存更新失败，但仓库可能仍然可用"
        # 即使缓存更新失败，也不应该阻止部署继续进行
        # 因为二进制包部署主要依赖直接的RPM安装
        return 0
    fi
}

# =============================================================================
# 二进制包部署专用函数
# =============================================================================

# 获取适合当前CPU的二进制包
get_compatible_binary_package() {
    local service_name=$1
    local package_dir=${2:-"$SOFTWARE_REPO"}

    log_info "为 $service_name 查找兼容的二进制包..."

    # 定义包名模式（按优先级排序）
    local package_patterns=()

    if [[ "$IS_HYGON_CPU" == "true" ]]; then
        # 海光CPU优先模式
        package_patterns+=(
            "${service_name}-hygon-*.tar.gz"
            "${service_name}-*-hygon-*.tar.gz"
            "${service_name}-*-x86_64-*.tar.gz"
            "${service_name}-*.tar.gz"
        )
    else
        # 通用x86_64模式
        package_patterns+=(
            "${service_name}-*-x86_64-*.tar.gz"
            "${service_name}-*.tar.gz"
        )
    fi

    # 查找匹配的包
    for pattern in "${package_patterns[@]}"; do
        local found_package=$(find "$package_dir" -name "$pattern" | head -1)
        if [[ -n "$found_package" ]]; then
            log_info "找到兼容的二进制包: $(basename $found_package)"
            echo "$found_package"
            return 0
        fi
    done

    log_error "未找到 $service_name 的兼容二进制包"
    return 1
}

# 验证二进制包完整性
verify_binary_package_integrity() {
    local package_file=$1
    local checksum_file=${2:-"$SOFTWARE_REPO/checksums.sha256"}

    if [[ ! -f "$package_file" ]]; then
        log_error "二进制包文件不存在: $package_file"
        return 1
    fi

    # 检查文件大小（基本验证）
    local file_size=$(stat -c%s "$package_file" 2>/dev/null || echo "0")
    if [[ $file_size -lt 1024 ]]; then
        log_error "二进制包文件过小，可能损坏: $package_file"
        return 1
    fi

    # 如果有校验和文件，进行校验
    if [[ -f "$checksum_file" ]]; then
        local package_name=$(basename "$package_file")
        if grep -q "$package_name" "$checksum_file"; then
            log_info "验证二进制包校验和: $package_name"
            if (cd "$(dirname "$package_file")" && sha256sum -c "$checksum_file" --ignore-missing | grep -q "$package_name.*OK"); then
                log_info "✓ 二进制包校验和验证通过"
                return 0
            else
                log_error "二进制包校验和验证失败: $package_name"
                return 1
            fi
        else
            log_warn "校验和文件中未找到 $package_name 的记录，跳过校验"
        fi
    else
        log_debug "未找到校验和文件，跳过完整性验证"
    fi

    log_info "✓ 二进制包基本验证通过: $(basename $package_file)"
    return 0
}

log_debug "通用函数库加载完成 - 二进制包部署模式"
log_debug "临时目录: $TEMP_DIR"
log_debug "部署模式: $([ "$OFFLINE_MODE" == "true" ] && echo "离线二进制包部署" || echo "在线二进制包部署")"
log_debug "软件仓库: $SOFTWARE_REPO"
log_debug "支持的CPU类型: $([ "$IS_HYGON_CPU" == "true" ] && echo "海光CPU优化" || echo "通用x86_64")"
