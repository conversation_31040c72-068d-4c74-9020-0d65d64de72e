#!/bin/bash
# MongoDB集群部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化MongoDB副本集部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="MongoDB集群部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# MongoDB特定配置
# =============================================================================

# MongoDB配置
MONGODB_USER="mongodb"
MONGODB_GROUP="mongodb"
MONGODB_HOME="/apps/mongodb"
MONGODB_DATA_DIR="/apps/data/mongodb"
MONGODB_LOG_DIR="/var/log/mongodb"
MONGODB_CONFIG_DIR="$MONGODB_HOME/conf"

# 二进制包配置
MONGODB_PACKAGE_DIR="/apps/software/mongodb"
MONGODB_BINARY_PACKAGE="mongodb-4.0.23.tar.gz"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INSTALL=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false
UNINSTALL=false
BACKUP_DATA=true

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --uninstall)
            UNINSTALL=true
            shift
            ;;
        --no-backup)
            BACKUP_DATA=false
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install      跳过二进制包安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过集群初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  --uninstall         卸载MongoDB集群"
            echo "  --no-backup         卸载时不备份数据"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# MongoDB依赖安装函数
# =============================================================================

# 统一二进制包安装MongoDB
install_mongodb_dependencies() {
    local host=$1
    local tgz_package="mongodb-linux-x86_64-rhel70-4.4.18.tgz"
    local remote_package_path="$MONGODB_PACKAGE_DIR/$tgz_package"

    log_info "在 $host 上安装MongoDB（统一二进制包）..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上解压MongoDB二进制包"
        return 0
    fi

    # 检查二进制包是否存在
    if ! remote_execute "$host" "test -f '$remote_package_path'"; then
        log_error "$host: 未找到MongoDB二进制包 $remote_package_path，请先分发安装包"
        return 1
    fi

    # 解压到目标目录
    remote_execute "$host" "
        rm -rf '$MONGODB_HOME' && \
        mkdir -p '$MONGODB_HOME' && \
        tar -xzf '$remote_package_path' -C '$MONGODB_HOME' --strip-components=1
    " || {
        log_error "$host: MongoDB二进制包解压失败"
        return 1
    }

    # 拷贝bin目录
    remote_execute "$host" "
        mkdir -p '$MONGODB_HOME/bin' && \
        cp -a '$MONGODB_HOME'/bin/* '$MONGODB_HOME/bin/' 2>/dev/null || true
    "

    # 自动检测并安装openssl-libs依赖
    log_info "检测并安装openssl-libs依赖: $host"
    remote_execute "$host" "
        # 禁用local-base仓库，避免yum源冲突
        if [[ -f /etc/yum.repos.d/local.repo ]]; then
            if grep -q '\[local-base\]' /etc/yum.repos.d/local.repo; then
                echo '检测到local-base仓库，禁用...'
                mv /etc/yum.repos.d/local.repo /etc/yum.repos.d/local.repo.disabled
                echo '✓ local-base仓库已禁用'
            fi
        fi
        # 恢复net.repo（如有备份）
        if [[ -f /etc/yum.repos.d/backup/net.repo ]]; then
            echo '检测到备份的net.repo，恢复...'
            cp /etc/yum.repos.d/backup/net.repo /etc/yum.repos.d/
            echo '✓ net.repo已恢复'
        fi
        if ! ldconfig -p | grep -q 'libcrypto.so.10'; then
            if command -v yum >/dev/null 2>&1; then
                yum install -y openssl-libs || echo 'openssl-libs安装失败，请检查YUM源或手动安装'
            else
                echo '未检测到yum，无法自动安装openssl-libs，请手动安装依赖包'
            fi
        else
            echo '✓ libcrypto.so.10已存在，无需安装openssl-libs'
        fi
    "

    # 设置权限
    remote_execute "$host" "
        chown -R $MONGODB_USER:$MONGODB_GROUP '$MONGODB_HOME' && \
        chmod -R 755 '$MONGODB_HOME'
    "

    log_info "$host: MongoDB二进制包安装完成"
    return 0
}

# =============================================================================
# MongoDB编译安装函数
# =============================================================================

prepare_mongodb_environment() {
    local host=$1

    log_info "在 $host 上准备MongoDB环境..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备MongoDB环境"
        return 0
    fi

    # 创建MongoDB用户
    remote_execute "$host" "
        if ! id $MONGODB_USER >/dev/null 2>&1; then
            groupadd -g 1001 $MONGODB_GROUP
            useradd -u 1001 -g $MONGODB_GROUP -r -s /bin/false -d /var/lib/mongodb $MONGODB_USER
        fi
    "

    # 检查离线环境
    check_offline_environment "$host"

    # 配置离线YUM仓库（如果是离线模式）
    setup_offline_yum_repo "$host"

    log_info "在 $host 创建MongoDB部署目录..."

    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $MONGODB_HOME/{bin,conf,logs}
        mkdir -p $MONGODB_DATA_DIR/{data,log,backup}
        mkdir -p /var/run/mongodb
        chown -R $MONGODB_USER:$MONGODB_GROUP $MONGODB_HOME $MONGODB_DATA_DIR /var/run/mongodb
        chmod 755 $MONGODB_HOME $MONGODB_DATA_DIR
        chmod 750 /var/run/mongodb
    "

    # 验证目录创建和权限设置
    log_info "验证 $host 上的MongoDB目录结构..."

    local validation_failed=false

    # 检查基础目录是否存在
    local base_dirs=("$MONGODB_HOME" "$MONGODB_HOME/bin" "$MONGODB_HOME/conf" "$MONGODB_HOME/logs"
                     "$MONGODB_DATA_DIR" "$MONGODB_DATA_DIR/data" "$MONGODB_DATA_DIR/log"
                     "$MONGODB_DATA_DIR/backup" "/var/run/mongodb")

    for dir in "${base_dirs[@]}"; do
        if ! remote_execute "$host" "test -d '$dir'"; then
            log_error "目录不存在: $dir"
            validation_failed=true
        else
            log_debug "✓ 目录存在: $dir"
        fi
    done

    # 检查目录权限和所有者
    remote_execute "$host" "
        # 检查所有者
        for dir in '$MONGODB_HOME' '$MONGODB_DATA_DIR' '/var/run/mongodb'; do
            owner=\$(stat -c '%U:%G' \"\$dir\" 2>/dev/null)
            if [[ \"\$owner\" != \"$MONGODB_USER:$MONGODB_GROUP\" ]]; then
                echo \"错误: 目录 \$dir 所有者不正确，期望: $MONGODB_USER:$MONGODB_GROUP，实际: \$owner\"
                exit 1
            else
                echo \"✓ 目录所有者正确: \$dir (\$owner)\"
            fi
        done

        # 检查权限
        mongodb_home_perm=\$(stat -c '%a' '$MONGODB_HOME' 2>/dev/null)
        data_dir_perm=\$(stat -c '%a' '$MONGODB_DATA_DIR' 2>/dev/null)
        run_dir_perm=\$(stat -c '%a' '/var/run/mongodb' 2>/dev/null)

        if [[ \"\$mongodb_home_perm\" != \"755\" ]]; then
            echo \"错误: $MONGODB_HOME 权限不正确，期望: 755，实际: \$mongodb_home_perm\"
            exit 1
        fi

        if [[ \"\$data_dir_perm\" != \"755\" ]]; then
            echo \"错误: $MONGODB_DATA_DIR 权限不正确，期望: 755，实际: \$data_dir_perm\"
            exit 1
        fi

        if [[ \"\$run_dir_perm\" != \"750\" ]]; then
            echo \"错误: /var/run/mongodb 权限不正确，期望: 750，实际: \$run_dir_perm\"
            exit 1
        fi

        echo \"✓ 所有目录权限设置正确\"
    " || validation_failed=true

    # 测试MongoDB用户的写权限
    remote_execute "$host" "
        # 测试写权限
        test_dirs=('$MONGODB_HOME/logs' '$MONGODB_DATA_DIR/data' '$MONGODB_DATA_DIR/log'
                   '$MONGODB_DATA_DIR/backup' '/var/run/mongodb')

        for test_dir in \"\${test_dirs[@]}\"; do
            test_file=\"\$test_dir/.write_test_\$(date +%s)\"
            if sudo -u $MONGODB_USER touch \"\$test_file\" 2>/dev/null; then
                sudo -u $MONGODB_USER rm -f \"\$test_file\" 2>/dev/null
                echo \"✓ MongoDB用户对 \$test_dir 有写权限\"
            else
                echo \"错误: MongoDB用户对 \$test_dir 没有写权限\"
                exit 1
            fi
        done
    " || validation_failed=true

    # 检查磁盘空间
    remote_execute "$host" "
        # 检查数据目录磁盘空间（至少需要1GB可用空间）
        available_space=\$(df '$MONGODB_DATA_DIR' | awk 'NR==2 {print \$4}')
        min_space=1048576  # 1GB in KB

        if [[ \$available_space -lt \$min_space ]]; then
            echo \"警告: $MONGODB_DATA_DIR 可用磁盘空间不足1GB (当前: \$((\$available_space/1024))MB)\"
        else
            echo \"✓ $MONGODB_DATA_DIR 磁盘空间充足 (可用: \$((\$available_space/1024))MB)\"
        fi
    "

    if [[ "$validation_failed" == "true" ]]; then
        log_error "MongoDB目录结构验证失败: $host"
        return 1
    else
        log_info "✓ MongoDB目录结构验证成功: $host"
    fi

    # 安装运行时依赖 - 使用混合安装策略
    install_mongodb_dependencies "$host"

    log_info "MongoDB环境准备完成: $host"
}

install_mongodb_binary() {
    local host=$1

    log_info "在 $host 上配置MongoDB..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上配置MongoDB"
        return 0
    fi

    # 检查MongoDB是否已安装
    if remote_execute "$host" "test -f $MONGODB_HOME/bin/mongod" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "MongoDB二进制版本已安装在 $host，跳过安装"
            return 0
        else
            log_info "强制重新安装MongoDB在 $host"
            remote_execute "$host" "systemctl stop mongod 2>/dev/null || true; systemctl disable mongod 2>/dev/null || true"
        fi
    fi

    # 创建MongoDB配置目录
    remote_execute "$host" "mkdir -p '$MONGODB_CONFIG_DIR'"

    # 设置目录权限
    remote_execute "$host" "chown -R $MONGODB_USER:$MONGODB_GROUP '$MONGODB_HOME' '$MONGODB_CONFIG_DIR'"

    # 确保MongoDB二进制文件有执行权限
    remote_execute "$host" "find '$MONGODB_HOME/bin' -type f -exec chmod +x {} \\; 2>/dev/null || true"

    # 创建配置文件模板（如果不存在）
    remote_execute "$host" "
        if [[ ! -f '$MONGODB_CONFIG_DIR/mongod-template.conf' ]]; then
            cat > '$MONGODB_CONFIG_DIR/mongod-template.conf' << 'TEMPLATE_EOF'
# MongoDB基础配置模板 - 二进制包安装版本
systemLog:
  destination: file
  path: /apps/data/mongodb/log/mongod.log
  logAppend: true
  logRotate: reopen

storage:
  dbPath: /apps/data/mongodb/data
  journal:
    enabled: true

processManagement:
  fork: true
  pidFilePath: /var/run/mongodb/mongod.pid

net:
  bindIp: 0.0.0.0
  port: 27017

replication:
  replSetName: \"rs0\"

security:
  authorization: disabled
TEMPLATE_EOF
            chown $MONGODB_USER:$MONGODB_GROUP '$MONGODB_CONFIG_DIR/mongod-template.conf'
        fi
    "

    # 设置环境变量
    remote_execute "$host" "if ! grep -q 'MONGODB_HOME' /etc/profile; then echo 'export MONGODB_HOME=$MONGODB_HOME' >> /etc/profile; echo 'export PATH=\$PATH:\$MONGODB_HOME/bin' >> /etc/profile; fi"

    # 验证安装和执行权限
    log_info "验证MongoDB安装和执行权限: $host"
    local verification_failed=false
    remote_execute "$host" "
        for binary in mongod mongo; do
            declare binary_path='$MONGODB_HOME/bin/'\$binary
            if [[ -e \"\$binary_path\" ]]; then
                if [[ -x \"\$binary_path\" ]]; then
                    echo \"✓ \$binary 文件存在且有执行权限\"
                else
                    chmod +x \"\$binary_path\"
                    if [[ -x \"\$binary_path\" ]]; then
                        echo \"✓ \$binary 执行权限已修复\"
                    else
                        echo \"✗ \$binary 执行权限修复失败\"
                        exit 1
                    fi
                fi
            else
                echo \"✗ \$binary 文件不存在: \$binary_path\"
                exit 1
            fi
        done
    " || verification_failed=true

    if [[ "$verification_failed" == "true" ]]; then
        log_error "MongoDB文件验证失败: $host"
        return 1
    fi

    if remote_execute "$host" "$MONGODB_HOME/bin/mongod --version"; then
        log_info "MongoDB二进制包安装成功: $host"
        local mongodb_version
        mongodb_version=$(remote_execute "$host" "$MONGODB_HOME/bin/mongod --version")
        log_info "MongoDB版本: $mongodb_version"
    else
        log_error "MongoDB二进制包安装失败或执行权限问题: $host"
        return 1
    fi
}

# =============================================================================
# MongoDB服务检查函数
# =============================================================================

check_mongodb_installed() {
    local host=$1
    local service_running=false

    # 检查服务是否运行
    if remote_execute "$host" "systemctl is-active --quiet mongod"; then
        log_info "MongoDB服务已在运行: $host"
        service_running=true
    fi

    # 如果服务已完整安装
    if [[ "$service_running" == "true" ]]; then
        return 0  # 已安装
    fi

    return 1  # 未安装
}

# 注意：离线包安装函数已移至 common.sh 中统一实现

# 安装可选包
install_optional_packages() {
    local host=$1
    shift
    local packages=("$@")

    log_info "尝试安装可选包（失败不影响主流程）: ${packages[*]}"

    for package in "${packages[@]}"; do
        if remote_execute "$host" "yum install -y $package" 2>/dev/null; then
            log_debug "✓ 可选包安装成功: $package"
        else
            log_debug "- 可选包安装失败: $package (忽略)"
        fi
    done
}

# ============================================= ================================
# MongoDB配置函数
# =============================================================================

generate_mongodb_config() {
    local host=$1
    local role=${MONGODB_ROLES[$host]:-"secondary"}
    local priority=${MONGODB_PRIORITIES[$host]:-"1"}
    
    log_info "为 $host 生成MongoDB配置文件 (角色: $role)..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成MongoDB配置文件"
        return 0
    fi
    
    # 确保配置目录存在
    remote_execute "$host" "mkdir -p '$MONGODB_CONFIG_DIR'"
    
    # 生成主配置文件
    local config_content="# MongoDB配置文件 - $host
systemLog:
  destination: file
  path: $MONGODB_DATA_DIR/log/mongod.log
  logAppend: true
  logRotate: reopen
  timeStampFormat: iso8601-local

storage:
  dbPath: $MONGODB_DATA_DIR/data
  journal:
    enabled: true
    commitIntervalMs: 100
  wiredTiger:
    engineConfig:
      cacheSizeGB: 16
      journalCompressor: snappy
      directoryForIndexes: true
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

processManagement:
  fork: true
  pidFilePath: /run/mongodb/mongod.pid
  timeZoneInfo: /usr/share/zoneinfo

net:
  bindIp: 0.0.0.0
  port: $MONGODB_PORT
  maxIncomingConnections: 2000
  wireObjectCheck: true
  ipv6: false

replication:
  replSetName: \"$MONGODB_REPLICA_SET\"
  oplogSizeMB: $MONGODB_OPLOG_SIZE

security:
  authorization: disabled


operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp

setParameter:
  enableLocalhostAuthBypass: false
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
  maxLogSizeKB: 10240
  logLevel: 1
  cursorTimeoutMillis: 600000
  notablescan: false
"
#     keyFile: $MONGODB_CONFIG_DIR/mongodb-keyfile
    # 写入配置文件
    remote_execute "$host" "cat > $MONGODB_CONFIG_DIR/mongod.conf << 'EOF'
$config_content
EOF"
    
    # 生成密钥文件
    if [[ "$role" == "primary" ]]; then
        log_info "生成MongoDB密钥文件..."
        local keyfile_content=$(openssl rand -base64 756)

        # 在当前机器上创建临时密钥文件
        local temp_keyfile="/tmp/mongodb-keyfile.$$"
        echo "$keyfile_content" > "$temp_keyfile"
        chmod 600 "$temp_keyfile"

        # 分发密钥文件到所有节点
        for other_host in "${MONGODB_HOSTS[@]}"; do
            log_debug "分发密钥文件到 $other_host"
            # 确保远程目录存在
            remote_execute "$other_host" "mkdir -p '$MONGODB_CONFIG_DIR'"
            if scp "$temp_keyfile" "$other_host:$MONGODB_CONFIG_DIR/mongodb-keyfile"; then
                log_debug "✓ 密钥文件已成功拷贝到 $other_host"
                # 设置远程机器上密钥文件的权限
                remote_execute "$other_host" "chmod 400 $MONGODB_CONFIG_DIR/mongodb-keyfile; chown $MONGODB_USER:$MONGODB_GROUP $MONGODB_CONFIG_DIR/mongodb-keyfile"
            else
                log_error "✗ 密钥文件拷贝到 $other_host 失败"
                rm -f "$temp_keyfile"
                return 1
            fi
        done
        # 清理临时密钥文件
        rm -f "$temp_keyfile"
        log_info "✓ MongoDB密钥文件生成和分发完成"
    fi
    
    log_info "MongoDB配置文件生成完成: $host"
}

create_mongodb_service() {
    local host=$1
    
    log_info "在 $host 上创建MongoDB systemd服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上创建MongoDB服务"
        return 0
    fi
    
    # 临时排查用：去掉--fork参数，便于输出详细错误
    local service_content_debug="[Unit]
Description=MongoDB Database Server (Debug)
Documentation=https://docs.mongodb.org/manual
After=network-online.target
Wants=network-online.target

[Service]
User=$MONGODB_USER
Group=$MONGODB_GROUP
Type=simple
PIDFile=/run/mongodb/mongod.pid
RuntimeDirectory=mongodb
ExecStart=$MONGODB_HOME/bin/mongod --config $MONGODB_CONFIG_DIR/mongod.conf
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mongod
KillMode=mixed
TimeoutStopSec=30
LimitNOFILE=64000
LimitNPROC=64000

[Install]
WantedBy=multi-user.target"
    
    remote_execute "$host" "
        cat > /etc/systemd/system/mongod.service << 'EOF'
$service_content_debug
EOF
        systemctl daemon-reload
        systemctl enable mongod
    "
    
    log_info "MongoDB服务创建完成: $host"
}

# =============================================================================
# MongoDB集群初始化函数
# =============================================================================

start_mongodb_cluster() {
    log_info "启动MongoDB集群..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动MongoDB集群"
        return 0
    fi
    
    # 启动所有节点
    for host in "${MONGODB_HOSTS[@]}"; do
        log_info "启动MongoDB服务: $host"
        start_service "$host" "mongod"
        
        # 等待服务启动
        if ! wait_for_port "$host" "$MONGODB_PORT" 60; then
            log_error "MongoDB服务启动失败: $host"
            return 1
        fi
    done
    
    log_info "所有MongoDB节点启动完成"
}

initialize_replica_set() {
    log_info "初始化MongoDB副本集..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将初始化MongoDB副本集"
        return 0
    fi
    
    # 找到主节点
    local primary_host=""
    for host in "${MONGODB_HOSTS[@]}"; do
        if [[ "${MONGODB_ROLES[$host]}" == "primary" ]]; then
            primary_host="$host"
            break
        fi
    done
    
    if [[ -z "$primary_host" ]]; then
        primary_host="${MONGODB_HOSTS[0]}"
        log_warn "未找到指定的主节点，使用第一个节点: $primary_host"
    fi
    
    # 生成副本集初始化脚本
    local init_script="rs.initiate({
  _id: \"$MONGODB_REPLICA_SET\",
  members: ["
    
    local member_id=0
    for host in "${MONGODB_HOSTS[@]}"; do
        local priority="${MONGODB_PRIORITIES[$host]:-1}"
        local role="${MONGODB_ROLES[$host]:-secondary}"
        
        init_script="$init_script
    { 
      _id: $member_id, 
      host: \"$host:$MONGODB_PORT\", 
      priority: $priority,
      tags: { \"role\": \"$role\", \"datacenter\": \"dc1\" }
    },"
        
        member_id=$((member_id + 1))
    done
    
    # 移除最后的逗号并完成脚本
    init_script="${init_script%,}
  ],
  settings: {
    chainingAllowed: false,
    heartbeatIntervalMillis: 2000,
    heartbeatTimeoutSecs: 10,
    electionTimeoutMillis: 10000,
    catchUpTimeoutMillis: 60000,
    getLastErrorModes: {
      \"majority\": { \"datacenter\": 1 }
    }
  }
})"
    
    # 执行初始化
    log_info "在主节点 $primary_host 上执行副本集初始化..."
    remote_execute "$primary_host" "
        $MONGODB_HOME/bin/mongo --host $primary_host:$MONGODB_PORT --eval '$init_script'
    "
    
    # 等待副本集稳定
    log_info "等待副本集稳定..."
    sleep 30
    
    # 验证副本集状态
    if remote_execute "$primary_host" "$MONGODB_HOME/bin/mongo --host $primary_host:$MONGODB_PORT --eval 'rs.status()'"; then
        log_info "MongoDB副本集初始化成功"
    else
        log_error "MongoDB副本集初始化失败"
        return 1
    fi
}

create_mongodb_users() {
    log_info "创建MongoDB用户..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将创建MongoDB用户"
        return 0
    fi
    
    # 找到主节点
    local primary_host=""
    for host in "${MONGODB_HOSTS[@]}"; do
        if [[ "${MONGODB_ROLES[$host]}" == "primary" ]]; then
            primary_host="$host"
            break
        fi
    done
    
    if [[ -z "$primary_host" ]]; then
        primary_host="${MONGODB_HOSTS[0]}"
    fi
    
    # 生成随机密码
    local admin_password=$(generate_password)
    local monitor_password=$(generate_password)
    
    # 创建管理员用户
    remote_execute "$primary_host" "
        $MONGODB_HOME/bin/mongo --host $primary_host:$MONGODB_PORT --eval '
        db.createUser({
          user: \"admin\",
          pwd: \"$admin_password\",
          roles: [
            { role: \"userAdminAnyDatabase\", db: \"admin\" },
            { role: \"readWriteAnyDatabase\", db: \"admin\" },
            { role: \"dbAdminAnyDatabase\", db: \"admin\" },
            { role: \"clusterAdmin\", db: \"admin\" }
          ]
        })
        
        db.createUser({
          user: \"monitor\",
          pwd: \"$monitor_password\",
          roles: [
            { role: \"clusterMonitor\", db: \"admin\" },
            { role: \"read\", db: \"local\" }
          ]
        })
        '
    "
#    bin/mongo --host ***************:27017 --eval "use admin; db.createUser({ user: 'admin', pwd: '123123', roles: [ { role: 'userAdminAnyDatabase', db: 'admin' }, { role: 'readWriteAnyDatabase', db: 'admin' }, { role: 'dbAdminAnyDatabase', db: 'admin' }, { role: 'clusterAdmin', db: 'admin' } ] }); db.createUser({ user: 'monitor', pwd: '1231231', roles: [ { role: 'clusterMonitor', db: 'admin' }, { role: 'read', db: 'local' } ] });"
    # 保存密码到文件
    remote_execute "$primary_host" "
        cat > $MONGODB_CONFIG_DIR/passwords.txt << EOF
MongoDB管理员密码:
用户名: admin
密码: $admin_password

MongoDB监控用户密码:
用户名: monitor
密码: $monitor_password

连接字符串:
mongodb://admin:$admin_password@${MONGODB_HOSTS[0]}:$MONGODB_PORT,${MONGODB_HOSTS[1]}:$MONGODB_PORT,${MONGODB_HOSTS[2]}:$MONGODB_PORT/admin?replicaSet=$MONGODB_REPLICA_SET
EOF
        chmod 600 $MONGODB_CONFIG_DIR/passwords.txt
        chown $MONGODB_USER:$MONGODB_GROUP $MONGODB_CONFIG_DIR/passwords.txt
    "
    
    log_info "MongoDB用户创建完成，密码已保存到 $primary_host:$MONGODB_CONFIG_DIR/passwords.txt"
}

# =============================================================================
# MongoDB卸载函数
# =============================================================================

# 备份MongoDB数据
backup_mongodb_data() {
    local host=$1
    local backup_dir="/backup/mongodb/$(date +%Y%m%d_%H%M%S)"

    log_info "在 $host 上备份MongoDB数据..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上备份MongoDB数据到 $backup_dir"
        return 0
    fi

    # 创建备份目录
    remote_execute "$host" "
        mkdir -p $backup_dir
        chown -R $MONGODB_USER:$MONGODB_GROUP $backup_dir 2>/dev/null || true
    " || {
        log_warn "创建备份目录失败: $host"
        return 1
    }

    # 备份MongoDB数据
    log_info "备份MongoDB数据..."

    remote_execute "$host" "
        # 复制数据文件
        if [[ -d '$MONGODB_DATA_DIR/data' ]]; then
            cp -r '$MONGODB_DATA_DIR/data' '$backup_dir/mongodb-data' 2>/dev/null || echo '数据文件复制失败，继续...'
        fi

        # 复制配置文件
        if [[ -f '$MONGODB_CONFIG_DIR/mongod.conf' ]]; then
            cp '$MONGODB_CONFIG_DIR/mongod.conf' '$backup_dir/mongod.conf' 2>/dev/null || echo '配置文件复制失败，继续...'
        fi

        # 复制日志文件
        if [[ -f '$MONGODB_DATA_DIR/log/mongod.log' ]]; then
            cp '$MONGODB_DATA_DIR/log/mongod.log' '$backup_dir/mongod.log' 2>/dev/null || echo '日志文件复制失败，继续...'
        fi

        # 复制密码文件
        if [[ -f '$MONGODB_CONFIG_DIR/passwords.txt' ]]; then
            cp '$MONGODB_CONFIG_DIR/passwords.txt' '$backup_dir/passwords.txt' 2>/dev/null || echo '密码文件复制失败，继续...'
        fi
    " || log_warn "MongoDB数据备份失败，继续卸载..."

    # 创建备份信息文件
    remote_execute "$host" "
        cat > '$backup_dir/backup_info.txt' << EOF
MongoDB数据备份信息
备份时间: $(date)
备份主机: $host
MongoDB版本: \$($MONGODB_HOME/bin/mongod --version 2>/dev/null || echo '未知')
备份目录: $backup_dir
EOF
    " || log_warn "创建备份信息文件失败: $host"

    log_info "MongoDB数据备份完成: $host -> $backup_dir"
}

# 停止MongoDB服务
stop_mongodb_services() {
    local host=$1

    log_info "在 $host 上停止MongoDB服务..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上停止MongoDB服务"
        return 0
    fi

    # 停止MongoDB服务
    remote_execute "$host" "
        echo \"停止MongoDB服务...\"

        # 停止服务
        systemctl stop mongod 2>/dev/null || true

        # 等待服务完全停止
        timeout 30 bash -c \"while systemctl is-active --quiet mongod; do sleep 1; done\" || true

        # 禁用服务
        systemctl disable mongod 2>/dev/null || true

        echo \"✓ MongoDB服务已停止并禁用\"

        # 强制杀死可能残留的MongoDB进程
        pkill -f mongod 2>/dev/null || true

        # 等待进程完全退出
        sleep 3

        # 检查是否还有MongoDB进程
        if pgrep -f mongod >/dev/null; then
            echo \"警告: 仍有MongoDB进程在运行，强制终止...\"
            pkill -9 -f mongod 2>/dev/null || true
        fi

        echo \"MongoDB服务已停止\"
        exit 0  # 确保命令成功退出
    " || log_warn "停止MongoDB服务时出现错误: $host"

    log_info "MongoDB服务停止完成: $host"
}

# 删除MongoDB服务文件
remove_mongodb_services() {
    local host=$1

    log_info "在 $host 上删除MongoDB服务文件..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上删除MongoDB服务文件"
        return 0
    fi

    remote_execute "$host" "
        # 删除systemd服务文件
        rm -f /etc/systemd/system/mongod.service

        # 重新加载systemd配置
        systemctl daemon-reload

        echo \"MongoDB服务文件已删除\"
        exit 0  # 确保命令成功退出
    " || log_warn "删除MongoDB服务文件时出现错误: $host"

    log_info "MongoDB服务文件删除完成: $host"
}

# 删除MongoDB文件和目录
remove_mongodb_files() {
    local host=$1

    log_info "在 $host 上删除MongoDB文件和目录..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上删除MongoDB文件和目录"
        return 0
    fi

    remote_execute "$host" "
        echo \"检查MongoDB安装方式...\"

        # 检查是否通过YUM安装
        if rpm -q mongodb-server >/dev/null 2>&1; then
            echo \"检测到YUM安装的MongoDB，执行YUM卸载...\"
            yum remove -y mongodb-server mongodb
            echo \"✓ YUM安装的MongoDB已卸载\"
        else
            echo \"未检测到YUM安装的MongoDB\"
        fi

        echo \"删除MongoDB安装目录...\"
        if [[ -d '$MONGODB_HOME' ]]; then
            rm -rf '$MONGODB_HOME'
            echo \"✓ 已删除: $MONGODB_HOME\"
        fi

        echo \"删除MongoDB数据目录...\"
        if [[ -d '$MONGODB_DATA_DIR' ]]; then
            rm -rf '$MONGODB_DATA_DIR'
            echo \"✓ 已删除: $MONGODB_DATA_DIR\"
        fi

        echo \"删除MongoDB日志目录...\"
        if [[ -d '$MONGODB_LOG_DIR' ]]; then
            rm -rf '$MONGODB_LOG_DIR'
            echo \"✓ 已删除: $MONGODB_LOG_DIR\"
        fi

        echo \"删除MongoDB运行时目录...\"
        if [[ -d '/var/run/mongodb' ]]; then
            rm -rf '/var/run/mongodb'
            echo \"✓ 已删除: /var/run/mongodb\"
        fi

        echo \"删除系统MongoDB配置文件...\"
        rm -f /etc/mongod.conf 2>/dev/null || true
        rm -rf /etc/mongodb/ 2>/dev/null || true

        echo \"删除MongoDB临时文件...\"
        rm -f /tmp/mongodb-*.sock 2>/dev/null || true
        rm -f /tmp/mongodb_*.tmp 2>/dev/null || true

        echo \"MongoDB文件和目录删除完成\"
        exit 0  # 确保命令成功退出
    " || log_warn "删除MongoDB文件和目录时出现错误: $host"

    log_info "MongoDB文件和目录删除完成: $host"
}

# 删除MongoDB用户和组
remove_mongodb_user() {
    local host=$1

    log_info "在 $host 上删除MongoDB用户和组..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上删除MongoDB用户和组"
        return 0
    fi

    remote_execute "$host" "
        # 删除MongoDB用户
        if id '$MONGODB_USER' >/dev/null 2>&1; then
            userdel '$MONGODB_USER' 2>/dev/null || true
            echo \"✓ 已删除用户: $MONGODB_USER\"
        fi

        # 删除MongoDB组
        if getent group '$MONGODB_GROUP' >/dev/null 2>&1; then
            groupdel '$MONGODB_GROUP' 2>/dev/null || true
            echo \"✓ 已删除组: $MONGODB_GROUP\"
        fi

        echo \"MongoDB用户和组删除完成\"
        exit 0  # 确保命令成功退出
    " || log_warn "删除MongoDB用户和组时出现错误: $host"

    log_info "MongoDB用户和组删除完成: $host"
}

# 清理环境变量和恢复仓库配置
cleanup_mongodb_environment() {
    local host=$1

    log_info "在 $host 上清理MongoDB环境变量和恢复仓库配置..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上清理MongoDB环境变量和恢复仓库配置"
        return 0
    fi

    remote_execute "$host" "
        # 从/etc/profile中移除MongoDB相关的环境变量
        if [[ -f /etc/profile ]]; then
            # 创建临时文件
            temp_file=\$(mktemp)

            # 过滤掉MongoDB相关的环境变量
            grep -v 'MONGODB_HOME' /etc/profile > \"\$temp_file\" 2>/dev/null || true

            # 替换原文件
            if [[ -s \"\$temp_file\" ]]; then
                mv \"\$temp_file\" /etc/profile
                echo \"✓ 已从/etc/profile中移除MongoDB环境变量\"
            else
                rm -f \"\$temp_file\"
            fi
        fi

        # 清理可能的MongoDB相关的cron任务
        crontab -l 2>/dev/null | grep -v mongodb | crontab - 2>/dev/null || true

        # 恢复local.repo（如果之前被禁用）
        echo \"检查是否需要恢复local.repo...\"
        if [[ -f '/etc/yum.repos.d/local.repo.disabled' ]]; then
            echo \"发现被禁用的local.repo，正在恢复...\"
            mv /etc/yum.repos.d/local.repo.disabled /etc/yum.repos.d/local.repo
            echo \"✓ local.repo已恢复\"

            # 清理YUM缓存
            yum clean all
            echo \"✓ YUM缓存已清理\"
        fi

        # 移除MongoDB部署时添加的net.repo（如果存在且不是原始的）
        if [[ -f '/etc/yum.repos.d/net.repo' ]]; then
            echo \"检查net.repo是否为MongoDB部署时添加...\"
            # 如果存在local.repo，说明net.repo可能是MongoDB部署时恢复的
            if [[ -f '/etc/yum.repos.d/local.repo' ]]; then
                echo \"移除MongoDB部署时恢复的net.repo...\"
                mv /etc/yum.repos.d/net.repo /etc/yum.repos.d/net.repo.mongodb_backup.\$(date +%s)
                echo \"✓ net.repo已备份并移除\"
            fi
        fi

        echo \"MongoDB环境变量和仓库配置清理完成\"
        exit 0  # 确保命令成功退出
    " || log_warn "清理MongoDB环境变量时出现错误: $host"

    log_info "MongoDB环境变量和仓库配置清理完成: $host"
}

# 主卸载函数
uninstall_mongodb() {
    log_info "开始卸载MongoDB集群..."

    # 获取锁
    if ! acquire_lock "uninstall_mongodb"; then
        log_error "无法获取锁，可能有其他MongoDB操作正在运行"
        exit 1
    fi

    # 检查MongoDB主机配置
    if [[ ${#MONGODB_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置MongoDB主机"
        exit 1
    fi

    # 确认卸载操作
    if [[ "$DRY_RUN" != "true" ]]; then
        echo
        echo "⚠️  警告: 即将卸载MongoDB集群"
        echo "影响的主机: ${MONGODB_HOSTS[*]}"
        echo "数据备份: $([ "$BACKUP_DATA" == "true" ] && echo "是" || echo "否")"
        echo
        read -p "确认继续卸载? (输入 'YES' 确认): " confirm

        if [[ "$confirm" != "YES" ]]; then
            log_info "用户取消卸载操作"
            release_lock
            exit 0
        fi
    fi

    log_info "MongoDB集群卸载主机: ${MONGODB_HOSTS[*]}"

    # 对每个主机执行卸载
    local failed_hosts=()
    for host in "${MONGODB_HOSTS[@]}"; do
        log_info "开始卸载 $host 上的MongoDB..."

        # 使用子shell执行卸载操作，避免单个主机失败导致整个脚本退出
        (
            # 备份数据（如果启用）
            if [[ "$BACKUP_DATA" == "true" ]]; then
                backup_mongodb_data "$host" || log_warn "备份数据失败: $host"
            fi

            # 停止MongoDB服务
            stop_mongodb_services "$host" || log_warn "停止MongoDB服务失败: $host"

            # 删除服务文件
            remove_mongodb_services "$host" || log_warn "删除服务文件失败: $host"

            # 删除文件和目录
            remove_mongodb_files "$host" || log_warn "删除文件和目录失败: $host"

            # 删除用户和组
            remove_mongodb_user "$host" || log_warn "删除用户和组失败: $host"

            # 清理环境变量
            cleanup_mongodb_environment "$host" || log_warn "清理环境变量失败: $host"
        )

        # 检查卸载是否成功
        if [[ $? -eq 0 ]]; then
            log_info "✓ $host 上的MongoDB卸载完成"
        else
            log_error "✗ $host 上的MongoDB卸载失败"
            failed_hosts+=("$host")
        fi
    done

    # 报告卸载结果
    if [[ ${#failed_hosts[@]} -gt 0 ]]; then
        log_warn "以下主机卸载过程中出现错误: ${failed_hosts[*]}"
        log_warn "请手动检查这些主机的MongoDB状态"
    fi

    # 释放锁
    release_lock

    log_info "MongoDB集群卸载完成"

    if [[ "$BACKUP_DATA" == "true" ]]; then
        echo
        echo "📁 数据备份位置: /backup/mongodb/"
        echo "💡 提示: 备份数据保留在各主机上，如需要可手动清理"
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 检查是否为卸载模式
    if [[ "$UNINSTALL" == "true" ]]; then
        uninstall_mongodb
        exit 0
    fi

    log_info "开始MongoDB集群部署..."

    # 获取锁
    if ! acquire_lock "deploy_mongodb"; then
        log_error "无法获取锁，可能有其他MongoDB部署实例正在运行"
        exit 1
    fi
    
    # 检查MongoDB主机配置
    if [[ ${#MONGODB_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置MongoDB主机"
        exit 1
    fi
    
    log_info "MongoDB集群主机: ${MONGODB_HOSTS[*]}"

    # 检查所有节点是否已安装MongoDB
    local all_installed=true
    for host in "${MONGODB_HOSTS[@]}"; do
        if ! check_mongodb_installed "$host"; then
            all_installed=false
            log_info "$host 节点未检测到完整MongoDB安装"
        fi
    done

    # 如果所有节点已安装且未强制重装
    if [[ "$all_installed" == "true" && "$FORCE_REINSTALL" != "true" ]]; then
        log_info "所有节点MongoDB服务已存在，跳过部署"

        # 检查副本集是否已初始化
        local primary_host="${MONGODB_HOSTS[0]}"
        if remote_execute "$primary_host" "$MONGODB_HOME/bin/mongo --host $primary_host:$MONGODB_PORT --eval 'rs.status().ok' | grep -q 1"; then
            log_info "副本集已初始化，跳过集群初始化"
        else
            log_warn "副本集未初始化，将执行初始化步骤"
            start_mongodb_cluster
            initialize_replica_set
            create_mongodb_users
        fi

        release_lock
        exit 0
    fi

    # 对每个主机进行部署
    for host in "${MONGODB_HOSTS[@]}"; do
        log_info "开始在主机 $host 上部署MongoDB..."

        # 检查是否已安装
        local already_installed=false
        if remote_execute "$host" "test -f $MONGODB_HOME/bin/mongod" 2>/dev/null; then
            if [[ "$FORCE_REINSTALL" != "true" ]]; then
                log_info "MongoDB已安装在 $host，跳过安装"
                already_installed=true
            else
                log_info "强制重新安装MongoDB在 $host"
            fi
        fi

        # MongoDB安装阶段
        if [[ "$already_installed" != "true" && "$SKIP_INSTALL" != "true" ]]; then
            # 准备环境
            prepare_mongodb_environment "$host"

            # 安装MongoDB（优先本地仓库）
            if ! install_mongodb_dependencies "$host"; then
                log_error "MongoDB安装失败，跳过主机 $host"
                continue
            fi

            # 配置MongoDB环境
            install_mongodb_binary "$host"
        elif [[ "$SKIP_INSTALL" == "true" ]]; then
            log_warn "跳过MongoDB安装"
        fi

        log_info "✓ 主机 $host 上的MongoDB部署完成"
    done
    
    # 配置阶段
    if [[ "$SKIP_CONFIG" != "true" ]]; then
        for host in "${MONGODB_HOSTS[@]}"; do
            generate_mongodb_config "$host"
            create_mongodb_service "$host"
        done
    else
        log_warn "跳过MongoDB配置"
    fi
    
    # 集群初始化阶段
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_mongodb_cluster
        initialize_replica_set
        create_mongodb_users
    else
        log_warn "跳过MongoDB集群初始化"
    fi
    
    log_info "MongoDB集群部署完成！"

    # 释放锁
    release_lock

    # 输出连接信息
    echo
    echo "=== MongoDB集群信息 ==="
    echo "副本集名称: $MONGODB_REPLICA_SET"
    echo "主机列表: ${MONGODB_HOSTS[*]}"
    echo "端口: $MONGODB_PORT"
    echo "配置文件: $MONGODB_CONFIG_DIR/mongod.conf"
    echo "密码文件: $MONGODB_CONFIG_DIR/passwords.txt"
    echo "========================"

    return 0
}

# 设置脚本权限
chmod +x "$0"

# 执行主函数
main "$@"
