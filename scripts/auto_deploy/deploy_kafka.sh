#!/bin/bash
# Kafka集群部署脚本 - Docker版本
# 遵循DevOps最佳实践，自动化Kafka集群部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="Kafka集群部署脚本 (Docker版本)"
SCRIPT_VERSION="2.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# Docker特定配置
# =============================================================================

# Docker镜像配置 - KRaft模式 (无需Zookeeper)
# 使用包含 shell 脚本的自定义镜像
KAFKA_IMAGE="apache/kafka:3.9.0"

# 目录配置
DOCKER_IMAGES_DIR="/apps/software/docker-images"
DOCKER_COMPOSE_DIR="/apps/software/docker-compose"
KAFKA_DATA_DIR="/apps/data/kafka"
KAFKA_LOGS_DIR="/apps/logs/kafka"

# 端口配置 - KRaft模式
KAFKA_PORT="9092"
KAFKA_CONTROLLER_PORT="9093"
KAFKA_INTERNAL_PORT="19092"

# KRaft 集群配置
KAFKA_CLUSTER_ID=""  # 将在运行时生成

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INSTALL=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false
CLUSTER_MODE="single"  # single, cluster

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --cluster-mode)
            CLUSTER_MODE="cluster"
            shift
            ;;
        --single-mode)
            CLUSTER_MODE="single"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install      跳过安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过集群初始化"
            echo "  --force-reinstall   强制重新安装（清理所有数据）"
            echo "  --dry-run          仅显示将要执行的操作"
            echo "  --cluster-mode     集群模式部署"
            echo "  --single-mode      单机模式部署（默认）"
            echo "  -h, --help         显示此帮助信息"
            echo ""
            echo "说明:"
            echo "  本脚本使用 Apache Kafka Native 3.9.0 镜像"
            echo "  采用 KRaft 模式，无需 Zookeeper"
            echo "  使用 docker-compose 进行部署"
            echo ""
            echo "注意事项:"
            echo "  --force-reinstall 会删除所有现有的Kafka数据和日志"
            echo "  如果遇到集群ID冲突错误，请使用此参数重新部署"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# Docker环境检查函数
# =============================================================================

check_docker_environment() {
    local host=$1

    log_info "检查 $host 上的Docker环境..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将检查 $host 上的Docker环境"
        return 0
    fi

    # 检查Docker是否已安装并运行
    if ! remote_execute "$host" "docker --version && systemctl is-active docker" >/dev/null 2>&1; then
        log_error "Docker未安装或未运行在 $host 上"
        log_info "请确保Docker已安装并运行"
        return 1
    fi

    # 检查docker-compose是否可用
    if ! remote_execute "$host" "docker-compose --version || docker compose version" >/dev/null 2>&1; then
        log_error "docker-compose未安装在 $host 上"
        log_info "请安装docker-compose或使用较新版本的Docker（内置compose插件）"
        return 1
    fi

    log_info "✓ Docker环境检查通过: $host"
}

# =============================================================================
# Kafka Docker部署函数
# =============================================================================

load_kafka_image() {
    local host=$1

    log_info "在 $host 上加载Kafka Docker镜像..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上加载Kafka Docker镜像"
        return 0
    fi

    # 检查镜像是否已存在
    if remote_execute "$host" "docker images -q $KAFKA_IMAGE" | grep -q . && [[ "$FORCE_REINSTALL" != "true" ]]; then
        log_info "Kafka镜像已存在于 $host，跳过加载"
        return 0
    fi

    # 生成镜像文件名
    local image_filename=$(echo "$KAFKA_IMAGE" | tr '/:' '_').tar.gz

    # 检查镜像文件是否存在
    if ! remote_execute "$host" "test -f $DOCKER_IMAGES_DIR/$image_filename"; then
        log_warn "镜像文件不存在: $DOCKER_IMAGES_DIR/$image_filename"
        log_info "请先运行分发脚本来分发Docker镜像"
        return 1
    fi

    # 加载Docker镜像
    log_info "加载镜像文件: $image_filename"
    remote_execute "$host" "docker load < $DOCKER_IMAGES_DIR/$image_filename"

    log_info "✓ Kafka Docker镜像加载完成: $host"
}

generate_docker_compose_config() {
    local host=$1
    local node_id=$2

    log_info "为 $host 生成docker-compose配置..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成docker-compose配置"
        return 0
    fi

    # 创建配置目录
    remote_execute "$host" "mkdir -p $DOCKER_COMPOSE_DIR $KAFKA_DATA_DIR $KAFKA_LOGS_DIR"

    # 生成集群ID（如果还没有）
    if [[ -z "$KAFKA_CLUSTER_ID" ]]; then
        # 使用 uuidgen 或 python 生成 UUID，避免捕获日志输出
        local uuid_command="
            if command -v uuidgen >/dev/null 2>&1; then
                uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]'
            elif command -v python3 >/dev/null 2>&1; then
                python3 -c 'import uuid; print(str(uuid.uuid4()).replace(\"-\", \"\"))'
            else
                # 备用方法：使用随机数生成
                echo \$(date +%s)\$(shuf -i 1000-9999 -n 1) | md5sum | cut -d' ' -f1
            fi
        "

        # 直接通过SSH执行命令，避免remote_execute的日志输出
        KAFKA_CLUSTER_ID=$(ssh -o ConnectTimeout=${SSH_TIMEOUT:-30} -o BatchMode=yes "$DEPLOY_USER@$host" "$uuid_command" 2>/dev/null | tr -d '\r\n')

        # 验证生成的UUID格式（应该是32位十六进制字符）
        if [[ ! "$KAFKA_CLUSTER_ID" =~ ^[a-f0-9]{32}$ ]]; then
            log_warn "生成的集群ID格式不正确，使用备用方法"
            # 备用方法：本地生成UUID
            if command -v uuidgen >/dev/null 2>&1; then
                KAFKA_CLUSTER_ID=$(uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]')
            else
                # 最后备用：使用时间戳和随机数
                KAFKA_CLUSTER_ID=$(echo "$(date +%s)$(shuf -i 1000-9999 -n 1)" | md5sum | cut -d' ' -f1)
            fi
        fi

        log_info "生成的Kafka集群ID: $KAFKA_CLUSTER_ID"
    fi

    # 构建controller.quorum.voters配置
    local quorum_voters=""
    local voter_id=1
    for kafka_host in "${KAFKA_HOSTS[@]}"; do
        if [[ $voter_id -gt 1 ]]; then
            quorum_voters+=","
        fi
        quorum_voters+="$voter_id@$kafka_host:$KAFKA_CONTROLLER_PORT"
        voter_id=$((voter_id + 1))
    done

    # 生成docker-compose.yml配置 (兼容 docker-compose 1.22.0)
    local compose_config="version: '3.3'

services:
  kafka:
    image: $KAFKA_IMAGE
    hostname: kafka-$node_id
    container_name: kafka-$node_id
    user: \"1000:1000\"  # 明确指定用户ID，确保与目录权限一致
    ports:
      - \"$KAFKA_PORT:9092\"
      - \"$KAFKA_CONTROLLER_PORT:9093\"
    environment:
      KAFKA_NODE_ID: $node_id
      KAFKA_PROCESS_ROLES: 'broker,controller'
      KAFKA_CONTROLLER_QUORUM_VOTERS: '$quorum_voters'
      KAFKA_LISTENERS: 'PLAINTEXT://:$KAFKA_INTERNAL_PORT,CONTROLLER://:$KAFKA_CONTROLLER_PORT,PLAINTEXT_HOST://:$KAFKA_PORT'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'PLAINTEXT'
      KAFKA_ADVERTISED_LISTENERS: 'PLAINTEXT_HOST://$host:$KAFKA_PORT'
      KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
      KAFKA_LOG_DIRS: '/var/lib/kafka/data'
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_NUM_PARTITIONS: 3
      CLUSTER_ID: '$KAFKA_CLUSTER_ID'
    volumes:
      - $KAFKA_DATA_DIR:/var/lib/kafka/data:rw
      - $KAFKA_LOGS_DIR:/var/log/kafka:rw
    restart: unless-stopped
    # apache/kafka-native 镜像会根据环境变量自动配置 KRaft 模式
    # 不需要自定义启动命令

networks:
  default:
    driver: bridge"

    # 写入docker-compose.yml文件
    remote_execute "$host" "
        cat > $DOCKER_COMPOSE_DIR/docker-compose.yml << 'EOF'
$compose_config
EOF
    "

    log_info "✓ docker-compose配置生成完成: $host"
}

deploy_kafka_cluster() {
    local host=$1
    local node_id=$2

    log_info "在 $host 上部署Kafka集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上部署Kafka集群"
        return 0
    fi

    # 停止已存在的服务
    remote_execute "$host" "
        cd $DOCKER_COMPOSE_DIR
        if [[ -f docker-compose.yml ]]; then
            # 尝试使用docker-compose或docker compose
            if command -v docker-compose >/dev/null 2>&1; then
                docker-compose down --remove-orphans 2>/dev/null || true
            else
                docker compose down --remove-orphans 2>/dev/null || true
            fi
        fi
    "

    # 如果是强制重新安装，清理数据目录
    if [[ "$FORCE_REINSTALL" == "true" ]]; then
        log_info "强制重新安装模式：清理Kafka数据目录..."
        remote_execute "$host" "
            if [[ -d $KAFKA_DATA_DIR ]]; then
                echo '清理Kafka数据目录: $KAFKA_DATA_DIR'
                rm -rf $KAFKA_DATA_DIR/*
                echo '✓ 数据目录清理完成'
            fi

            if [[ -d $KAFKA_LOGS_DIR ]]; then
                echo '清理Kafka日志目录: $KAFKA_LOGS_DIR'
                rm -rf $KAFKA_LOGS_DIR/*
                echo '✓ 日志目录清理完成'
            fi
        "
    fi

    # 创建必要的目录并设置权限
    remote_execute "$host" "
        mkdir -p $KAFKA_DATA_DIR $KAFKA_LOGS_DIR

        # 检查是否存在旧的集群数据（非强制重新安装模式）
        if [[ -f $KAFKA_DATA_DIR/meta.properties && '$FORCE_REINSTALL' != 'true' ]]; then
            echo '=== 检测到现有Kafka数据 ==='
            echo '发现现有的meta.properties文件，可能导致集群ID冲突'
            echo '如需重新部署，请使用 --force-reinstall 参数'
            echo '当前数据目录内容:'
            ls -la $KAFKA_DATA_DIR/
            if [[ -f $KAFKA_DATA_DIR/meta.properties ]]; then
                echo '现有meta.properties内容:'
                cat $KAFKA_DATA_DIR/meta.properties
            fi
        fi

        # 设置目录权限和所有者 (apache/kafka-native 镜像使用 uid=1000 gid=1000)
        chown -R 1000:1000 $KAFKA_DATA_DIR $KAFKA_LOGS_DIR
        chmod -R 755 $KAFKA_DATA_DIR $KAFKA_LOGS_DIR

        # 验证权限设置
        echo '=== 目录权限验证 ==='
        ls -la $KAFKA_DATA_DIR
        ls -la $KAFKA_LOGS_DIR

        # 确保目录可写
        if ! sudo -u '#1000' test -w $KAFKA_DATA_DIR; then
            echo 'ERROR: Kafka数据目录不可写，重新设置权限...'
            chown -R 1000:1000 $KAFKA_DATA_DIR
            chmod -R 755 $KAFKA_DATA_DIR
        fi

        if ! sudo -u '#1000' test -w $KAFKA_LOGS_DIR; then
            echo 'ERROR: Kafka日志目录不可写，重新设置权限...'
            chown -R 1000:1000 $KAFKA_LOGS_DIR
            chmod -R 755 $KAFKA_LOGS_DIR
        fi

        echo '✓ 目录权限验证完成'
    "

    # 启动服务
    log_info "启动Kafka服务..."
    remote_execute "$host" "
        cd $DOCKER_COMPOSE_DIR

        # 使用docker-compose或docker compose
        if command -v docker-compose >/dev/null 2>&1; then
            docker-compose up -d
        else
            docker compose up -d
        fi
    "

    # 等待服务启动
    log_info "等待Kafka服务启动..."
    sleep 30

    # 检查服务状态
    remote_execute "$host" "
        cd $DOCKER_COMPOSE_DIR

        echo '=== 容器状态 ==='
        if command -v docker-compose >/dev/null 2>&1; then
            docker-compose ps
        else
            docker compose ps
        fi

        echo '=== 容器日志 ==='
        docker logs kafka-$node_id --tail 20

        echo '=== 容器内部权限检查 ==='
        docker exec kafka-$node_id ls -la /var/lib/kafka/ || echo '容器未运行或无法访问'
        docker exec kafka-$node_id whoami || echo '无法获取容器用户信息'
    "

    log_info "✓ Kafka集群部署完成: $host"
}

verify_kafka_cluster() {
    local first_host="${KAFKA_HOSTS[0]}"

    log_info "验证Kafka集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将验证Kafka集群"
        return 0
    fi

    # 等待集群完全启动
    log_info "等待Kafka集群完全启动..."
    sleep 60

    # 获取第一个节点的容器名称
    local kafka_container_name="kafka-1"

    # 检查容器状态
    log_info "检查容器状态..."
    remote_execute "$first_host" "
        cd $DOCKER_COMPOSE_DIR

        echo '=== Docker Compose 版本 ==='
        if command -v docker-compose >/dev/null 2>&1; then
            docker-compose --version
            docker-compose ps
        else
            docker compose version
            docker compose ps
        fi

        echo '=== 容器运行状态 ==='
        docker ps --filter name=kafka
    "

    # 检查Kafka是否准备就绪
    log_info "检查Kafka服务状态..."
    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        log_info "尝试连接Kafka... ($attempt/$max_attempts)"

        if remote_execute "$first_host" "
            docker exec $kafka_container_name \
                /opt/kafka/bin/kafka-broker-api-versions.sh --bootstrap-server localhost:9092
        " >/dev/null 2>&1; then
            log_info "✓ Kafka服务已就绪"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "Kafka服务未能在预期时间内启动"
            # 显示容器日志以便调试
            remote_execute "$first_host" "
                echo '=== Kafka容器日志 ==='
                docker logs $kafka_container_name --tail 50
            "
            return 1
        fi

        sleep 10
        attempt=$((attempt + 1))
    done

    # 创建测试主题
    log_info "创建测试主题..."
    if remote_execute "$first_host" "
        docker exec $kafka_container_name \
            /opt/kafka/bin/kafka-topics.sh --create \
            --topic test-topic \
            --partitions 3 \
            --replication-factor 3 \
            --bootstrap-server localhost:9092
    "; then
        log_info "✓ 测试主题创建成功"
    else
        log_warn "测试主题创建失败，可能已存在"
    fi

    # 列出主题验证集群
    log_info "验证主题列表..."
    if remote_execute "$first_host" "
        docker exec $kafka_container_name \
            /opt/kafka/bin/kafka-topics.sh --list \
            --bootstrap-server localhost:9092
    "; then
        log_info "✓ Kafka集群验证成功"
    else
        log_error "Kafka集群验证失败"
        return 1
    fi

    # 测试生产和消费消息
    log_info "测试消息生产和消费..."
    remote_execute "$first_host" "
        # 生产测试消息
        echo 'test-message-$(date)' | docker exec -i $kafka_container_name \
            /opt/kafka/bin/kafka-console-producer.sh \
            --topic test-topic \
            --bootstrap-server localhost:9092

        # 消费测试消息
        timeout 10 docker exec $kafka_container_name \
            /opt/kafka/bin/kafka-console-consumer.sh \
            --topic test-topic \
            --from-beginning \
            --max-messages 1 \
            --bootstrap-server localhost:9092 || true
    "

    # 清理测试主题
    log_info "清理测试主题..."
    remote_execute "$first_host" "
        docker exec $kafka_container_name \
            /opt/kafka/bin/kafka-topics.sh --delete \
            --topic test-topic \
            --bootstrap-server localhost:9092
    " || log_warn "测试主题删除失败（可能已不存在）"

    # 显示集群信息
    log_info "=== Kafka集群部署完成 ==="
    log_info "集群模式: $CLUSTER_MODE"
    log_info "Kafka端口: $KAFKA_PORT"
    log_info "Controller端口: $KAFKA_CONTROLLER_PORT"
    log_info "集群ID: $KAFKA_CLUSTER_ID"
    log_info "数据目录: $KAFKA_DATA_DIR"
    log_info "日志目录: $KAFKA_LOGS_DIR"
    log_info ""
    log_info "管理命令示例:"
    log_info "  # 查看主题列表"
    log_info "  docker exec kafka-1 /opt/kafka/bin/kafka-topics.sh --list --bootstrap-server localhost:9092"
    log_info "  # 创建主题"
    log_info "  docker exec kafka-1 /opt/kafka/bin/kafka-topics.sh --create --topic my-topic --partitions 3 --replication-factor 3 --bootstrap-server localhost:9092"
    log_info "  # 查看容器状态"
    log_info "  cd $DOCKER_COMPOSE_DIR && docker-compose ps"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始Kafka集群部署 (KRaft模式)..."

    # 获取锁
    if ! acquire_lock "deploy_kafka"; then
        log_error "无法获取锁，可能有其他Kafka部署实例正在运行"
        exit 1
    fi

    # 检查Kafka主机配置
    if [[ ${#KAFKA_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置Kafka主机"
        exit 1
    fi

    log_info "Kafka集群主机: ${KAFKA_HOSTS[*]}"
    log_info "部署模式: $CLUSTER_MODE"
    log_info "使用镜像: $KAFKA_IMAGE"

    # 环境检查和镜像加载阶段
    if [[ "$SKIP_INSTALL" != "true" ]]; then
        for host in "${KAFKA_HOSTS[@]}"; do
            check_docker_environment "$host"
            load_kafka_image "$host"
        done
    else
        log_warn "跳过Docker环境检查和Kafka镜像加载"
    fi

    # 配置和部署阶段
    if [[ "$SKIP_CONFIG" != "true" ]]; then
        local node_id=1
        for host in "${KAFKA_HOSTS[@]}"; do
            generate_docker_compose_config "$host" "$node_id"
            deploy_kafka_cluster "$host" "$node_id"
            node_id=$((node_id + 1))
        done
    else
        log_warn "跳过Kafka配置和部署"
    fi

    # 验证阶段
    if [[ "$SKIP_INIT" != "true" ]]; then
        verify_kafka_cluster
    else
        log_warn "跳过Kafka集群验证"
    fi

    release_lock "deploy_kafka"
    log_info "=== Kafka集群部署完成 ==="
}

# 执行主函数
main "$@"

