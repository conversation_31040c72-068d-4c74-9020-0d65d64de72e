#!/bin/bash
# 修复脚本文件的换行符问题
# 将Windows换行符(CRLF)转换为Linux换行符(LF)

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "=== 检查和修复换行符问题 ==="
echo "脚本目录: $SCRIPT_DIR"

# 检查是否有dos2unix命令
if command -v dos2unix >/dev/null 2>&1; then
    echo "使用dos2unix命令修复换行符..."
    DOS2UNIX_CMD="dos2unix"
elif command -v sed >/dev/null 2>&1; then
    echo "使用sed命令修复换行符..."
    DOS2UNIX_CMD="sed -i 's/\r$//' "
else
    echo "错误: 未找到dos2unix或sed命令"
    exit 1
fi

# 查找所有需要修复的文件
echo "查找需要修复的脚本文件..."
find "$SCRIPT_DIR" -type f \( -name "*.sh" -o -name "*.conf" \) -print0 | while IFS= read -r -d '' file; do
    echo "检查文件: $file"
    
    # 检查文件是否包含CRLF
    if file "$file" | grep -q "CRLF"; then
        echo "  发现CRLF换行符，正在修复..."
        if [[ "$DOS2UNIX_CMD" == "dos2unix" ]]; then
            dos2unix "$file"
        else
            sed -i 's/\r$//' "$file"
        fi
        echo "  ✓ 修复完成"
    else
        echo "  ✓ 换行符正常"
    fi
    
    # 确保脚本文件有执行权限
    if [[ "$file" == *.sh ]]; then
        chmod +x "$file"
        echo "  ✓ 设置执行权限"
    fi
done

echo "=== 换行符修复完成 ==="

# 验证主要脚本文件
echo "=== 验证主要脚本文件 ==="
main_scripts=(
    "$SCRIPT_DIR/deploy_redis.sh"
    "$SCRIPT_DIR/lib/common.sh"
    "$SCRIPT_DIR/config/hosts.conf"
    "$SCRIPT_DIR/config/redis.conf"
    "$SCRIPT_DIR/config/global.conf"
)

for script in "${main_scripts[@]}"; do
    if [[ -f "$script" ]]; then
        echo "验证: $script"
        if file "$script" | grep -q "CRLF"; then
            echo "  ✗ 仍然包含CRLF换行符"
        else
            echo "  ✓ 换行符正常"
        fi
        
        # 检查语法（仅对.sh文件）
        if [[ "$script" == *.sh ]]; then
            if bash -n "$script" 2>/dev/null; then
                echo "  ✓ 语法检查通过"
            else
                echo "  ✗ 语法检查失败"
                bash -n "$script"
            fi
        fi
    else
        echo "文件不存在: $script"
    fi
done

echo "=== 验证完成 ==="
