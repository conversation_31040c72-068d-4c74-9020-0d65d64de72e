# MongoDB部署脚本更新文档

## 概述

本文档记录了MongoDB部署脚本从源码编译安装方式调整为镜像仓库安装方式的详细修改内容。参照Redis部署脚本的镜像仓库安装方案，将MongoDB版本从4.4.18更新为4.0.23，并采用统一的镜像仓库安装策略。

## 修改目标

1. **版本更新**: 将MongoDB版本从4.4.18更新为4.0.23
2. **安装方式**: 从源码编译安装改为镜像仓库安装
3. **功能对齐**: 与Redis部署脚本保持一致的功能和参数
4. **卸载支持**: 添加完整的卸载功能

## 主要修改内容

### 1. 版本配置更新

#### 文件: `scripts/auto_deploy/config/global.conf`
```bash
# 修改前
export MONGODB_VERSION="4.4.18"

# 修改后  
export MONGODB_VERSION="4.0.23"
```

#### 文件: `scripts/auto_deploy/config/mongodb.conf`
```bash
# 修改前
export MONGODB_VERSION="4.4.18"

# 修改后
export MONGODB_VERSION="4.0.23"
```

### 2. 脚本配置调整

#### 文件: `scripts/auto_deploy/deploy_mongodb.sh`

**配置变量更新:**
```bash
# 修改前
# 编译配置
MONGODB_SOURCE_DIR="/apps/software/mongodb"
MONGODB_BUILD_DIR="/tmp/mongodb-build"

# 修改后
# 二进制包配置
MONGODB_PACKAGE_DIR="/apps/software/mongodb"
MONGODB_BINARY_PACKAGE="mongodb-4.0.23.tar.gz"
```

**参数解析更新:**
```bash
# 新增参数
SKIP_INSTALL=false      # 替代原来的SKIP_COMPILE
UNINSTALL=false         # 新增卸载功能
BACKUP_DATA=true        # 新增备份选项

# 新增命令行选项
--skip-install          # 跳过二进制包安装
--uninstall            # 卸载MongoDB集群
--no-backup            # 卸载时不备份数据
```

### 3. 新增镜像仓库安装函数

#### 恢复原始YUM仓库配置
```bash
restore_original_yum_repos() {
    # 禁用local.repo避免冲突
    # 恢复备份的net.repo配置
    # 重建YUM缓存
}
```

#### 从本地仓库安装MongoDB
```bash
install_mongodb_from_local_repo() {
    # 检查可用的MongoDB版本
    # 优先选择4.0.23或4.0.x系列
    # 安装mongodb-org-server、mongodb-org-shell、mongodb-org-tools
}
```

#### 安装MongoDB依赖
```bash
install_mongodb_dependencies() {
    # 优先从本地仓库安装MongoDB
    # 安装运行时依赖包：openssl-libs、libcurl、cyrus-sasl等
}
```

#### 配置MongoDB二进制环境
```bash
install_mongodb_binary() {
    # 检测YUM安装的MongoDB
    # 创建符号链接到标准路径
    # 生成配置模板
    # 设置环境变量
}
```

### 4. 新增完整卸载功能

#### 数据备份
```bash
backup_mongodb_data() {
    # 备份数据文件、配置文件、日志文件
    # 创建备份信息文件
}
```

#### 服务管理
```bash
stop_mongodb_services() {
    # 停止并禁用MongoDB服务
    # 强制终止残留进程
}

remove_mongodb_services() {
    # 删除systemd服务文件
    # 重新加载systemd配置
}
```

#### 文件清理
```bash
remove_mongodb_files() {
    # 卸载YUM安装的MongoDB包
    # 删除安装目录、数据目录、日志目录
    # 清理临时文件
}

remove_mongodb_user() {
    # 删除MongoDB用户和组
}

cleanup_mongodb_environment() {
    # 清理环境变量
    # 恢复仓库配置
    # 清理cron任务
}
```

### 5. 环境准备函数优化

#### 简化环境准备
```bash
prepare_mongodb_environment() {
    # 移除编译依赖安装
    # 增强目录结构验证
    # 添加权限检查
    # 添加磁盘空间检查
}
```

#### 服务检查简化
```bash
check_mongodb_installed() {
    # 简化为只检查服务运行状态
    # 移除复杂的二进制文件检查
}
```

### 6. 主函数流程调整

#### 新增卸载模式检查
```bash
main() {
    # 检查是否为卸载模式
    if [[ "$UNINSTALL" == "true" ]]; then
        uninstall_mongodb
        exit 0
    fi
    # ... 其他逻辑
}
```

#### 安装流程优化
```bash
# 修改前：编译安装阶段
if [[ "$SKIP_COMPILE" != "true" ]]; then
    prepare_mongodb_environment "$host"
    compile_mongodb "$host"
fi

# 修改后：镜像仓库安装阶段
if [[ "$SKIP_INSTALL" != "true" ]]; then
    prepare_mongodb_environment "$host"
    install_mongodb_dependencies "$host"
    install_mongodb_binary "$host"
fi
```

## 配置兼容性

### MongoDB 4.0.23配置调整

保持WiredTiger存储引擎配置，确保与4.0.23版本兼容：
```yaml
storage:
  dbPath: /apps/data/mongodb/data
  journal:
    enabled: true
    commitIntervalMs: 100
  wiredTiger:
    engineConfig:
      cacheSizeGB: 16
      journalCompressor: snappy
      directoryForIndexes: true
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true
```

## 测试验证

创建了完整的测试脚本 `test/test_mongodb_deploy.sh`，验证以下功能：

1. ✅ MongoDB版本配置正确性
2. ✅ 脚本参数解析功能
3. ✅ 镜像仓库安装函数存在性
4. ✅ 卸载功能完整性
5. ✅ 配置文件生成正确性
6. ✅ DRY RUN模式功能

## 使用方法

### 安装MongoDB集群
```bash
# 完整安装
./deploy_mongodb.sh

# 跳过安装，仅配置
./deploy_mongodb.sh --skip-install

# 强制重新安装
./deploy_mongodb.sh --force-reinstall

# 干运行模式
./deploy_mongodb.sh --dry-run
```

### 卸载MongoDB集群
```bash
# 卸载并备份数据
./deploy_mongodb.sh --uninstall

# 卸载不备份数据
./deploy_mongodb.sh --uninstall --no-backup

# 干运行卸载
./deploy_mongodb.sh --uninstall --dry-run
```

## 总结

通过本次更新，MongoDB部署脚本已成功调整为镜像仓库安装方案，具备以下优势：

1. **安装速度快**: 无需编译，直接从镜像仓库安装
2. **版本稳定**: 使用经过测试的MongoDB 4.0.23版本
3. **功能完整**: 支持安装、配置、初始化、卸载全流程
4. **操作简便**: 与Redis脚本保持一致的参数和使用方式
5. **环境适配**: 适配openEuler-20.03-LTS环境的镜像仓库

所有修改已通过测试验证，可以安全部署使用。
