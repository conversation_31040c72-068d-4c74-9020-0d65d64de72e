# Kafka 部署问题修复文档

## 问题描述

在使用 `deploy_kafka.sh` 脚本部署 Kafka 集群时遇到以下问题：

1. **权限问题**: 容器无法写入数据目录，出现 `java.nio.file.AccessDeniedException` 错误
2. **集群ID冲突**: 强制重新安装时，旧数据导致集群ID不匹配错误

## 修复内容

### 1. 权限问题修复

**问题原因**: `apache/kafka-native` 镜像以 `appuser` (uid=1000) 运行，但挂载的数据目录属于 root 用户。

**修复方案**:
- 在创建目录时设置正确的所有者: `chown -R 1000:1000`
- 在 docker-compose.yml 中明确指定用户: `user: "1000:1000"`
- 添加权限验证和自动修复机制

### 2. 数据清理功能

**问题原因**: 强制重新安装时，旧的 `meta.properties` 文件包含不同的集群ID。

**修复方案**:
- 在 `--force-reinstall` 模式下自动清理数据目录
- 添加数据冲突检测和提示
- 更新帮助信息说明数据清理行为

## 使用方法

### 全新部署
```bash
# 正常部署
./deploy_kafka.sh

# 集群模式部署
./deploy_kafka.sh --cluster-mode
```

### 强制重新安装（清理数据）
```bash
# 强制重新安装，会清理所有现有数据
./deploy_kafka.sh --force-reinstall

# 强制重新安装 + 集群模式
./deploy_kafka.sh --force-reinstall --cluster-mode
```

### 测试部署
```bash
# 运行测试脚本
./test/test_kafka_deployment.sh

# 干运行模式
./deploy_kafka.sh --dry-run
```

## 验证部署

### 1. 检查容器状态
```bash
docker ps --filter name=kafka
```

### 2. 查看容器日志
```bash
docker logs kafka-1 --tail 50
docker logs kafka-2 --tail 50
docker logs kafka-3 --tail 50
```

### 3. 检查权限
```bash
# 检查主机目录权限
ls -la /apps/data/kafka
ls -la /apps/logs/kafka

# 检查容器内权限
docker exec kafka-1 ls -la /var/lib/kafka/
docker exec kafka-1 whoami
```

### 4. 测试Kafka功能
```bash
# 列出主题
docker exec kafka-1 kafka-topics.sh --list --bootstrap-server localhost:9092

# 创建测试主题
docker exec kafka-1 kafka-topics.sh --create --topic test --partitions 3 --replication-factor 3 --bootstrap-server localhost:9092

# 生产消息
echo "test message" | docker exec -i kafka-1 kafka-console-producer.sh --topic test --bootstrap-server localhost:9092

# 消费消息
docker exec kafka-1 kafka-console-consumer.sh --topic test --from-beginning --max-messages 1 --bootstrap-server localhost:9092
```

## 故障排除

### 权限问题
如果仍然遇到权限问题：
```bash
# 手动修复权限
sudo chown -R 1000:1000 /apps/data/kafka /apps/logs/kafka
sudo chmod -R 755 /apps/data/kafka /apps/logs/kafka
```

### 集群ID冲突
如果遇到集群ID冲突：
```bash
# 使用强制重新安装
./deploy_kafka.sh --force-reinstall
```

### 容器无法启动
```bash
# 检查Docker环境
docker --version
docker-compose --version

# 检查镜像
docker images | grep kafka

# 重新加载镜像
docker load < /apps/software/docker-images/apache_kafka-native_3.9.0.tar.gz
```

## 注意事项

1. **数据安全**: `--force-reinstall` 会删除所有现有数据，请谨慎使用
2. **网络配置**: 确保防火墙允许 9092 和 9093 端口
3. **资源要求**: 每个 Kafka 节点建议至少 8GB 内存
4. **集群部署**: 建议在生产环境使用 3 节点集群配置

## 相关文件

- `deploy_kafka.sh`: 主部署脚本
- `test/test_kafka_deployment.sh`: 测试脚本
- `config/hosts.conf`: 主机配置文件
- `config/global.conf`: 全局配置文件
