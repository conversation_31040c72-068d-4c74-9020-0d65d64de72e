# 工业场景基础服务自动化部署系统

> **遵循DevOps最佳实践，专为银河麒麟+海光CPU环境设计的企业级自动化部署解决方案**

## 🎯 系统概述

本自动化部署系统基于DevOps最佳实践设计，专门针对工业场景的高可用需求，支持以下基础服务的一键部署：

- **数据库服务**：MongoDB 4.x、达梦数据库 V8
- **缓存服务**：Redis 7.x 集群
- **消息队列**：Kafka 2.8.x 集群
- **专业数据库**：TDEngine 3.x、NebulaGraph 3.x
- **监控系统**：Prometheus + Grafana + AlertManager

## 脚本列表

### 主部署脚本
- `deploy_all.sh` - 一键部署所有基础服务
- `deploy_infrastructure.sh` - 部署基础环境
- `deploy_monitoring.sh` - 部署监控系统

### 数据库服务部署
- `deploy_mongodb.sh` - MongoDB集群自动部署
- `deploy_redis.sh` - Redis集群自动部署
- `deploy_dameng.sh` - 达梦数据库集群部署

### 消息队列部署
- `deploy_kafka.sh` - Kafka集群自动部署

### 专业数据库部署
- `deploy_tdengine.sh` - TDEngine集群部署
- `deploy_nebula.sh` - NebulaGraph集群部署

### 辅助脚本
- `prepare_environment.sh` - 环境准备脚本
- `distribute_packages.sh` - 安装包分发脚本
- `configure_network.sh` - 网络配置脚本

## 部署流程

### 1. 环境准备
```bash
# 准备部署环境
./prepare_environment.sh

# 分发安装包
./distribute_packages.sh
```

### 2. 基础服务部署
```bash
# 部署核心数据库
./deploy_dameng.sh
./deploy_mongodb.sh

# 部署缓存服务
./deploy_redis.sh

# 部署消息队列
./deploy_kafka.sh

# 部署专业数据库
./deploy_tdengine.sh
./deploy_nebula.sh
```

### 3. 监控系统部署
```bash
# 部署监控系统
./deploy_monitoring.sh
```

### 4. 一键部署（推荐）
```bash
# 执行完整部署
./deploy_all.sh
```

## 配置文件

部署脚本使用配置文件来管理部署参数：

### 主配置文件
- `config/global.conf` - 全局配置
- `config/hosts.conf` - 主机配置
- `config/network.conf` - 网络配置

### 服务配置文件
- `config/mongodb.conf` - MongoDB部署配置
- `config/redis.conf` - Redis部署配置
- `config/kafka.conf` - Kafka部署配置
- `config/tdengine.conf` - TDEngine部署配置
- `config/nebula.conf` - NebulaGraph部署配置
- `config/dameng.conf` - 达梦数据库部署配置

## 配置示例

### 全局配置 (global.conf)
```bash
# 基础配置
DEPLOY_USER="root"
DEPLOY_KEY="/root/.ssh/id_rsa"
SOFTWARE_REPO="/apps/software-repo"
BACKUP_DIR="/backup"

# 环境配置
OS_VERSION="kylin_v10"
CPU_ARCH="x86_64"
TIMEZONE="Asia/Shanghai"

# 安全配置
ENABLE_FIREWALL="false"
ENABLE_SELINUX="false"
```

### 主机配置 (hosts.conf)
```bash
# MongoDB集群
MONGODB_HOSTS="*************,*************,*************"

# Redis集群
REDIS_HOSTS="*************,*************,*************,*************,*************,*************"

# Kafka集群
KAFKA_HOSTS="*************,*************,*************"

# TDEngine集群
TDENGINE_HOSTS="*************,*************,*************"

# NebulaGraph集群
NEBULA_HOSTS="*************,*************,*************"

# 达梦数据库集群
DAMENG_HOSTS="*************,*************,*************"

# 监控服务器
MONITOR_HOSTS="*************,*************"
```

## 部署特性

### 自动化特性
- **环境检查**：自动检查系统环境和依赖
- **包分发**：自动分发安装包到目标服务器
- **配置生成**：根据模板自动生成配置文件
- **服务启动**：自动启动和验证服务
- **集群初始化**：自动初始化集群配置

### 安全特性
- **SSH密钥认证**：使用SSH密钥进行安全连接
- **权限控制**：最小权限原则
- **配置加密**：敏感配置信息加密存储
- **审计日志**：详细的部署日志记录

### 容错特性
- **断点续传**：支持从失败点继续部署
- **回滚机制**：部署失败时自动回滚
- **健康检查**：部署后自动验证服务状态
- **错误处理**：详细的错误信息和处理建议

## 🚀 快速开始

### 1. 环境要求

- **操作系统**：银河麒麟 V10 SP1/SP2
- **CPU架构**：海光CPU（x86_64，不支持AVX）
- **内存**：每台服务器至少16GB
- **存储**：系统盘50GB + 数据盘500GB+
- **网络**：千兆以太网，节点间互通

### 2. 配置部署参数

```bash
# 编辑全局配置
vim config/global.conf

# 编辑主机配置
vim config/hosts.conf
```

### 3. 执行一键部署

```bash
# 完整部署流程
./deploy_all.sh

# 或者分步骤部署
./prepare_environment.sh      # 环境准备
./distribute_packages.sh      # 包分发
./deploy_all.sh --skip-infrastructure  # 跳过基础设施
```

### 4. 验证部署结果

```bash
# 执行部署验证
./verify_deployment.sh

# 查看验证报告
cat logs/verification_report_*.txt
```

## 🔧 高级功能

### 并行部署

```bash
# 启用并行部署（实验性功能）
./deploy_all.sh --parallel
```

### 选择性部署

```bash
# 只部署数据库服务
./deploy_all.sh --skip-cache --skip-messaging --skip-monitoring

# 只部署特定服务
./deploy_mongodb.sh
./deploy_redis.sh
```

### 强制重新部署

```bash
# 强制重新安装所有服务
./deploy_all.sh --force-reinstall

# 强制重新部署特定服务
./deploy_mongodb.sh --force-reinstall
```

### 调试模式

```bash
# 启用详细日志
export LOG_LEVEL="DEBUG"
./deploy_all.sh

# 干运行模式（仅显示操作，不执行）
./deploy_all.sh --dry-run
```

## 🛠️ 故障排查

### 常见问题

#### 1. SSH连接失败
```bash
# 检查SSH密钥
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa
ssh-copy-id root@target_host

# 检查网络连通性
ping target_host
telnet target_host 22
```

#### 2. 编译失败（海光CPU相关）
```bash
# 确认CPU架构
lscpu | grep -i hygon

# 检查AVX支持
grep -i avx /proc/cpuinfo

# 使用正确的编译参数
export CFLAGS="-O2 -march=x86-64 -mtune=generic"
export PORTABLE=1
export USE_AVX=OFF
```

#### 3. 服务启动失败
```bash
# 检查服务状态
systemctl status service_name

# 查看服务日志
journalctl -u service_name -f

# 检查端口占用
netstat -tlnp | grep port_number
```

### 日志分析

```bash
# 查看部署日志
tail -f logs/deploy_all.log

# 查看错误日志
grep -i error logs/*.log

# 查看特定服务日志
tail -f logs/deploy_mongodb.log
```

---

**本自动化部署系统严格遵循DevOps最佳实践，为工业场景提供可靠、高效、安全的基础服务部署解决方案。**
