# Redis集群管理工具使用指南

## 概述

`redis_tools.sh` 是一个综合的Redis集群管理工具，整合了密码获取、集群状态查看、节点重启等功能，为Redis集群的日常管理提供便利。

## 功能特性

- **密码管理**: 获取和验证Redis集群密码
- **状态监控**: 查看集群状态、节点信息、槽分布
- **节点管理**: 启动、停止、重启Redis节点
- **健康检查**: 全面的集群健康状态检查
- **故障处理**: 手动故障转移、集群重置
- **多种输出格式**: 支持plain、json、table格式

## 基本用法

```bash
# 显示帮助信息
./tools/redis_tools.sh --help

# 基本命令格式
./tools/redis_tools.sh <命令> [选项]
```

## 命令详解

### 1. 密码管理

```bash
# 获取Redis密码（默认格式）
./tools/redis_tools.sh password

# 以环境变量格式输出
./tools/redis_tools.sh password -f env

# 以JSON格式输出
./tools/redis_tools.sh password -f json

# 以表格格式输出
./tools/redis_tools.sh password -f table
```

### 2. 集群状态查看

```bash
# 查看集群状态
./tools/redis_tools.sh status

# 查看集群节点信息
./tools/redis_tools.sh nodes

# 以表格形式查看节点
./tools/redis_tools.sh nodes -f table

# 查看槽分布
./tools/redis_tools.sh slots
```

### 3. 节点信息查看

```bash
# 查看Redis实例基本信息
./tools/redis_tools.sh info

# 查看详细信息
./tools/redis_tools.sh info -v

# 查看指定节点信息
./tools/redis_tools.sh info -h ***********

# 查看所有节点信息
./tools/redis_tools.sh info -a
```

### 4. 连通性测试

```bash
# 测试默认节点连通性
./tools/redis_tools.sh ping

# 测试指定节点连通性
./tools/redis_tools.sh ping -h ***********

# 测试所有节点连通性
./tools/redis_tools.sh ping -a
```

### 5. 节点管理

```bash
# 重启指定节点
./tools/redis_tools.sh restart -h ***********

# 重启所有节点
./tools/redis_tools.sh restart -a

# 停止指定节点
./tools/redis_tools.sh stop -h ***********

# 停止所有节点
./tools/redis_tools.sh stop -a

# 启动指定节点
./tools/redis_tools.sh start -h ***********

# 启动所有节点
./tools/redis_tools.sh start -a
```

### 6. 健康检查

```bash
# 执行全面健康检查
./tools/redis_tools.sh health
```

健康检查包括：
- 节点连通性检查
- 集群状态检查
- 服务状态检查

### 7. 高级管理

```bash
# 手动故障转移
./tools/redis_tools.sh failover -h ***********

# 重置集群状态（危险操作）
./tools/redis_tools.sh reset -a
```

## 通用选项

| 选项 | 说明 | 示例 |
|------|------|------|
| `-h, --host <IP>` | 指定主机IP | `-h ***********` |
| `-p, --port <端口>` | 指定端口 | `-p 6379` |
| `-a, --all` | 对所有节点执行操作 | `-a` |
| `-f, --format <格式>` | 输出格式 | `-f table` |
| `-v, --verbose` | 详细输出 | `-v` |

## 输出格式

### Plain格式（默认）
```
=== Redis集群状态 ===
cluster_state:ok
cluster_slots_assigned:16384
...
```

### JSON格式
```json
{
  "title": "Redis集群状态",
  "content": "cluster_state:ok\ncluster_slots_assigned:16384\n..."
}
```

### Table格式
```
┌─ Redis集群节点信息 ─┐
│ 主机            │ 端口 │ 角色    │ 节点ID                               │
├─────────────────┼──────┼─────────┼──────────────────────────────────────┤
│ ***********     │ 6379 │ master  │ abc123...                            │
└─────────────────┴──────┴─────────┴──────────────────────────────────────┘
```

## 常用场景

### 场景1: 日常健康检查
```bash
# 快速检查集群状态
./tools/redis_tools.sh health

# 查看所有节点连通性
./tools/redis_tools.sh ping -a

# 查看集群节点分布
./tools/redis_tools.sh nodes -f table
```

### 场景2: 故障排查
```bash
# 获取密码进行手动连接测试
./tools/redis_tools.sh password -f env

# 查看详细的节点信息
./tools/redis_tools.sh info -a -v

# 检查集群状态
./tools/redis_tools.sh status
```

### 场景3: 节点维护
```bash
# 重启有问题的节点
./tools/redis_tools.sh restart -h ***********

# 验证重启后状态
./tools/redis_tools.sh ping -h ***********

# 检查集群是否恢复正常
./tools/redis_tools.sh health
```

### 场景4: 集群管理
```bash
# 查看槽分布
./tools/redis_tools.sh slots

# 手动故障转移
./tools/redis_tools.sh failover -h ***********

# 验证故障转移结果
./tools/redis_tools.sh nodes -f table
```

## 注意事项

1. **权限要求**: 需要对目标主机有SSH访问权限
2. **配置依赖**: 依赖`hosts.conf`中的Redis主机配置
3. **安全考虑**: 密码信息会在命令行输出中显示，注意保护
4. **危险操作**: `reset`命令会清除集群状态，使用前请确认
5. **网络要求**: 确保管理主机与Redis节点网络连通

## 故障排除

### 常见错误

1. **配置文件不存在**
   ```
   [ERROR] 配置文件不存在: /apps/redis/conf/redis-6379.conf
   ```
   解决：检查Redis是否正确部署

2. **SSH连接失败**
   ```
   [ERROR] 配置文件未加载，无法执行远程命令
   ```
   解决：检查SSH密钥配置和网络连通性

3. **密码认证失败**
   ```
   [ERROR] Redis连接测试失败
   ```
   解决：检查Redis服务状态和密码配置

### 调试技巧

```bash
# 使用详细输出模式
./tools/redis_tools.sh info -v

# 检查配置文件
cat /apps/redis/conf/redis-6379.conf | grep -E "(requirepass|masterauth)"

# 手动测试Redis连接
redis-cli -h *********** -p 6379 ping
```

## 与原工具的关系

`redis_tools.sh` 整合了原有的 `get_redis_password.sh` 功能，并扩展了更多管理功能：

- 保留了所有密码获取功能
- 新增了集群状态监控
- 新增了节点管理功能
- 新增了健康检查功能
- 提供了统一的命令接口

建议使用新的 `redis_tools.sh` 替代单独的密码获取工具。
