# Redis测试文档

## 概述

本文档描述了Redis部署脚本的测试体系，包括测试组织、运行方法和验证标准。

## 测试架构

### 测试目录结构

```
test/
├── run_redis_tests.sh           # Redis测试套件入口
├── test_redis_cluster_config.sh # 集群配置测试
├── test_redis_yum_install.sh    # YUM安装测试
└── test_redis_uninstall.sh      # 卸载功能测试
```

### 测试分类

#### 1. 配置测试 (`test_redis_cluster_config.sh`)

**测试目标**: 验证Redis集群配置的正确性

**测试内容**:
- 配置文件语法检查
- 主机与NebulaGraph共享验证
- 端口配置验证
- 集群拓扑验证
- 资源分配验证
- 配置验证函数测试
- 集群节点生成测试

**运行方式**:
```bash
# 单独运行
./test/test_redis_cluster_config.sh

# 通过测试套件运行
./test/run_redis_tests.sh --config-only
```

#### 2. 安装测试 (`test_redis_yum_install.sh`)

**测试目标**: 验证Redis YUM安装功能

**测试内容**:
- 脚本语法检查
- 函数完整性验证
- 仓库恢复功能测试
- Redis版本检查测试
- YUM安装逻辑测试
- 环境兼容性测试
- 卸载YUM支持测试
- Dry-run模式测试

**运行方式**:
```bash
# 单独运行
./test/test_redis_yum_install.sh

# 通过测试套件运行
./test/run_redis_tests.sh --install-only
```

#### 3. 卸载测试 (`test_redis_uninstall.sh`)

**测试目标**: 验证Redis卸载功能的完整性

**测试内容**:
- 帮助信息验证
- 参数解析测试
- 配置文件加载测试
- 卸载函数存在性检查
- Dry-run模式测试
- 锁机制测试

**运行方式**:
```bash
# 单独运行
./test/test_redis_uninstall.sh

# 通过测试套件运行
./test/run_redis_tests.sh --uninstall-only
```

## 测试套件使用

### 基本用法

```bash
# 运行所有Redis测试
./test/run_redis_tests.sh

# 显示帮助信息
./test/run_redis_tests.sh --help
```

### 高级选项

```bash
# 详细输出模式
./test/run_redis_tests.sh --verbose

# 仅运行特定类型的测试
./test/run_redis_tests.sh --config-only
./test/run_redis_tests.sh --install-only
./test/run_redis_tests.sh --uninstall-only
```

### 测试输出

测试套件会生成以下输出：

1. **控制台输出**: 实时显示测试进度和结果
2. **测试日志**: 详细日志保存在 `/tmp/redis_tests/`
3. **汇总报告**: 测试完成后显示统计信息

## 测试验证标准

### 配置测试标准

- ✅ 所有配置文件语法正确
- ✅ Redis与NebulaGraph主机列表匹配
- ✅ 端口配置完整且无冲突
- ✅ 集群拓扑符合3主3从架构
- ✅ 资源分配合理
- ✅ 配置验证函数正常工作

### 安装测试标准

- ✅ 脚本语法检查通过
- ✅ 所有必需函数存在
- ✅ 仓库恢复逻辑完整
- ✅ 版本检查逻辑正确
- ✅ YUM安装流程完整
- ✅ 环境兼容性处理正确
- ✅ 卸载支持完整

### 卸载测试标准

- ✅ 帮助信息包含卸载选项
- ✅ 参数解析正确
- ✅ 配置文件正确加载
- ✅ 所有卸载函数存在
- ✅ Dry-run模式正常工作
- ✅ 锁机制正常工作

## 测试环境要求

### 系统要求

- **操作系统**: Linux (推荐Kylin V10)
- **Shell**: Bash 4.0+
- **权限**: 普通用户权限（测试不需要root）

### 依赖工具

- `bash` - Shell解释器
- `grep` - 文本搜索
- `awk` - 文本处理
- `sed` - 流编辑器
- `sort` - 排序工具

### 测试数据

测试使用的配置数据：

```bash
# Redis主机（与NebulaGraph共享）
REDIS_HOSTS=(*********** *********** ***********)

# Redis端口配置
REDIS_CLUSTER_PORTS=(7001 7002 7003 7004 7005 7006)

# 主机端口映射
***********: 7001,7004
***********: 7002,7005
***********: 7003,7006
```

## 故障排除

### 常见测试失败

#### 1. 配置文件加载失败

**症状**: `配置文件不存在` 或 `配置文件语法错误`

**解决方案**:
```bash
# 检查配置文件路径
ls -la config/hosts.conf config/redis.conf

# 检查配置文件语法
bash -n config/hosts.conf
bash -n config/redis.conf
```

#### 2. 函数未找到

**症状**: `函数缺失: function_name`

**解决方案**:
```bash
# 检查函数定义
grep -n "^function_name()" deploy_redis.sh

# 检查函数调用
grep -n "function_name" deploy_redis.sh
```

#### 3. 权限问题

**症状**: `Permission denied` 或 `cannot create directory`

**解决方案**:
```bash
# 确保测试脚本可执行
chmod +x test/*.sh

# 检查临时目录权限
ls -ld /tmp/
```

### 调试技巧

#### 1. 启用详细输出

```bash
# 使用verbose模式
./test/run_redis_tests.sh --verbose

# 直接运行单个测试查看详细输出
./test/test_redis_cluster_config.sh
```

#### 2. 检查测试日志

```bash
# 查看测试日志目录
ls -la /tmp/redis_tests/

# 查看特定测试的日志
cat /tmp/redis_tests/test_redis_cluster_config.log
```

#### 3. 手动验证

```bash
# 手动加载配置文件
source config/hosts.conf
source config/redis.conf

# 检查变量是否正确设置
echo "Redis hosts: ${REDIS_HOSTS[*]}"
echo "Redis ports: ${REDIS_CLUSTER_PORTS[*]}"
```

## 测试扩展

### 添加新测试

1. **创建测试脚本**:
```bash
cp test/test_redis_cluster_config.sh test/test_redis_new_feature.sh
```

2. **修改测试内容**:
```bash
# 编辑新测试脚本
vim test/test_redis_new_feature.sh
```

3. **添加到测试套件**:
```bash
# 编辑测试套件
vim test/run_redis_tests.sh
# 在tests数组中添加新测试
```

### 测试最佳实践

1. **测试独立性**: 每个测试应该独立运行，不依赖其他测试
2. **错误处理**: 测试应该有适当的错误处理和清理
3. **日志记录**: 重要的测试步骤应该有日志记录
4. **断言明确**: 测试断言应该明确且易于理解

## 持续集成

### 自动化测试

可以将测试集成到CI/CD流程中：

```bash
#!/bin/bash
# CI脚本示例

# 运行Redis测试
if ./test/run_redis_tests.sh; then
    echo "Redis测试通过"
else
    echo "Redis测试失败"
    exit 1
fi
```

### 测试报告

测试套件会生成标准化的测试报告，包括：

- 测试统计信息
- 成功/失败状态
- 详细的错误日志
- 性能指标（测试执行时间）

## 总结

Redis测试体系提供了全面的验证机制，确保：

1. **配置正确性**: 验证所有配置参数和拓扑结构
2. **功能完整性**: 验证安装、配置、卸载等核心功能
3. **环境兼容性**: 验证在目标环境中的兼容性
4. **错误处理**: 验证异常情况的处理能力

通过运行这些测试，可以确保Redis部署脚本的质量和可靠性。
