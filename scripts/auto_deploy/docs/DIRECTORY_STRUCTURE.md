# 自动部署脚本目录结构说明

## 目录组织原则

为了更好地组织和维护自动部署脚本，采用以下目录结构：

```
scripts/auto_deploy/
├── docs/                           # 📚 文档目录
│   ├── DIRECTORY_STRUCTURE.md      # 目录结构说明
│   ├── DEPLOYMENT_SCRIPTS_SUMMARY.md
│   ├── REDIS_BINARY_DEPLOYMENT_README.md
│   ├── HYBRID_PACKAGE_INSTALL_README.md
│   ├── CUSTOM_REPO_STRUCTURE_README.md
│   ├── YUM_REPO_CHECK_README.md
│   ├── OFFLINE_INSTALLATION_GUIDE.md
│   └── ...
├── test/                           # 🧪 测试脚本目录
│   ├── check_yum_repo_packages.sh  # YUM仓库包检测脚本
│   ├── test_custom_repo_simple.sh  # 自定义仓库测试
│   ├── test_hybrid_package_install.sh # 混合安装策略测试
│   ├── test_mirrors.sh             # 镜像源测试
│   └── test_yum_repo_check.sh      # 仓库检查测试
├── lib/                            # 📦 公共库目录
│   └── common.sh                   # 通用函数库
├── config/                         # ⚙️ 配置文件目录
│   ├── global.conf
│   ├── hosts.conf
│   ├── mongodb.conf
│   ├── redis.conf
│   └── ...
├── deploy_*.sh                     # 🚀 主要部署脚本
├── prepare_*.sh                    # 🔧 准备脚本
├── setup.sh                       # 📋 安装脚本
└── README.md                       # 📖 主要说明文档
```

## 目录说明

### 📚 docs/ - 文档目录

存放所有相关文档，包括：

- **部署指南** - 各服务的详细部署说明
- **配置说明** - 配置文件和参数说明
- **故障排查** - 常见问题和解决方案
- **最佳实践** - 部署和运维建议
- **API文档** - 脚本接口和函数说明

**命名规范**:
- 使用大写字母和下划线：`SERVICE_FEATURE_README.md`
- 主要文档使用 `README.md` 后缀
- 指南文档使用 `GUIDE.md` 后缀
- 总结文档使用 `SUMMARY.md` 后缀

### 🧪 test/ - 测试脚本目录

存放所有测试和验证脚本，包括：

- **功能测试** - 验证脚本功能是否正常
- **集成测试** - 验证组件间的集成
- **性能测试** - 验证部署性能
- **兼容性测试** - 验证不同环境的兼容性
- **回归测试** - 验证修改后的功能

**命名规范**:
- 测试脚本以 `test_` 开头
- 检查脚本以 `check_` 开头
- 验证脚本以 `verify_` 开头
- 基准测试以 `benchmark_` 开头

### 📦 lib/ - 公共库目录

存放可重用的函数库和工具：

- **common.sh** - 通用函数库
- **network.sh** - 网络相关函数
- **package.sh** - 包管理函数
- **service.sh** - 服务管理函数

### ⚙️ config/ - 配置文件目录

存放各种配置文件：

- **global.conf** - 全局配置
- **hosts.conf** - 主机配置
- **service.conf** - 各服务配置

## 文件引用规范

### 在主脚本中引用测试脚本

```bash
# 在部署脚本中调用测试
./test/test_custom_repo_simple.sh

# 或使用相对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
"$SCRIPT_DIR/test/test_hybrid_package_install.sh"
```

### 在测试脚本中引用文档

```bash
# 在测试脚本中引用相关文档
echo "详细说明请参考: docs/HYBRID_PACKAGE_INSTALL_README.md"
```

### 在文档中交叉引用

```markdown
## 相关文档

- [Redis二进制部署](./REDIS_BINARY_DEPLOYMENT_README.md)
- [混合安装策略](./HYBRID_PACKAGE_INSTALL_README.md)
- [自定义仓库结构](./CUSTOM_REPO_STRUCTURE_README.md)

## 相关测试

- [自定义仓库测试](../test/test_custom_repo_simple.sh)
- [混合安装测试](../test/test_hybrid_package_install.sh)
```

## 维护规范

### 新增测试脚本

1. 在 `test/` 目录下创建测试脚本
2. 使用规范的命名方式
3. 添加执行权限：`chmod +x test/new_test.sh`
4. 在相关文档中添加引用

### 新增文档

1. 在 `docs/` 目录下创建文档
2. 使用Markdown格式
3. 在主README中添加索引
4. 在相关脚本中添加文档引用

### 更新现有文件

1. 保持目录结构的一致性
2. 更新相关的交叉引用
3. 更新版本信息和更新日志
4. 验证所有链接的有效性

## 脚本模板

### 测试脚本模板

```bash
#!/bin/bash

# =============================================================================
# [测试名称] 测试脚本
# 功能：[简要描述测试功能]
# 作者：[作者信息]
# 版本：v1.0
# =============================================================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PARENT_DIR="$(dirname "$SCRIPT_DIR")"

# 引用公共库
source "$PARENT_DIR/lib/common.sh"

# 测试配置
TEST_NAME="[测试名称]"
TEST_VERSION="v1.0"

# 日志函数
log_info() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*"; }
log_warn() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $*"; }
log_error() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*"; }

# 显示使用说明
show_usage() {
    cat << EOF
用法: $0 [选项]

选项:
  -h, --help    显示此帮助信息

功能说明:
  [详细功能描述]

相关文档:
  - docs/[相关文档].md

EOF
}

# 主测试函数
main() {
    case "${1:-test}" in
        "help"|"-h"|"--help")
            show_usage
            exit 0
            ;;
        *)
            log_info "开始 $TEST_NAME 测试..."
            
            # 测试逻辑
            
            log_info "$TEST_NAME 测试完成"
            ;;
    esac
}

# 执行主函数
main "$@"
```

### 文档模板

```markdown
# [文档标题]

## 概述

[简要描述文档内容和目的]

## 功能特性

- ✅ [特性1]
- ✅ [特性2]
- ✅ [特性3]

## 使用方法

### 基本用法

```bash
# 示例命令
./script.sh [参数]
```

## 配置说明

[配置相关说明]

## 故障排查

### 常见问题

1. **问题描述**
   ```
   错误信息
   ```
   **解决方案**: [解决方法]

## 相关文档

- [相关文档1](./OTHER_DOC.md)
- [相关文档2](./ANOTHER_DOC.md)

## 相关测试

- [测试脚本1](../test/test_script.sh)
- [测试脚本2](../test/another_test.sh)

## 更新日志

- **v1.0** - 初始版本
```

## 最佳实践

1. **保持目录结构清晰** - 按功能分类，避免文件混乱
2. **使用一致的命名规范** - 便于查找和维护
3. **及时更新交叉引用** - 确保文档和脚本的关联性
4. **定期清理过时文件** - 移除不再使用的文件
5. **添加版本信息** - 便于跟踪变更历史

## 相关文档

- [部署脚本总结](./DEPLOYMENT_SCRIPTS_SUMMARY.md)
- [Redis二进制部署](./REDIS_BINARY_DEPLOYMENT_README.md)
- [混合安装策略](./HYBRID_PACKAGE_INSTALL_README.md)
- [离线安装指南](./OFFLINE_INSTALLATION_GUIDE.md)
