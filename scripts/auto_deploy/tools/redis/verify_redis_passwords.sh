#!/bin/bash
# Redis密码配置验证工具
# 检查requirepass和masterauth是否配置正确

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 检查是否只是显示帮助
if [[ "${1:-}" == "--help" ]]; then
    cat << EOF
Redis密码配置验证工具

用法: $0 [选项]

选项:
  -h, --host <主机IP>     指定主机IP（默认检查所有Redis主机）
  -p, --port <端口>       指定端口（默认6379）
  --fix                   自动修复密码不一致问题
  --verbose               显示详细输出
  --help                  显示此帮助信息

功能:
  1. 检查requirepass和masterauth是否存在
  2. 验证两个密码是否相同
  3. 测试密码认证是否有效
  4. 可选择自动修复不一致问题

示例:
  $0                      # 检查所有Redis节点
  $0 -h ***********       # 检查指定主机
  $0 --fix                # 自动修复密码不一致问题
EOF
    exit 0
fi

# 加载配置
source "$PROJECT_ROOT/lib/common.sh"
source "$PROJECT_ROOT/config/hosts.conf"

# Redis配置
REDIS_HOME="/apps/redis"
REDIS_CONFIG_DIR="/apps/redis/conf"

# 默认参数
TARGET_HOST=""
TARGET_PORT="6379"
AUTO_FIX=false
VERBOSE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            TARGET_HOST="$2"
            shift 2
            ;;
        -p|--port)
            TARGET_PORT="$2"
            shift 2
            ;;
        --fix)
            AUTO_FIX=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 获取目标主机列表
get_target_hosts() {
    if [[ -n "$TARGET_HOST" ]]; then
        echo "$TARGET_HOST"
    else
        printf '%s\n' "${REDIS_HOSTS[@]}"
    fi
}

# 检查单个节点的密码配置
check_node_passwords() {
    local host=$1
    local port=$2
    local config_file="$REDIS_CONFIG_DIR/redis-$port.conf"
    
    log_info "检查节点 $host:$port 的密码配置..."
    
    # 检查配置文件是否存在
    if ! remote_execute "$host" "test -f '$config_file'" 2>/dev/null; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 获取requirepass和masterauth
    local requirepass
    local masterauth
    
    requirepass=$(remote_execute "$host" "grep '^requirepass' '$config_file' | awk '{print \$2}'" 2>/dev/null || echo "")
    masterauth=$(remote_execute "$host" "grep '^masterauth' '$config_file' | awk '{print \$2}'" 2>/dev/null || echo "")
    
    # 检查密码是否存在
    if [[ -z "$requirepass" ]]; then
        log_error "未找到requirepass配置"
        return 1
    fi
    
    if [[ -z "$masterauth" ]]; then
        log_error "未找到masterauth配置"
        return 1
    fi
    
    # 检查密码是否相同
    if [[ "$requirepass" == "$masterauth" ]]; then
        log_info "✓ requirepass和masterauth密码一致"
        if [[ "$VERBOSE" == "true" ]]; then
            log_info "密码长度: ${#requirepass}"
        fi
    else
        log_error "✗ requirepass和masterauth密码不一致"
        log_error "requirepass长度: ${#requirepass}"
        log_error "masterauth长度: ${#masterauth}"
        
        if [[ "$AUTO_FIX" == "true" ]]; then
            log_info "自动修复: 将masterauth设置为与requirepass相同"
            # 修复密码不一致问题 - 使用更安全的方法
            remote_execute "$host" "
                # 备份原配置文件
                cp '$config_file' '$config_file.backup'
                # 删除旧的masterauth行
                grep -v '^masterauth' '$config_file.backup' > '$config_file.tmp'
                # 添加新的masterauth行
                echo 'masterauth $requirepass' >> '$config_file.tmp'
                # 替换原文件
                mv '$config_file.tmp' '$config_file'
                echo '✓ masterauth已更新'
            "

            # 重启Redis服务以应用更改
            log_info "重启Redis服务以应用配置更改..."
            remote_execute "$host" "systemctl restart redis-$port"
            sleep 5

            log_info "✓ 密码不一致问题已修复"
        else
            return 1
        fi
    fi
    
    # 测试密码认证
    log_info "测试密码认证..."
    local auth_result
    auth_result=$(remote_execute "$host" "REDISCLI_AUTH='$requirepass' $REDIS_HOME/bin/redis-cli -h $host -p $port ping" 2>/dev/null || echo "FAILED")
    
    if [[ "$auth_result" == "PONG" ]]; then
        log_info "✓ 密码认证测试成功"
    else
        log_error "✗ 密码认证测试失败"
        log_error "响应: $auth_result"
        return 1
    fi
    
    return 0
}

# 主函数
main() {
    log_info "Redis密码配置验证工具"
    log_info "========================"
    
    local hosts
    mapfile -t hosts < <(get_target_hosts)
    
    local total_nodes=0
    local success_nodes=0
    local failed_nodes=()
    
    for host in "${hosts[@]}"; do
        # 获取该主机的端口列表
        local host_ports=""
        if [[ -n "${REDIS_HOST_PORTS[$host]:-}" ]]; then
            host_ports="${REDIS_HOST_PORTS[$host]}"
        else
            host_ports="$TARGET_PORT"
        fi
        
        # 解析端口列表
        IFS=',' read -ra ports <<< "$host_ports"
        for port in "${ports[@]}"; do
            ((total_nodes++))
            
            echo
            log_info "检查节点: $host:$port"
            
            if check_node_passwords "$host" "$port"; then
                log_info "✓ 节点 $host:$port 密码配置正确"
                ((success_nodes++))
            else
                log_error "✗ 节点 $host:$port 密码配置有问题"
                failed_nodes+=("$host:$port")
            fi
        done
    done
    
    echo
    log_info "验证完成"
    log_info "========================"
    log_info "总节点数: $total_nodes"
    log_info "成功节点: $success_nodes"
    log_info "失败节点: ${#failed_nodes[@]}"
    
    if [[ ${#failed_nodes[@]} -gt 0 ]]; then
        log_error "以下节点存在密码配置问题:"
        for node in "${failed_nodes[@]}"; do
            log_error "  - $node"
        done
        
        if [[ "$AUTO_FIX" != "true" ]]; then
            echo
            log_info "运行以下命令自动修复问题:"
            log_info "$0 --fix"
        fi
        
        exit 1
    else
        log_info "✓ 所有节点密码配置都正确"
        exit 0
    fi
}

# 执行主函数
main "$@"
