#!/bin/bash
# Redis密码认证修复工具
# 用于诊断和修复Redis密码认证问题

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 检查是否只是显示帮助
if [[ "${1:-}" == "--help" ]]; then
    show_help() {
        cat << EOF
Redis密码认证修复工具

用法: $0 [选项]

选项:
  -h, --host <主机IP>     指定主机IP（默认检查所有Redis主机）
  -p, --port <端口>       指定端口（默认6379）
  --password <密码>       指定Redis密码（如果已知）
  --fix                   自动修复发现的问题
  --restart               重启Redis服务
  --verbose               显示详细输出
  --help                  显示此帮助信息

示例:
  $0                      # 检查所有Redis节点
  $0 -h ***********       # 检查指定主机
  $0 --fix                # 自动修复问题
  $0 --restart            # 重启所有Redis服务
EOF
    }
    show_help
    exit 0
fi

# 加载配置
source "$PROJECT_ROOT/lib/common.sh"
source "$PROJECT_ROOT/config/hosts.conf"

# Redis配置
REDIS_HOME="/apps/redis"
REDIS_CONFIG_DIR="/apps/redis/conf"
REDIS_DATA_DIR="/apps/data/redis"



# 默认参数
TARGET_HOST=""
TARGET_PORT="6379"
REDIS_PASSWORD=""
AUTO_FIX=false
RESTART_SERVICES=false
VERBOSE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            TARGET_HOST="$2"
            shift 2
            ;;
        -p|--port)
            TARGET_PORT="$2"
            shift 2
            ;;
        --password)
            REDIS_PASSWORD="$2"
            shift 2
            ;;
        --fix)
            AUTO_FIX=true
            shift
            ;;
        --restart)
            RESTART_SERVICES=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 获取目标主机列表
get_target_hosts() {
    if [[ -n "$TARGET_HOST" ]]; then
        echo "$TARGET_HOST"
    else
        printf '%s\n' "${REDIS_HOSTS[@]}"
    fi
}

# 检查Redis服务状态
check_redis_service() {
    local host=$1
    local port=$2
    
    log_info "检查 $host:$port 的Redis服务状态..."
    
    # 检查服务是否运行
    if remote_execute "$host" "systemctl is-active --quiet redis-$port"; then
        log_info "✓ Redis服务正在运行"
    else
        log_warn "✗ Redis服务未运行"
        return 1
    fi
    
    # 检查端口监听
    if remote_execute "$host" "netstat -tlnp | grep :$port" >/dev/null 2>&1; then
        log_info "✓ 端口 $port 正在监听"
    else
        log_warn "✗ 端口 $port 未监听"
        return 1
    fi
    
    return 0
}

# 检查Redis配置文件
check_redis_config() {
    local host=$1
    local port=$2
    local config_file="$REDIS_CONFIG_DIR/redis-$port.conf"
    
    log_info "检查 $host:$port 的Redis配置文件..."
    
    # 检查配置文件是否存在
    if ! remote_execute "$host" "test -f '$config_file'"; then
        log_error "✗ 配置文件不存在: $config_file"
        return 1
    fi
    
    log_info "✓ 配置文件存在: $config_file"
    
    # 检查密码配置
    local requirepass_config
    requirepass_config=$(remote_execute "$host" "grep '^requirepass' '$config_file' || echo 'NOT_FOUND'")
    
    if [[ "$requirepass_config" == "NOT_FOUND" ]]; then
        log_warn "✗ 未找到requirepass配置"
        return 1
    else
        log_info "✓ 找到requirepass配置"
        if [[ "$VERBOSE" == "true" ]]; then
            log_info "配置内容: $requirepass_config"
        fi
    fi
    
    # 检查masterauth配置
    local masterauth_config
    masterauth_config=$(remote_execute "$host" "grep '^masterauth' '$config_file' || echo 'NOT_FOUND'")
    
    if [[ "$masterauth_config" == "NOT_FOUND" ]]; then
        log_warn "✗ 未找到masterauth配置"
    else
        log_info "✓ 找到masterauth配置"
        if [[ "$VERBOSE" == "true" ]]; then
            log_info "配置内容: $masterauth_config"
        fi
    fi
    
    return 0
}

# 获取Redis密码
get_redis_password() {
    local host=$1
    local port=$2
    local config_file="$REDIS_CONFIG_DIR/redis-$port.conf"
    
    # 如果已经提供了密码，直接使用
    if [[ -n "$REDIS_PASSWORD" ]]; then
        echo "$REDIS_PASSWORD"
        return 0
    fi
    
    # 从配置文件获取密码
    local password
    password=$(remote_execute "$host" "grep '^requirepass' '$config_file' | awk '{print \$2}'" 2>/dev/null || echo "")
    
    if [[ -n "$password" ]]; then
        echo "$password"
        return 0
    fi
    
    return 1
}

# 测试Redis连接
test_redis_connection() {
    local host=$1
    local port=$2
    local password=$3
    
    log_info "测试 $host:$port 的Redis连接..."
    
    # 测试无密码连接
    local no_auth_result
    no_auth_result=$(remote_execute "$host" "$REDIS_HOME/bin/redis-cli -h $host -p $port ping" 2>/dev/null || echo "FAILED")
    
    if [[ "$no_auth_result" == "PONG" ]]; then
        log_warn "⚠ 无密码连接成功 - Redis可能未启用认证"
        return 2  # 特殊返回码表示无密码连接成功
    fi
    
    # 测试密码连接（使用环境变量）
    local env_auth_result
    env_auth_result=$(remote_execute "$host" "REDISCLI_AUTH='$password' $REDIS_HOME/bin/redis-cli -h $host -p $port ping" 2>/dev/null || echo "FAILED")
    
    if [[ "$env_auth_result" == "PONG" ]]; then
        log_info "✓ 使用环境变量认证成功"
        return 0
    fi
    
    # 测试密码连接（使用-a参数）
    local param_auth_result
    param_auth_result=$(remote_execute "$host" "$REDIS_HOME/bin/redis-cli -h $host -p $port -a '$password' ping" 2>/dev/null || echo "FAILED")
    
    if [[ "$param_auth_result" == "PONG" ]]; then
        log_info "✓ 使用-a参数认证成功"
        return 0
    fi
    
    log_error "✗ 所有认证方式都失败"
    return 1
}

# 修复Redis认证问题
fix_redis_auth() {
    local host=$1
    local port=$2
    
    log_info "修复 $host:$port 的Redis认证问题..."
    
    # 重启Redis服务
    log_info "重启Redis服务..."
    if remote_execute "$host" "systemctl restart redis-$port"; then
        log_info "✓ Redis服务重启成功"
        sleep 10  # 等待服务完全启动
    else
        log_error "✗ Redis服务重启失败"
        return 1
    fi
    
    # 检查服务状态
    if ! check_redis_service "$host" "$port"; then
        log_error "✗ Redis服务重启后仍然异常"
        return 1
    fi
    
    log_info "✓ Redis认证问题修复完成"
    return 0
}

# 显示Redis日志
show_redis_logs() {
    local host=$1
    local port=$2
    local log_file="$REDIS_DATA_DIR/log/redis-$port.log"
    
    log_info "显示 $host:$port 的Redis日志..."
    
    remote_execute "$host" "
        if [[ -f '$log_file' ]]; then
            echo '=== Redis日志 (最后50行) ==='
            tail -50 '$log_file'
        else
            echo '日志文件不存在: $log_file'
        fi
    "
}

# 主函数
main() {
    log_info "开始Redis密码认证诊断..."
    
    local hosts
    mapfile -t hosts < <(get_target_hosts)
    
    local total_issues=0
    local fixed_issues=0
    
    for host in "${hosts[@]}"; do
        log_info "检查主机: $host"
        
        # 获取该主机的端口列表
        local host_ports=""
        if [[ -n "${REDIS_HOST_PORTS[$host]:-}" ]]; then
            host_ports="${REDIS_HOST_PORTS[$host]}"
        else
            host_ports="$TARGET_PORT"
        fi
        
        # 解析端口列表
        IFS=',' read -ra ports <<< "$host_ports"
        for port in "${ports[@]}"; do
            log_info "检查端口: $port"
            
            local has_issues=false
            
            # 检查服务状态
            if ! check_redis_service "$host" "$port"; then
                has_issues=true
                ((total_issues++))
                
                if [[ "$AUTO_FIX" == "true" ]] || [[ "$RESTART_SERVICES" == "true" ]]; then
                    if fix_redis_auth "$host" "$port"; then
                        ((fixed_issues++))
                        has_issues=false
                    fi
                fi
            fi
            
            # 检查配置文件
            if ! check_redis_config "$host" "$port"; then
                has_issues=true
                ((total_issues++))
            fi
            
            # 获取密码并测试连接
            local password
            if password=$(get_redis_password "$host" "$port"); then
                log_info "获取到Redis密码，长度: ${#password}"
                
                local conn_result
                test_redis_connection "$host" "$port" "$password"
                conn_result=$?
                
                if [[ $conn_result -eq 1 ]]; then
                    has_issues=true
                    ((total_issues++))
                    
                    if [[ "$AUTO_FIX" == "true" ]]; then
                        if fix_redis_auth "$host" "$port"; then
                            ((fixed_issues++))
                        fi
                    fi
                elif [[ $conn_result -eq 2 ]]; then
                    log_warn "Redis未启用密码认证，这可能是安全风险"
                fi
            else
                log_error "无法获取Redis密码"
                has_issues=true
                ((total_issues++))
            fi
            
            # 如果有问题且启用了详细模式，显示日志
            if [[ "$has_issues" == "true" && "$VERBOSE" == "true" ]]; then
                show_redis_logs "$host" "$port"
            fi
            
            echo "----------------------------------------"
        done
    done
    
    # 总结
    log_info "诊断完成"
    log_info "发现问题: $total_issues"
    if [[ "$AUTO_FIX" == "true" ]]; then
        log_info "修复问题: $fixed_issues"
    fi
    
    if [[ $total_issues -gt 0 ]]; then
        if [[ "$AUTO_FIX" != "true" ]]; then
            log_info "运行 '$0 --fix' 来自动修复问题"
        fi
        exit 1
    else
        log_info "✓ 所有Redis节点认证正常"
        exit 0
    fi
}

# 执行主函数
main "$@"
