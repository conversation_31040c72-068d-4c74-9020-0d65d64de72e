#!/bin/bash
# Redis密码获取工具
# 用于获取当前Redis集群的密码

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 检查是否只是显示帮助
if [[ "${1:-}" == "--help" ]]; then
    show_help() {
        cat << EOF
Redis密码获取工具

用法: $0 [选项]

选项:
  -h, --host <主机IP>     指定主机IP（默认使用第一个Redis主机）
  -p, --port <端口>       指定端口（默认6379）
  -f, --format <格式>     输出格式：plain|env|json（默认plain）
  -t, --test             测试密码是否有效
  --help                 显示此帮助信息

示例:
  $0                                    # 获取默认主机的Redis密码
  $0 -h ***********                   # 获取指定主机的Redis密码
  $0 -f env                           # 以环境变量格式输出
  $0 -t                               # 获取密码并测试连接
  $0 -f json                          # 以JSON格式输出

EOF
    }
    show_help
    exit 0
fi

# 加载配置
source "$PROJECT_ROOT/lib/common.sh"
source "$PROJECT_ROOT/config/hosts.conf"

# Redis配置
REDIS_CONFIG_DIR="/apps/redis/conf"



# 默认参数
TARGET_HOST=""
TARGET_PORT="6379"
OUTPUT_FORMAT="plain"
TEST_CONNECTION=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            TARGET_HOST="$2"
            shift 2
            ;;
        -p|--port)
            TARGET_PORT="$2"
            shift 2
            ;;
        -f|--format)
            OUTPUT_FORMAT="$2"
            shift 2
            ;;
        -t|--test)
            TEST_CONNECTION=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "错误: 未知参数 $1" >&2
            echo "使用 --help 查看帮助信息" >&2
            exit 1
            ;;
    esac
done

# 获取目标主机
if [[ -z "$TARGET_HOST" ]]; then
    if [[ ${#REDIS_HOSTS[@]} -gt 0 ]]; then
        TARGET_HOST="${REDIS_HOSTS[0]}"
        log_info "使用默认主机: $TARGET_HOST"
    else
        log_error "未配置Redis主机，请使用 -h 参数指定主机"
        exit 1
    fi
fi

# 验证输出格式
case "$OUTPUT_FORMAT" in
    plain|env|json)
        ;;
    *)
        log_error "不支持的输出格式: $OUTPUT_FORMAT"
        log_error "支持的格式: plain, env, json"
        exit 1
        ;;
esac

# 获取Redis密码
get_redis_password() {
    local host=$1
    local port=$2
    local config_file="$REDIS_CONFIG_DIR/redis-$port.conf"
    
    log_info "从 $host 获取Redis密码..."
    log_info "配置文件: $config_file"
    
    # 检查配置文件是否存在
    if ! remote_execute "$host" "test -f '$config_file'" 2>/dev/null; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 获取requirepass密码
    local requirepass
    requirepass=$(remote_execute "$host" "grep '^requirepass' '$config_file' | awk '{print \$2}'" 2>/dev/null)
    
    # 获取masterauth密码
    local masterauth
    masterauth=$(remote_execute "$host" "grep '^masterauth' '$config_file' | awk '{print \$2}'" 2>/dev/null)
    
    if [[ -z "$requirepass" ]]; then
        log_error "未找到requirepass配置"
        return 1
    fi
    
    echo "$requirepass"
    return 0
}

# 测试Redis连接
test_redis_connection() {
    local host=$1
    local port=$2
    local password=$3
    
    log_info "测试Redis连接: $host:$port"
    
    # 测试PING命令
    local ping_result
    ping_result=$(remote_execute "$host" "REDISCLI_AUTH='$password' /apps/redis/bin/redis-cli -h $host -p $port ping" 2>/dev/null)
    
    if [[ "$ping_result" == "PONG" ]]; then
        log_info "✓ Redis连接测试成功"
        return 0
    else
        log_error "✗ Redis连接测试失败"
        log_error "响应: $ping_result"
        return 1
    fi
}

# 输出密码信息
output_password() {
    local host=$1
    local port=$2
    local password=$3
    local format=$4
    
    case "$format" in
        plain)
            echo "Redis密码信息:"
            echo "主机: $host"
            echo "端口: $port"
            echo "密码: $password"
            echo "密码长度: ${#password}"
            ;;
        env)
            echo "# Redis环境变量"
            echo "export REDIS_HOST='$host'"
            echo "export REDIS_PORT='$port'"
            echo "export REDIS_PASSWORD='$password'"
            echo "export REDISCLI_AUTH='$password'"
            ;;
        json)
            cat << EOF
{
  "redis": {
    "host": "$host",
    "port": "$port",
    "password": "$password",
    "password_length": ${#password},
    "connection_string": "redis://:$password@$host:$port"
  }
}
EOF
            ;;
    esac
}

# 主函数
main() {
    log_info "Redis密码获取工具"
    log_info "目标主机: $TARGET_HOST:$TARGET_PORT"
    log_info "输出格式: $OUTPUT_FORMAT"
    
    # 获取密码
    local password
    if ! password=$(get_redis_password "$TARGET_HOST" "$TARGET_PORT"); then
        log_error "获取Redis密码失败"
        exit 1
    fi
    
    # 测试连接（如果需要）
    if [[ "$TEST_CONNECTION" == "true" ]]; then
        if ! test_redis_connection "$TARGET_HOST" "$TARGET_PORT" "$password"; then
            log_error "Redis连接测试失败"
            exit 1
        fi
    fi
    
    # 输出密码信息
    echo
    output_password "$TARGET_HOST" "$TARGET_PORT" "$password" "$OUTPUT_FORMAT"
    
    # 显示使用示例
    if [[ "$OUTPUT_FORMAT" == "plain" ]]; then
        echo
        echo "使用示例:"
        echo "# 连接Redis"
        echo "REDISCLI_AUTH='$password' redis-cli -h $TARGET_HOST -p $TARGET_PORT"
        echo
        echo "# 查看集群状态"
        echo "REDISCLI_AUTH='$password' redis-cli -h $TARGET_HOST -p $TARGET_PORT cluster info"
        echo
        echo "# 查看集群节点"
        echo "REDISCLI_AUTH='$password' redis-cli -h $TARGET_HOST -p $TARGET_PORT cluster nodes"
    fi
}

# 执行主函数
main "$@"
