# Redis密码认证问题修复工具

当Redis部署脚本出现密码认证失败错误时，可以使用以下工具进行诊断和修复。

## 错误症状

```
[2025-06-19 10:01:34] [INFO] 使用传入的Redis集群密码
[2025-06-19 10:01:34] [INFO] Redis集群密码已获取，长度: 14
[2025-06-19 10:01:34] [INFO] 验证Redis节点密码认证...
[2025-06-19 10:01:35] [WARN] 节点 ***********:6379 密码认证失败
```

## 常见原因

1. **密码配置不一致**: `requirepass` 和 `masterauth` 设置了不同的密码
2. **Redis服务未启动**: 服务状态异常或启动失败
3. **配置文件问题**: 密码配置缺失或格式错误
4. **网络连接问题**: 节点间无法正常通信
5. **脚本语法错误**: sed命令特殊字符处理问题（已修复）

## 快速修复方案

### 1. 一键快速修复（推荐）

```bash
# 运行快速修复工具，自动诊断和修复所有Redis节点
./tools/redis_quick_fix.sh
```

这个工具会：
- 检查所有Redis服务状态
- 重启未运行的服务
- 验证端口监听
- 检查配置文件
- 测试密码认证
- 提供详细的修复报告

### 2. 详细诊断工具

```bash
# 诊断所有Redis节点（显示详细信息）
./tools/fix_redis_auth.sh --verbose

# 诊断指定主机
./tools/fix_redis_auth.sh -h *********** --verbose

# 自动修复发现的问题
./tools/fix_redis_auth.sh --fix

# 重启所有Redis服务
./tools/fix_redis_auth.sh --restart
```

### 3. 密码获取和测试

```bash
# 获取Redis密码
./tools/get_redis_password.sh

# 测试密码有效性
./tools/get_redis_password.sh -t

# 以环境变量格式输出（便于脚本使用）
./tools/get_redis_password.sh -f env
```

### 4. 密码配置验证（新增）

```bash
# 验证所有节点的密码配置一致性
./tools/verify_redis_passwords.sh

# 验证指定节点
./tools/verify_redis_passwords.sh -h ***********

# 自动修复密码不一致问题
./tools/verify_redis_passwords.sh --fix

# 显示详细信息
./tools/verify_redis_passwords.sh --verbose
```

## 手动修复步骤

如果自动修复工具无法解决问题，可以按以下步骤手动修复：

### 1. 检查Redis服务状态

```bash
# 检查所有Redis服务
for host in *********** *********** *********** *********** *********** ***********; do
    echo "检查主机: $host"
    ssh $host "systemctl status redis-6379"
done
```

### 2. 重启Redis服务

```bash
# 重启所有Redis服务
for host in *********** *********** *********** *********** *********** ***********; do
    echo "重启主机 $host 的Redis服务"
    ssh $host "systemctl restart redis-6379"
    sleep 5
done
```

### 3. 验证配置文件

```bash
# 检查配置文件中的密码设置
ssh *********** "grep -E '^(requirepass|masterauth)' /apps/redis/conf/redis-6379.conf"
```

### 4. 测试连接

```bash
# 获取密码
PASSWORD=$(ssh *********** "grep '^requirepass' /apps/redis/conf/redis-6379.conf | awk '{print \$2}'")

# 测试连接
ssh *********** "REDISCLI_AUTH='$PASSWORD' /apps/redis/bin/redis-cli -h *********** -p 6379 ping"
```

## 常见问题和解决方案

### 问题1: Redis服务未启动

**症状**: `systemctl status redis-6379` 显示服务未运行

**解决方案**:
```bash
systemctl start redis-6379
systemctl enable redis-6379
```

### 问题2: 密码配置不一致

**症状**: `requirepass` 和 `masterauth` 设置了不同的密码

**解决方案**:
```bash
# 自动修复密码不一致
./tools/verify_redis_passwords.sh --fix

# 或手动修复
requirepass=$(grep '^requirepass' /apps/redis/conf/redis-6379.conf | awk '{print $2}')
sed -i "s/^masterauth.*/masterauth $requirepass/" /apps/redis/conf/redis-6379.conf
systemctl restart redis-6379
```

### 问题3: 配置文件中没有密码

**症状**: `grep requirepass` 没有输出

**解决方案**:
```bash
# 重新运行部署脚本的配置阶段
./deploy_redis.sh --skip-install --force-reinstall
```

### 问题3: 端口未监听

**症状**: `netstat -tlnp | grep :6379` 没有输出

**解决方案**:
```bash
# 检查配置文件中的bind和port设置
grep -E '^(bind|port)' /apps/redis/conf/redis-6379.conf

# 检查防火墙
firewall-cmd --list-ports
firewall-cmd --add-port=6379/tcp --permanent
firewall-cmd --reload
```

### 问题4: sed命令特殊字符错误

**症状**: `sed: -e expression #1, char 112: unknown option to 's'`

**原因**: 密码包含特殊字符（如 `/`, `&`, `\` 等）导致sed命令解析失败

**解决方案**: 已修复，现在使用更安全的配置文件修复方法
```bash
# 自动修复（推荐）
./tools/redis_quick_fix.sh

# 或手动修复
./tools/verify_redis_passwords.sh --fix
```

### 问题5: 密码认证仍然失败

**症状**: 即使密码正确，认证仍然失败

**解决方案**:
```bash
# 检查Redis日志
tail -50 /apps/data/redis/log/redis-6379.log

# 尝试不同的认证方式
redis-cli -h *********** -p 6379 -a 'password' ping
REDISCLI_AUTH='password' redis-cli -h *********** -p 6379 ping

# 重新生成配置文件
./deploy_redis.sh --skip-install --skip-init --force-reinstall
```

## 修复后验证

修复完成后，可以通过以下方式验证：

```bash
# 1. 检查所有服务状态
./tools/fix_redis_auth.sh

# 2. 重新运行Redis部署（跳过安装和配置）
./deploy_redis.sh --skip-install --skip-config

# 3. 检查集群状态
./tools/redis_tools.sh status
```

## 预防措施

为了避免类似问题，建议：

1. **部署前检查**: 确保所有主机的SSH连接正常
2. **分步部署**: 使用 `--dry-run` 参数先预览操作
3. **监控日志**: 部署过程中关注日志输出
4. **定期检查**: 使用监控工具定期检查Redis服务状态

## 获取帮助

如果问题仍然存在，请：

1. 运行详细诊断: `./tools/fix_redis_auth.sh --verbose`
2. 收集日志信息: `journalctl -u redis-6379 -n 100`
3. 检查系统资源: `free -h && df -h`
4. 联系技术支持并提供上述信息
