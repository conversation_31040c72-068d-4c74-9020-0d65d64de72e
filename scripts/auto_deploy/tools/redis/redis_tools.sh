#!/bin/bash
# Redis集群管理工具
# 整合密码获取、集群状态查看、节点重启等功能

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 检查是否只是显示帮助信息
if [[ $# -eq 1 && "$1" == "--help" ]]; then
    show_help() {
        cat << EOF
Redis集群管理工具

用法: $0 <命令> [选项]

命令:
  password                获取Redis密码
  status                  查看集群状态
  nodes                   查看集群节点信息
  info                    查看Redis实例信息
  restart                 重启Redis节点
  stop                    停止Redis节点
  start                   启动Redis节点
  ping                    测试节点连通性
  health                  健康检查
  slots                   查看槽分布
  failover                手动故障转移
  reset                   重置集群状态

通用选项:
  -h, --host <主机IP>     指定主机IP（默认使用第一个Redis主机）
  -p, --port <端口>       指定端口（默认6379）
  -a, --all              对所有节点执行操作
  -f, --format <格式>     输出格式：plain|json|table（默认plain）
  -v, --verbose          详细输出
  --help                 显示此帮助信息

示例:
  $0 password                           # 获取Redis密码
  $0 status                            # 查看集群状态
  $0 nodes -f table                    # 以表格形式查看节点
  $0 restart -h ***********           # 重启指定节点
  $0 ping -a                          # 测试所有节点连通性
  $0 health                           # 执行健康检查
  $0 info -h *********** -v          # 查看详细信息

EOF
    }
    show_help
    exit 0
fi

# 加载配置（仅在非帮助模式下）
if [[ -f "$PROJECT_ROOT/lib/common.sh" ]]; then
    source "$PROJECT_ROOT/lib/common.sh"
else
    # 提供基本的日志函数
    log_info() { echo "[INFO] $*"; }
    log_error() { echo "[ERROR] $*"; }
    log_warn() { echo "[WARN] $*"; }
    log_debug() { echo "[DEBUG] $*"; }
    remote_execute() {
        echo "[ERROR] 配置文件未加载，无法执行远程命令"
        return 1
    }
fi

if [[ -f "$PROJECT_ROOT/config/hosts.conf" ]]; then
    source "$PROJECT_ROOT/config/hosts.conf"
else
    # 提供默认的主机配置
    declare -a REDIS_HOSTS=()
fi

# Redis配置
REDIS_CONFIG_DIR="/apps/redis/conf"
REDIS_HOME="/apps/redis"



# 默认参数
COMMAND=""
TARGET_HOST=""
TARGET_PORT="6379"
OUTPUT_FORMAT="plain"
ALL_NODES=false
VERBOSE=false

# 解析命令行参数
if [[ $# -eq 0 ]]; then
    show_help
    exit 1
fi

COMMAND="$1"
shift

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            TARGET_HOST="$2"
            shift 2
            ;;
        -p|--port)
            TARGET_PORT="$2"
            shift 2
            ;;
        -a|--all)
            ALL_NODES=true
            shift
            ;;
        -f|--format)
            OUTPUT_FORMAT="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "错误: 未知参数 $1" >&2
            echo "使用 --help 查看帮助信息" >&2
            exit 1
            ;;
    esac
done

# 获取目标主机列表
get_target_hosts() {
    if [[ "$ALL_NODES" == "true" ]]; then
        echo "${REDIS_HOSTS[@]}"
    elif [[ -n "$TARGET_HOST" ]]; then
        echo "$TARGET_HOST"
    else
        if [[ ${#REDIS_HOSTS[@]} -gt 0 ]]; then
            echo "${REDIS_HOSTS[0]}"
        else
            log_error "未配置Redis主机，请使用 -h 参数指定主机或 -a 选项"
            exit 1
        fi
    fi
}

# 获取Redis密码
get_redis_password() {
    local host=$1
    local port=$2
    local config_file="$REDIS_CONFIG_DIR/redis-$port.conf"
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "从 $host 获取Redis密码..."
    fi
    
    # 检查配置文件是否存在
    if ! remote_execute "$host" "test -f '$config_file'" 2>/dev/null; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 获取requirepass密码
    local requirepass
    requirepass=$(remote_execute "$host" "grep '^requirepass' '$config_file' | awk '{print \$2}'" 2>/dev/null)
    
    if [[ -z "$requirepass" ]]; then
        log_error "未找到requirepass配置"
        return 1
    fi
    
    echo "$requirepass"
    return 0
}

# 执行Redis命令
execute_redis_command() {
    local host=$1
    local port=$2
    local password=$3
    local command=$4
    
    remote_execute "$host" "REDISCLI_AUTH='$password' $REDIS_HOME/bin/redis-cli -h $host -p $port $command" 2>/dev/null
}

# 测试节点连通性
ping_node() {
    local host=$1
    local port=$2
    local password=$3
    
    local result
    result=$(execute_redis_command "$host" "$port" "$password" "ping")
    
    if [[ "$result" == "PONG" ]]; then
        echo "✓ $host:$port - 连接正常"
        return 0
    else
        echo "✗ $host:$port - 连接失败: $result"
        return 1
    fi
}

# 获取节点信息
get_node_info() {
    local host=$1
    local port=$2
    local password=$3
    local info_type=${4:-"server"}
    
    execute_redis_command "$host" "$port" "$password" "info $info_type"
}

# 获取集群状态
get_cluster_status() {
    local host=$1
    local port=$2
    local password=$3
    
    execute_redis_command "$host" "$port" "$password" "cluster info"
}

# 获取集群节点
get_cluster_nodes() {
    local host=$1
    local port=$2
    local password=$3
    
    execute_redis_command "$host" "$port" "$password" "cluster nodes"
}

# 获取槽分布
get_cluster_slots() {
    local host=$1
    local port=$2
    local password=$3
    
    execute_redis_command "$host" "$port" "$password" "cluster slots"
}

# 重启Redis服务
restart_redis_service() {
    local host=$1
    local port=$2
    
    log_info "重启 $host:$port Redis服务..."
    
    # 停止服务
    if ! remote_execute "$host" "systemctl stop redis-$port"; then
        log_error "停止Redis服务失败: $host:$port"
        return 1
    fi
    
    # 等待服务完全停止
    sleep 3
    
    # 启动服务
    if ! remote_execute "$host" "systemctl start redis-$port"; then
        log_error "启动Redis服务失败: $host:$port"
        return 1
    fi
    
    # 等待服务启动
    sleep 5
    
    # 验证服务状态
    if remote_execute "$host" "systemctl is-active --quiet redis-$port"; then
        log_info "✓ Redis服务重启成功: $host:$port"
        return 0
    else
        log_error "✗ Redis服务重启失败: $host:$port"
        return 1
    fi
}

# 停止Redis服务
stop_redis_service() {
    local host=$1
    local port=$2
    
    log_info "停止 $host:$port Redis服务..."
    
    if remote_execute "$host" "systemctl stop redis-$port"; then
        log_info "✓ Redis服务停止成功: $host:$port"
        return 0
    else
        log_error "✗ Redis服务停止失败: $host:$port"
        return 1
    fi
}

# 启动Redis服务
start_redis_service() {
    local host=$1
    local port=$2
    
    log_info "启动 $host:$port Redis服务..."
    
    if remote_execute "$host" "systemctl start redis-$port"; then
        # 等待服务启动
        sleep 5
        if remote_execute "$host" "systemctl is-active --quiet redis-$port"; then
            log_info "✓ Redis服务启动成功: $host:$port"
            return 0
        else
            log_error "✗ Redis服务启动失败: $host:$port"
            return 1
        fi
    else
        log_error "✗ Redis服务启动失败: $host:$port"
        return 1
    fi
}

# 格式化输出
format_output() {
    local format=$1
    local title=$2
    local content=$3

    case "$format" in
        plain)
            echo "=== $title ==="
            echo "$content"
            echo
            ;;
        json)
            echo "{\"title\": \"$title\", \"content\": \"$content\"}"
            ;;
        table)
            echo "┌─ $title ─┐"
            echo "$content" | sed 's/^/│ /'
            echo "└─────────────┘"
            ;;
    esac
}

# 命令处理函数

# 处理password命令
cmd_password() {
    local hosts=($(get_target_hosts))
    local host="${hosts[0]}"

    local password
    if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
        log_error "获取Redis密码失败"
        exit 1
    fi

    case "$OUTPUT_FORMAT" in
        plain)
            echo "Redis密码信息:"
            echo "主机: $host"
            echo "端口: $TARGET_PORT"
            echo "密码: $password"
            echo "密码长度: ${#password}"
            ;;
        json)
            cat << EOF
{
  "redis": {
    "host": "$host",
    "port": "$TARGET_PORT",
    "password": "$password",
    "password_length": ${#password}
  }
}
EOF
            ;;
        table)
            printf "┌─────────────┬─────────────────────────────────────┐\n"
            printf "│ %-11s │ %-35s │\n" "属性" "值"
            printf "├─────────────┼─────────────────────────────────────┤\n"
            printf "│ %-11s │ %-35s │\n" "主机" "$host"
            printf "│ %-11s │ %-35s │\n" "端口" "$TARGET_PORT"
            printf "│ %-11s │ %-35s │\n" "密码" "$password"
            printf "│ %-11s │ %-35s │\n" "密码长度" "${#password}"
            printf "└─────────────┴─────────────────────────────────────┘\n"
            ;;
    esac
}

# 处理status命令
cmd_status() {
    local hosts=($(get_target_hosts))
    local host="${hosts[0]}"

    local password
    if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
        log_error "获取Redis密码失败"
        exit 1
    fi

    local status
    status=$(get_cluster_status "$host" "$TARGET_PORT" "$password")

    format_output "$OUTPUT_FORMAT" "Redis集群状态 ($host:$TARGET_PORT)" "$status"
}

# 处理nodes命令
cmd_nodes() {
    local hosts=($(get_target_hosts))
    local host="${hosts[0]}"

    local password
    if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
        log_error "获取Redis密码失败"
        exit 1
    fi

    local nodes
    nodes=$(get_cluster_nodes "$host" "$TARGET_PORT" "$password")

    if [[ "$OUTPUT_FORMAT" == "table" ]]; then
        echo "Redis集群节点信息:"
        printf "┌─────────────────┬──────┬─────────┬──────────────────────────────────────┐\n"
        printf "│ %-15s │ %-4s │ %-7s │ %-36s │\n" "主机" "端口" "角色" "节点ID"
        printf "├─────────────────┼──────┼─────────┼──────────────────────────────────────┤\n"

        echo "$nodes" | while IFS= read -r line; do
            if [[ -n "$line" ]]; then
                node_id=$(echo "$line" | awk '{print $1}')
                host_port=$(echo "$line" | awk '{print $2}' | cut -d'@' -f1)
                host_ip=$(echo "$host_port" | cut -d':' -f1)
                port=$(echo "$host_port" | cut -d':' -f2)
                role=$(echo "$line" | awk '{print $3}' | cut -d',' -f1)

                printf "│ %-15s │ %-4s │ %-7s │ %-36s │\n" "$host_ip" "$port" "$role" "${node_id:0:36}"
            fi
        done
        printf "└─────────────────┴──────┴─────────┴──────────────────────────────────────┘\n"
    else
        format_output "$OUTPUT_FORMAT" "Redis集群节点 ($host:$TARGET_PORT)" "$nodes"
    fi
}

# 处理info命令
cmd_info() {
    local hosts=($(get_target_hosts))

    for host in $hosts; do
        local password
        if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
            log_error "获取 $host Redis密码失败"
            continue
        fi

        local info_type="server"
        if [[ "$VERBOSE" == "true" ]]; then
            info_type="all"
        fi

        local info
        info=$(get_node_info "$host" "$TARGET_PORT" "$password" "$info_type")

        format_output "$OUTPUT_FORMAT" "Redis信息 ($host:$TARGET_PORT)" "$info"
    done
}

# 处理ping命令
cmd_ping() {
    local hosts=($(get_target_hosts))
    local failed_count=0

    echo "Redis节点连通性测试:"

    for host in $hosts; do
        local password
        if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
            echo "✗ $host:$TARGET_PORT - 获取密码失败"
            ((failed_count++))
            continue
        fi

        if ! ping_node "$host" "$TARGET_PORT" "$password"; then
            ((failed_count++))
        fi
    done

    echo
    local total_count=$(echo $hosts | wc -w)
    local success_count=$((total_count - failed_count))
    echo "测试结果: $success_count/$total_count 节点正常"

    if [[ $failed_count -gt 0 ]]; then
        exit 1
    fi
}

# 处理restart命令
cmd_restart() {
    local hosts=($(get_target_hosts))
    local failed_count=0

    echo "重启Redis节点:"

    for host in $hosts; do
        if restart_redis_service "$host" "$TARGET_PORT"; then
            echo "✓ $host:$TARGET_PORT - 重启成功"
        else
            echo "✗ $host:$TARGET_PORT - 重启失败"
            ((failed_count++))
        fi
    done

    if [[ $failed_count -gt 0 ]]; then
        exit 1
    fi
}

# 处理stop命令
cmd_stop() {
    local hosts=($(get_target_hosts))
    local failed_count=0

    echo "停止Redis节点:"

    for host in $hosts; do
        if stop_redis_service "$host" "$TARGET_PORT"; then
            echo "✓ $host:$TARGET_PORT - 停止成功"
        else
            echo "✗ $host:$TARGET_PORT - 停止失败"
            ((failed_count++))
        fi
    done

    if [[ $failed_count -gt 0 ]]; then
        exit 1
    fi
}

# 处理start命令
cmd_start() {
    local hosts=($(get_target_hosts))
    local failed_count=0

    echo "启动Redis节点:"

    for host in $hosts; do
        if start_redis_service "$host" "$TARGET_PORT"; then
            echo "✓ $host:$TARGET_PORT - 启动成功"
        else
            echo "✗ $host:$TARGET_PORT - 启动失败"
            ((failed_count++))
        fi
    done

    if [[ $failed_count -gt 0 ]]; then
        exit 1
    fi
}

# 处理health命令
cmd_health() {
    local hosts=($(get_target_hosts))
    local issues=0

    echo "Redis集群健康检查:"
    echo

    # 检查所有节点连通性
    echo "1. 节点连通性检查:"
    for host in $hosts; do
        local password
        if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
            echo "  ✗ $host:$TARGET_PORT - 获取密码失败"
            ((issues++))
            continue
        fi

        if ping_node "$host" "$TARGET_PORT" "$password" | grep -q "✓"; then
            echo "  ✓ $host:$TARGET_PORT - 连接正常"
        else
            echo "  ✗ $host:$TARGET_PORT - 连接异常"
            ((issues++))
        fi
    done

    # 检查集群状态
    echo
    echo "2. 集群状态检查:"
    local first_host="${hosts%% *}"
    local password
    if password=$(get_redis_password "$first_host" "$TARGET_PORT"); then
        local cluster_state
        cluster_state=$(execute_redis_command "$first_host" "$TARGET_PORT" "$password" "cluster info" | grep "cluster_state")

        if [[ "$cluster_state" == *"ok"* ]]; then
            echo "  ✓ 集群状态正常: $cluster_state"
        else
            echo "  ✗ 集群状态异常: $cluster_state"
            ((issues++))
        fi

        # 检查集群节点数量
        local node_count
        node_count=$(execute_redis_command "$first_host" "$TARGET_PORT" "$password" "cluster nodes" | wc -l)

        if [[ $node_count -eq 6 ]]; then
            echo "  ✓ 集群节点数量正常: $node_count"
        else
            echo "  ✗ 集群节点数量异常: $node_count (期望: 6)"
            ((issues++))
        fi
    else
        echo "  ✗ 无法获取集群状态"
        ((issues++))
    fi

    # 检查服务状态
    echo
    echo "3. 服务状态检查:"
    for host in $hosts; do
        if remote_execute "$host" "systemctl is-active --quiet redis-$TARGET_PORT"; then
            echo "  ✓ $host:$TARGET_PORT - 服务运行正常"
        else
            echo "  ✗ $host:$TARGET_PORT - 服务未运行"
            ((issues++))
        fi
    done

    echo
    if [[ $issues -eq 0 ]]; then
        echo "🎉 健康检查通过，集群状态良好"
    else
        echo "⚠️  发现 $issues 个问题，请检查集群状态"
        exit 1
    fi
}

# 处理slots命令
cmd_slots() {
    local hosts=($(get_target_hosts))
    local host="${hosts[0]}"

    local password
    if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
        log_error "获取Redis密码失败"
        exit 1
    fi

    local slots
    slots=$(get_cluster_slots "$host" "$TARGET_PORT" "$password")

    format_output "$OUTPUT_FORMAT" "Redis集群槽分布 ($host:$TARGET_PORT)" "$slots"
}

# 处理failover命令
cmd_failover() {
    local hosts=($(get_target_hosts))
    local host="${hosts[0]}"

    local password
    if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
        log_error "获取Redis密码失败"
        exit 1
    fi

    echo "执行手动故障转移: $host:$TARGET_PORT"

    local result
    result=$(execute_redis_command "$host" "$TARGET_PORT" "$password" "cluster failover")

    if [[ "$result" == "OK" ]]; then
        echo "✓ 故障转移命令执行成功"
        echo "等待故障转移完成..."
        sleep 10

        # 检查新的集群状态
        local nodes
        nodes=$(get_cluster_nodes "$host" "$TARGET_PORT" "$password")
        format_output "$OUTPUT_FORMAT" "故障转移后的集群节点状态" "$nodes"
    else
        echo "✗ 故障转移命令执行失败: $result"
        exit 1
    fi
}

# 处理reset命令
cmd_reset() {
    local hosts=($(get_target_hosts))

    echo "⚠️  警告: 此操作将重置集群状态，所有数据将丢失！"
    read -p "确认继续? (输入 'YES' 确认): " confirm

    if [[ "$confirm" != "YES" ]]; then
        echo "操作已取消"
        exit 0
    fi

    echo "重置Redis集群状态:"

    for host in $hosts; do
        local password
        if ! password=$(get_redis_password "$host" "$TARGET_PORT"); then
            echo "✗ $host:$TARGET_PORT - 获取密码失败"
            continue
        fi

        local result
        result=$(execute_redis_command "$host" "$TARGET_PORT" "$password" "cluster reset")

        if [[ "$result" == "OK" ]]; then
            echo "✓ $host:$TARGET_PORT - 重置成功"
        else
            echo "✗ $host:$TARGET_PORT - 重置失败: $result"
        fi
    done
}

# 主函数
main() {
    case "$COMMAND" in
        password)
            cmd_password
            ;;
        status)
            cmd_status
            ;;
        nodes)
            cmd_nodes
            ;;
        info)
            cmd_info
            ;;
        ping)
            cmd_ping
            ;;
        restart)
            cmd_restart
            ;;
        stop)
            cmd_stop
            ;;
        start)
            cmd_start
            ;;
        health)
            cmd_health
            ;;
        slots)
            cmd_slots
            ;;
        failover)
            cmd_failover
            ;;
        reset)
            cmd_reset
            ;;
        *)
            echo "错误: 未知命令 '$COMMAND'" >&2
            echo "使用 --help 查看可用命令" >&2
            exit 1
            ;;
    esac
}

# 执行主函数
main
