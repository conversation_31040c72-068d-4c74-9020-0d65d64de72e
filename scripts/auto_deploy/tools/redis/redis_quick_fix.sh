#!/bin/bash
# Redis快速修复脚本
# 专门用于解决Redis密码认证失败问题

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载配置
source "$PROJECT_ROOT/lib/common.sh"
source "$PROJECT_ROOT/config/hosts.conf"

# Redis配置
REDIS_HOME="/apps/redis"
REDIS_CONFIG_DIR="/apps/redis/conf"
REDIS_DATA_DIR="/apps/data/redis"

log_info "Redis密码认证快速修复工具"
log_info "================================"

# 获取Redis主机列表
REDIS_NODES=()
for host in "${REDIS_HOSTS[@]}"; do
    if [[ -n "${REDIS_HOST_PORTS[$host]:-}" ]]; then
        host_ports="${REDIS_HOST_PORTS[$host]}"
        IFS=',' read -ra ports <<< "$host_ports"
        for port in "${ports[@]}"; do
            REDIS_NODES+=("$host:$port")
        done
    fi
done

log_info "发现Redis节点: ${#REDIS_NODES[@]} 个"
for node in "${REDIS_NODES[@]}"; do
    log_info "  - $node"
done

echo
log_info "开始快速诊断和修复..."

# 修复函数
fix_redis_node() {
    local node=$1
    local host="${node%:*}"
    local port="${node#*:}"
    
    log_info "修复节点: $node"
    
    # 1. 检查并重启Redis服务
    log_info "  1. 检查Redis服务状态..."
    if ! remote_execute "$host" "systemctl is-active --quiet redis-$port"; then
        log_warn "    Redis服务未运行，正在启动..."
        remote_execute "$host" "systemctl start redis-$port"
        sleep 5
    else
        log_info "    Redis服务正在运行，重启以确保配置生效..."
        remote_execute "$host" "systemctl restart redis-$port"
        sleep 30
    fi
    
    # 2. 验证端口监听
    log_info "  2. 验证端口监听..."
    if remote_execute "$host" "netstat -tlnp | grep :$port" >/dev/null 2>&1; then
        log_info "    ✓ 端口 $port 正在监听"
    else
        log_error "    ✗ 端口 $port 未监听"
        return 1
    fi
    
    # 3. 检查配置文件
    log_info "  3. 检查配置文件..."
    local config_file="$REDIS_CONFIG_DIR/redis-$port.conf"
    if remote_execute "$host" "test -f '$config_file'"; then
        log_info "    ✓ 配置文件存在"

        # 检查密码配置
        local requirepass
        local masterauth
        requirepass=$(remote_execute "$host" "grep '^requirepass' '$config_file' | awk '{print \$2}'" || echo "")
        masterauth=$(remote_execute "$host" "grep '^masterauth' '$config_file' | awk '{print \$2}'" || echo "")

        if [[ -n "$requirepass" ]]; then
            log_info "    ✓ 找到requirepass配置"
        else
            log_error "    ✗ 未找到requirepass配置"
            return 1
        fi

        if [[ -n "$masterauth" ]]; then
            log_info "    ✓ 找到masterauth配置"
        else
            log_error "    ✗ 未找到masterauth配置"
            return 1
        fi

        # 检查密码是否一致
        if [[ "$requirepass" == "$masterauth" ]]; then
            log_info "    ✓ requirepass和masterauth密码一致"
        else
            log_error "    ✗ requirepass和masterauth密码不一致，正在修复..."
            # 修复密码不一致问题 - 使用更安全的方法
            remote_execute "$host" "
                # 备份原配置文件
                cp '$config_file' '$config_file.backup'
                # 删除旧的masterauth行
                grep -v '^masterauth' '$config_file.backup' > '$config_file.tmp'
                # 添加新的masterauth行
                echo 'masterauth $requirepass' >> '$config_file.tmp'
                # 替换原文件
                mv '$config_file.tmp' '$config_file'
                echo '✓ masterauth已更新为与requirepass相同'
            "

            # 重启服务以应用更改
            remote_execute "$host" "systemctl restart redis-$port"
            sleep 30
            log_info "    ✓ 密码不一致问题已修复"
        fi
    else
        log_error "    ✗ 配置文件不存在: $config_file"
        return 1
    fi
    
    # 4. 测试连接
    log_info "  4. 测试Redis连接..."
    local password
    password=$(remote_execute "$host" "grep '^requirepass' '$config_file' | awk '{print \$2}'" 2>/dev/null || echo "")
    
    if [[ -n "$password" ]]; then
        # 测试密码认证
        local auth_result
        auth_result=$(remote_execute "$host" "REDISCLI_AUTH='$password' $REDIS_HOME/bin/redis-clia -h $host -p $port ping" 2>/dev/null || echo "FAILED")
        
        if [[ "$auth_result" == "PONG" ]]; then
            log_info "    ✓ 密码认证成功"
            return 0
        else
            # 尝试其他认证方式
            log_info "    ✓ 使用-a参数认证成功"
            auth_result=$(remote_execute "$host" "$REDIS_HOME/bin/redis-cli -h $host -p $port -a '$password' ping" 2>/dev/null || echo "FAILED")
            if [[ "$auth_result" == "PONG" ]]; then
                return 0
            else
                log_error "    ✗ 使用-a参数认证失败: $auth_result"
                log_error "    ✗ 密码认证失败"
                return 1
            fi
        fi
    else
        log_error "    ✗ 无法获取密码"
        return 1
    fi
}

# 批量修复所有节点
failed_nodes=()
success_count=0

for node in "${REDIS_NODES[@]}"; do
    echo
    if fix_redis_node "$node"; then
        log_info "✓ 节点 $node 修复成功"
        ((success_count++))
    else
        log_error "✗ 节点 $node 修复失败"
        failed_nodes+=("$node")
    fi
done

echo
log_info "修复完成"
log_info "================================"
log_info "成功修复: $success_count/${#REDIS_NODES[@]} 个节点"

if [[ ${#failed_nodes[@]} -gt 0 ]]; then
    log_error "失败节点: ${failed_nodes[*]}"
    echo
    log_error "手动修复建议："
    for failed_node in "${failed_nodes[@]}"; do
        failed_host="${failed_node%:*}"
        failed_port="${failed_node#*:}"
        echo
        log_error "节点 $failed_node:"
        log_error "  1. 检查服务状态:"
        log_error "     ssh $failed_host 'systemctl status redis-$failed_port'"
        log_error "  2. 查看日志:"
        log_error "     ssh $failed_host 'journalctl -u redis-$failed_port -n 50'"
        log_error "     ssh $failed_host 'tail -50 $REDIS_DATA_DIR/log/redis-$failed_port.log'"
        log_error "  3. 检查配置:"
        log_error "     ssh $failed_host 'cat $REDIS_CONFIG_DIR/redis-$failed_port.conf | grep -E \"^(port|bind|requirepass|masterauth)\"'"
        log_error "  4. 手动重启:"
        log_error "     ssh $failed_host 'systemctl restart redis-$failed_port'"
    done
    exit 1
else
    log_info "✓ 所有Redis节点都已成功修复"
    echo
    log_info "现在可以重新运行Redis部署脚本:"
    log_info "  ./deploy_redis.sh --skip-install --skip-config"
    exit 0
fi
