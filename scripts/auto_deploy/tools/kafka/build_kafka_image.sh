#!/bin/bash
# Kafka Native 镜像构建脚本
# 构建包含 shell 脚本的 apache/kafka-native 镜像

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 脚本信息
SCRIPT_NAME="Kafka Native 镜像构建脚本"
SCRIPT_VERSION="1.0.0"

echo "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 配置参数
# =============================================================================

# 镜像配置
BASE_IMAGE="apache/kafka-native:3.9.0"
NEW_IMAGE_NAME="apache/kafka-native-with-scripts"
NEW_IMAGE_TAG="3.9.0"
FULL_IMAGE_NAME="${NEW_IMAGE_NAME}:${NEW_IMAGE_TAG}"

# 文件路径
DOCKERFILE_PATH="$PROJECT_ROOT/Dockerfile.kafka-native-with-scripts"
KAFKA_BIN_ZIP="$PROJECT_ROOT/software-repo/kafka-bin.zip"
BUILD_CONTEXT="$PROJECT_ROOT"

# 输出目录
OUTPUT_DIR="/apps/software/docker-images"
IMAGE_FILENAME="${NEW_IMAGE_NAME//\//_}_${NEW_IMAGE_TAG}.tar.gz"

# =============================================================================
# 参数解析
# =============================================================================

DRY_RUN=false
FORCE_REBUILD=false
SKIP_EXPORT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force-rebuild)
            FORCE_REBUILD=true
            shift
            ;;
        --skip-export)
            SKIP_EXPORT=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --dry-run         仅显示将要执行的操作"
            echo "  --force-rebuild   强制重新构建镜像"
            echo "  --skip-export     跳过镜像导出"
            echo "  -h, --help        显示此帮助信息"
            echo ""
            echo "说明:"
            echo "  本脚本构建包含 shell 脚本的 apache/kafka-native 镜像"
            echo "  解决原镜像缺少 kafka-topics.sh 等管理脚本的问题"
            echo ""
            echo "构建的镜像: $FULL_IMAGE_NAME"
            echo "导出文件: $OUTPUT_DIR/$IMAGE_FILENAME"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# 辅助函数
# =============================================================================

log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $*"
}

log_warn() {
    echo "[WARN] $(date '+%Y-%m-%d %H:%M:%S') $*"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $*" >&2
}

# =============================================================================
# 环境检查函数
# =============================================================================

check_prerequisites() {
    log_info "检查构建环境..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将检查构建环境"
        return 0
    fi

    # 检查 Docker
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 未安装"
        exit 1
    fi

    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行"
        exit 1
    fi

    # 检查 Dockerfile
    if [[ ! -f "$DOCKERFILE_PATH" ]]; then
        log_error "Dockerfile 不存在: $DOCKERFILE_PATH"
        exit 1
    fi

    # 检查 kafka-bin.zip
    if [[ ! -f "$KAFKA_BIN_ZIP" ]]; then
        log_error "Kafka bin 文件不存在: $KAFKA_BIN_ZIP"
        exit 1
    fi

    # 检查 unzip 工具
    if ! command -v unzip >/dev/null 2>&1; then
        log_error "unzip 工具未安装，需要解压 kafka-bin.zip"
        exit 1
    fi

    # 检查基础镜像
    if ! docker image inspect "$BASE_IMAGE" >/dev/null 2>&1; then
        log_warn "基础镜像不存在，尝试拉取: $BASE_IMAGE"
        docker pull "$BASE_IMAGE"
    fi

    log_info "✓ 环境检查通过"
}

# =============================================================================
# 镜像构建函数
# =============================================================================

prepare_kafka_bin() {
    log_info "准备 Kafka bin 脚本..."

    local kafka_bin_dir="$PROJECT_ROOT/software-repo/kafka-bin"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将解压 kafka-bin.zip 到: $kafka_bin_dir"
        return 0
    fi

    # 清理旧的解压目录
    if [[ -d "$kafka_bin_dir" ]]; then
        log_info "清理旧的 kafka-bin 目录..."
        rm -rf "$kafka_bin_dir"
    fi

    # 创建解压目录
    mkdir -p "$kafka_bin_dir"

    # 解压 kafka-bin.zip
    log_info "解压 kafka-bin.zip..."
    cd "$kafka_bin_dir"
    if unzip -q "$KAFKA_BIN_ZIP"; then
        log_info "✓ kafka-bin.zip 解压成功"
    else
        log_error "kafka-bin.zip 解压失败"
        exit 1
    fi

    # 验证解压结果
    if [[ -d "bin" ]]; then
        log_info "✓ 发现 bin 目录"
    elif find . -name "bin" -type d | grep -q .; then
        # 如果 bin 目录在子目录中，移动到根目录
        local bin_path=$(find . -name "bin" -type d | head -1)
        log_info "移动 bin 目录: $bin_path -> ./bin"
        chown -R "1000:1000" ./bin
        chmod -R 755 ./bin/*.sh
        mv "$bin_path" ./bin
    else
        log_error "未找到 bin 目录"
        exit 1
    fi

    # 验证关键脚本
    local required_scripts=(
        "kafka-topics.sh"
        "kafka-console-producer.sh"
        "kafka-console-consumer.sh"
        "kafka-broker-api-versions.sh"
    )

    for script in "${required_scripts[@]}"; do
        if [[ -f "bin/$script" ]]; then
            log_info "✓ 发现脚本: $script"
        else
            log_error "缺少脚本: $script"
            exit 1
        fi
    done

    cd "$PROJECT_ROOT"
    log_info "✓ Kafka bin 脚本准备完成"
}

build_image() {
    log_info "构建 Kafka Native 镜像..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将构建镜像: $FULL_IMAGE_NAME"
        log_info "[DRY RUN] 构建上下文: $BUILD_CONTEXT"
        log_info "[DRY RUN] Dockerfile: $DOCKERFILE_PATH"
        return 0
    fi

    # 检查镜像是否已存在
    if docker image inspect "$FULL_IMAGE_NAME" >/dev/null 2>&1 && [[ "$FORCE_REBUILD" != "true" ]]; then
        log_info "镜像已存在: $FULL_IMAGE_NAME"
        log_info "使用 --force-rebuild 参数强制重新构建"
        return 0
    fi

    # 构建镜像
    log_info "开始构建镜像..."
    log_info "基础镜像: $BASE_IMAGE"
    log_info "目标镜像: $FULL_IMAGE_NAME"
    log_info "构建上下文: $BUILD_CONTEXT"

    if docker build \
        -f "$DOCKERFILE_PATH" \
        -t "$FULL_IMAGE_NAME" \
        --build-arg BASE_IMAGE="$BASE_IMAGE" \
        "$BUILD_CONTEXT"; then
        log_info "✓ 镜像构建成功: $FULL_IMAGE_NAME"
    else
        log_error "镜像构建失败"
        exit 1
    fi

    # 验证镜像
    log_info "验证构建的镜像..."
    if docker run --rm "$FULL_IMAGE_NAME" kafka-topics.sh --version >/dev/null 2>&1; then
        log_info "✓ 镜像验证成功"
    else
        log_warn "镜像验证失败，但构建已完成"
    fi
}

export_image() {
    if [[ "$SKIP_EXPORT" == "true" ]]; then
        log_info "跳过镜像导出"
        return 0
    fi

    log_info "导出 Docker 镜像..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将导出镜像到: $OUTPUT_DIR/$IMAGE_FILENAME"
        return 0
    fi

    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"

    # 导出镜像
    log_info "导出镜像: $FULL_IMAGE_NAME -> $OUTPUT_DIR/$IMAGE_FILENAME"
    if docker save "$FULL_IMAGE_NAME" | gzip > "$OUTPUT_DIR/$IMAGE_FILENAME"; then
        log_info "✓ 镜像导出成功"
        log_info "文件大小: $(du -h "$OUTPUT_DIR/$IMAGE_FILENAME" | cut -f1)"
    else
        log_error "镜像导出失败"
        exit 1
    fi
}

cleanup_temp_files() {
    log_info "清理临时文件..."

    local kafka_bin_dir="$PROJECT_ROOT/software-repo/kafka-bin"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将清理临时目录: $kafka_bin_dir"
        return 0
    fi

    if [[ -d "$kafka_bin_dir" ]]; then
        log_info "删除临时目录: $kafka_bin_dir"
        rm -rf "$kafka_bin_dir"
        log_info "✓ 临时文件清理完成"
    else
        log_info "无需清理临时文件"
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始构建 Kafka Native 镜像..."

    # 环境检查
    check_prerequisites

    # 准备 Kafka bin 脚本
    prepare_kafka_bin

    # 构建镜像
    build_image

    # 导出镜像
    export_image

    # 清理临时文件
    cleanup_temp_files

    # 显示结果
    log_info "=== 构建完成 ==="
    log_info "镜像名称: $FULL_IMAGE_NAME"
    if [[ "$SKIP_EXPORT" != "true" ]]; then
        log_info "导出文件: $OUTPUT_DIR/$IMAGE_FILENAME"
    fi
    log_info ""
    log_info "使用方法:"
    log_info "  # 加载镜像"
    log_info "  docker load < $OUTPUT_DIR/$IMAGE_FILENAME"
    log_info "  # 运行容器"
    log_info "  docker run --rm $FULL_IMAGE_NAME kafka-topics.sh --version"
    log_info "  # 更新部署脚本中的镜像名称"
    log_info "  # KAFKA_IMAGE=\"$FULL_IMAGE_NAME\""
}

# 执行主函数
main "$@"
