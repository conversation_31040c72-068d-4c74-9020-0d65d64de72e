# Dockerfile for Apache Kafka Native with Shell Scripts
# 基于 apache/kafka-native 镜像，添加必要的 shell 脚本支持
# 解决 apache/kafka-native 镜像缺少 kafka-topics.sh 等脚本的问题

FROM apache/kafka:3.9.0

# 维护者信息
LABEL maintainer="DevOps Team"
LABEL description="Apache Kafka Native with shell scripts for management operations"
LABEL version="3.9.0-with-scripts"

# 设置工作目录
WORKDIR /opt/kafka

# 创建 bin 目录（如果不存在）
RUN mkdir -p /opt/kafka/bin

# 复制 Kafka shell 脚本
# 注意：需要先在构建机器上解压 kafka-bin.zip，然后直接复制 bin 目录
# 假设 kafka-bin.zip 解压后的结构为 bin/ 目录包含所有脚本
COPY software-repo/kafka-bin/bin/ /opt/kafka/bin/

# 设置脚本执行权限并验证安装
RUN pwd && \
    # 验证关键脚本是否存在
    ls -la /opt/kafka/bin/ && \
    echo "=== 验证关键脚本 ===" && \
    for script in kafka-topics.sh kafka-console-producer.sh kafka-console-consumer.sh kafka-broker-api-versions.sh; do \
        if [ -f "/opt/kafka/bin/$script" ]; then \
            echo "✓ $script 已安装"; \
        else \
            echo "✗ $script 缺失"; \
        fi; \
    done

# 更新 PATH 环境变量，确保脚本可以直接调用
ENV PATH="/opt/kafka/bin:${PATH}"

# 创建符号链接到标准位置（如果需要）
RUN ln -sf /opt/kafka/bin/* /usr/local/bin/ 2>/dev/null || true

# 验证安装
RUN echo "=== 验证 Kafka 脚本安装 ===" && \
    which kafka-topics.sh || echo "kafka-topics.sh not in PATH" && \
    which kafka-console-producer.sh || echo "kafka-console-producer.sh not in PATH" && \
    which kafka-console-consumer.sh || echo "kafka-console-consumer.sh not in PATH" && \
    which kafka-broker-api-versions.sh || echo "kafka-broker-api-versions.sh not in PATH" && \
    echo "=== 脚本安装验证完成 ==="

# 保持原有的入口点和命令
# apache/kafka-native 镜像的默认配置将被保留

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD kafka-broker-api-versions.sh --bootstrap-server localhost:9092 || exit 1

# 暴露标准端口
EXPOSE 9092 9093

# 设置默认用户（与原镜像保持一致）
USER 1000:1000

# 工作目录
WORKDIR /opt/kafka
