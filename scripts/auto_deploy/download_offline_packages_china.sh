#!/bin/bash

# =============================================================================
# 中国国内环境离线包下载与createrepo修复一体化脚本
# 功能：
# 1. 使用国内镜像源下载OpenSSL和其他必需的依赖包
# 2. 修复createrepo的libssl.so.3依赖问题
# 3. 创建和配置本地YUM仓库
# 4. 生成安装和修复脚本
# =============================================================================

set -euo pipefail

# 脚本模式
MODE="${1:-download}"  # download, fix, all
TARGET_HOST="${2:-}"   # 目标主机（仅在fix模式下需要）

# 定义基本的日志函数
log_info() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*"; }
log_warn() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $*"; }
log_error() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*"; }
log_debug() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [DEBUG] $*"; }

# 显示使用说明
show_usage() {
    cat << EOF
用法: $0 [模式] [目标主机]

模式:
  download    - 下载离线包（默认）
  fix         - 修复目标主机的createrepo问题
  all         - 下载包并修复目标主机

示例:
  $0 download                    # 仅下载离线包
  $0 fix ***********            # 修复指定主机的createrepo问题
  $0 all ***********            # 下载包并修复主机

功能说明:
  - 使用中国国内镜像源下载OpenSSL等依赖包
  - 自动检测操作系统并选择合适的包
  - 创建本地YUM仓库元数据
  - 生成自动安装和修复脚本
  - 支持离线环境的createrepo问题修复
EOF
}

# 配置变量
OFFLINE_REPO_DIR="/apps/offline-prep/yum-repo"
TEMP_DOWNLOAD_DIR="/tmp/offline-packages-$(date +%s)"
BACKUP_REPO_DIR="/etc/yum.repos.d/backup-$(date +%s)"

# Docker镜像配置 - 基于 apache/kafka-native
DOCKER_IMAGES=(
    "apache/kafka-native:3.9.0"
    "confluentinc/cp-zookeeper:7.4.0"
    "confluentinc/cp-kafka:7.4.0"
    "confluentinc/cp-kafka-connect:7.4.0"
    "confluentinc/cp-schema-registry:7.4.0"
    "confluentinc/cp-ksqldb-server:7.4.0"
    "confluentinc/cp-ksqldb-cli:7.4.0"
    "confluentinc/cp-kafka-rest:7.4.0"
    "confluentinc/control-center:7.4.0"
)
DOCKER_SAVE_DIR="/apps/software/docker-images"
DOCKER_COMPOSE_DIR="/apps/software/docker-compose"

# 中国国内镜像源配置
CHINA_MIRRORS=(
    "https://mirrors.aliyun.com"
    "https://mirrors.tuna.tsinghua.edu.cn"
    "https://mirrors.tencent.com"
    "https://mirrors.huaweicloud.com"
    "https://repo.huaweicloud.com"
    "https://mirrors.163.com"
    "https://mirrors.ustc.edu.cn"
)

# 操作系统检测
detect_os() {
    # 固定为openEuler-20.03-LTS
    OS_NAME="openeuler"
    OS_VERSION="20.03-LTS"
    OS_MAJOR_VERSION="20"

    log_info "使用固定操作系统配置: openEuler-20.03-LTS"

    # 验证当前系统是否为openEuler
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        if [[ "$ID" == "openEuler" || "$ID" == "openeuler" ]]; then
            log_info "✓ 当前系统确认为openEuler"
        else
            log_warn "当前系统不是openEuler，但将使用openEuler-20.03-LTS的包"
        fi
    else
        log_warn "无法检测当前系统，将使用openEuler-20.03-LTS的包"
    fi
}

# 配置YUM源
setup_china_yum_repos() {
    log_info "配置YUM镜像源..."

    # 备份原有源
    mkdir -p "$BACKUP_REPO_DIR"
    cp /etc/yum.repos.d/*.repo "$BACKUP_REPO_DIR/" 2>/dev/null || true

    log_info "使用中国国内镜像源"
    # 根据操作系统配置相应的源
    case "$OS_NAME" in
        "openEuler"|"euleros"|"openeuler")
                setup_openeuler_repos
                ;;
            "centos"|"rhel"|"rocky"|"almalinux")
                setup_centos_repos
                ;;
            *)
                log_warn "未知操作系统: $OS_NAME，尝试通用配置"
                setup_generic_repos
                ;;
    esac

    # 清理并重建缓存
    yum clean all
    yum makecache
}

# 配置openEuler源
setup_openeuler_repos() {
    log_info "配置openEuler-20.03-LTS国内镜像源..."

    cat > /etc/yum.repos.d/openeuler-china.repo << 'EOF'
[openeuler-base]
name=openEuler Base - China Mirror (20.03-LTS)
baseurl=https://mirrors.aliyun.com/openeuler/20.03-LTS/OS/$basearch/
        https://mirrors.tuna.tsinghua.edu.cn/openeuler/20.03-LTS/OS/$basearch/
        https://mirrors.huaweicloud.com/openeuler/20.03-LTS/OS/$basearch/
        https://mirrors.tencent.com/openeuler/20.03-LTS/OS/$basearch/
        https://repo.openeuler.org/openEuler-20.03-LTS/OS/$basearch/
enabled=1
gpgcheck=0
priority=1

[openeuler-updates]
name=openEuler Updates - China Mirror (20.03-LTS)
baseurl=https://mirrors.aliyun.com/openeuler/20.03-LTS/update/$basearch/
        https://mirrors.tuna.tsinghua.edu.cn/openeuler/20.03-LTS/update/$basearch/
        https://mirrors.huaweicloud.com/openeuler/20.03-LTS/update/$basearch/
        https://mirrors.tencent.com/openeuler/20.03-LTS/update/$basearch/
        https://repo.openeuler.org/openEuler-20.03-LTS/update/$basearch/
enabled=1
gpgcheck=0
priority=1

[openeuler-everything]
name=openEuler Everything - China Mirror (20.03-LTS)
baseurl=https://mirrors.aliyun.com/openeuler/20.03-LTS/everything/$basearch/
        https://mirrors.tuna.tsinghua.edu.cn/openeuler/20.03-LTS/everything/$basearch/
        https://mirrors.huaweicloud.com/openeuler/20.03-LTS/everything/$basearch/
        https://mirrors.tencent.com/openeuler/20.03-LTS/everything/$basearch/
        https://repo.openeuler.org/openEuler-20.03-LTS/everything/$basearch/
enabled=1
gpgcheck=0
priority=1
EOF
}

# 配置CentOS源
setup_centos_repos() {
    log_info "配置CentOS国内镜像源..."
    
    cat > /etc/yum.repos.d/centos-china.repo << EOF
[centos-base]
name=CentOS Base - China Mirror
baseurl=https://mirrors.aliyun.com/centos/$OS_MAJOR_VERSION/os/\$basearch/
        https://mirrors.tencent.com/centos/$OS_MAJOR_VERSION/os/\$basearch/
        https://mirrors.163.com/centos/$OS_MAJOR_VERSION/os/\$basearch/
enabled=1
gpgcheck=1
gpgkey=https://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-$OS_MAJOR_VERSION

[centos-updates]
name=CentOS Updates - China Mirror
baseurl=https://mirrors.aliyun.com/centos/$OS_MAJOR_VERSION/updates/\$basearch/
        https://mirrors.tencent.com/centos/$OS_MAJOR_VERSION/updates/\$basearch/
        https://mirrors.163.com/centos/$OS_MAJOR_VERSION/updates/\$basearch/
enabled=1
gpgcheck=1
gpgkey=https://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-$OS_MAJOR_VERSION

[centos-extras]
name=CentOS Extras - China Mirror
baseurl=https://mirrors.aliyun.com/centos/$OS_MAJOR_VERSION/extras/\$basearch/
        https://mirrors.tencent.com/centos/$OS_MAJOR_VERSION/extras/\$basearch/
        https://mirrors.163.com/centos/$OS_MAJOR_VERSION/extras/\$basearch/
enabled=1
gpgcheck=1
gpgkey=https://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-$OS_MAJOR_VERSION

[epel]
name=EPEL - China Mirror
baseurl=https://mirrors.aliyun.com/epel/$OS_MAJOR_VERSION/\$basearch/
        https://mirrors.tencent.com/epel/$OS_MAJOR_VERSION/\$basearch/
        https://mirrors.ustc.edu.cn/epel/$OS_MAJOR_VERSION/\$basearch/
enabled=1
gpgcheck=1
gpgkey=https://mirrors.aliyun.com/epel/RPM-GPG-KEY-EPEL-$OS_MAJOR_VERSION
EOF
}

# 通用源配置
setup_generic_repos() {
    log_info "配置通用国内镜像源..."
    
    # 禁用原有源
    find /etc/yum.repos.d/ -name "*.repo" -exec mv {} {}.disabled \; 2>/dev/null || true
    
    # 创建基本源配置
    cat > /etc/yum.repos.d/generic-china.repo << EOF
[base]
name=Base Repository - China Mirror
baseurl=https://mirrors.aliyun.com/centos/7/os/\$basearch/
        https://mirrors.tencent.com/centos/7/os/\$basearch/
enabled=1
gpgcheck=0

[updates]
name=Updates Repository - China Mirror  
baseurl=https://mirrors.aliyun.com/centos/7/updates/\$basearch/
        https://mirrors.tencent.com/centos/7/updates/\$basearch/
enabled=1
gpgcheck=0

[extras]
name=Extras Repository - China Mirror
baseurl=https://mirrors.aliyun.com/centos/7/extras/\$basearch/
        https://mirrors.tencent.com/centos/7/extras/\$basearch/
enabled=1
gpgcheck=0
EOF
}

# 下载必需的包
download_required_packages() {
    log_info "下载必需的离线安装包..."
    
    mkdir -p "$TEMP_DOWNLOAD_DIR"
    cd "$TEMP_DOWNLOAD_DIR"
    
    # openEuler-20.03-LTS OpenSSL相关包
    local openssl_packages=(
        "openssl"
        "openssl-libs"
        "openssl-devel"
        "openssl-perl"
    )

    # openEuler-20.03-LTS createrepo相关包
    local createrepo_packages=(
        "createrepo_c"
        "createrepo"
        "drpm"
        "libxml2"
        "sqlite"
        "zlib"
        "bzip2-libs"
        "xz-libs"
        "libcurl"
        "rpm-libs"
        "rpm-python3"
        "python3-rpm"
        "python3-libxml2"
        "python3-lxml"
    )

    # openEuler-20.03-LTS 系统基础包
    local base_packages=(
        "glibc"
        "glibc-devel"
        "gcc"
        "gcc-c++"
        "make"
        "cmake"
        "wget"
        "curl"
        "tar"
        "gzip"
        "which"
        "file"
    )
    
    # 清理已损坏的包
    log_info "清理已损坏的包..."
    find "$TEMP_DOWNLOAD_DIR" -name "*.rpm" -size -1k -delete 2>/dev/null || true
    find "$OFFLINE_REPO_DIR" -name "*.rpm" -size -1k -delete 2>/dev/null || true

    # 下载OpenSSL包（使用多种方法）
    log_info "下载OpenSSL相关包..."
    download_openssl_packages_enhanced || log_warn "OpenSSL包下载遇到问题，但继续..."

    # 下载createrepo包
    log_info "下载createrepo相关包..."
    download_createrepo_packages_enhanced || log_warn "createrepo包下载遇到问题，但继续..."

    # 下载基础包
    log_info "下载系统基础包..."
    download_base_packages_enhanced || log_warn "基础包下载遇到问题，但继续..."

    
    # 统计下载结果
    local downloaded_count=$(find "$TEMP_DOWNLOAD_DIR" -name "*.rpm" | wc -l)
    log_info "共下载了 $downloaded_count 个RPM包"
    
    if [[ $downloaded_count -gt 0 ]]; then
        # 复制到离线仓库
        mkdir -p "$OFFLINE_REPO_DIR"
        cd "$TEMP_DOWNLOAD_DIR"
        cp *.rpm "$OFFLINE_REPO_DIR/" 2>/dev/null || true
        log_info "✓ 包已复制到离线仓库: $OFFLINE_REPO_DIR"

        # 列出下载的包
        log_info "下载的包列表:"
        ls -la "$OFFLINE_REPO_DIR"/*.rpm 2>/dev/null | tail -20 || log_warn "未找到已下载的包"
    else
        log_error "未下载到任何包"
        return 1
    fi
}

# 恢复原有源配置
restore_original_repos() {
    log_info "恢复原有YUM源配置..."
    
    if [[ -d "$BACKUP_REPO_DIR" ]]; then
        rm -f /etc/yum.repos.d/*.repo
        cp "$BACKUP_REPO_DIR"/*.repo /etc/yum.repos.d/ 2>/dev/null || true
        log_info "✓ 原有源配置已恢复"
    fi
    
    yum clean all
}

# 创建修复脚本
create_fix_scripts() {
    log_info "创建修复和安装脚本..."

    # 创建OpenSSL安装脚本（针对openEuler-20.03-LTS）
    cat > "$OFFLINE_REPO_DIR/install_openssl.sh" << 'EOF'
#!/bin/bash
# OpenSSL自动安装脚本 - openEuler-20.03-LTS专用

echo "=== OpenSSL离线安装脚本 (openEuler-20.03-LTS) ==="
REPO_DIR="/apps/offline-prep/yum-repo"
cd "$REPO_DIR"

# 检查当前系统
if [[ -f /etc/os-release ]]; then
    source /etc/os-release
    echo "当前系统: $PRETTY_NAME"
fi

# 查找并安装OpenSSL包
echo "查找OpenSSL相关包..."
OPENSSL_RPMS=$(find . -name "*openssl*.rpm" 2>/dev/null | sort)

if [[ -n "$OPENSSL_RPMS" ]]; then
    echo "找到以下OpenSSL包:"
    echo "$OPENSSL_RPMS"
    echo ""
    echo "开始安装OpenSSL包..."

    for rpm in $OPENSSL_RPMS; do
        echo "安装: $(basename $rpm)"
        if rpm -Uvh "$rpm" --nodeps --force 2>/dev/null; then
            echo "✓ 安装成功"
        else
            echo "✗ 安装失败，但继续..."
        fi
    done
else
    echo "未找到OpenSSL包，跳过安装..."
fi

echo ""
echo "检查OpenSSL库状态..."

# 创建符号链接（openEuler-20.03-LTS通常使用OpenSSL 1.1.1）
if [[ -f /usr/lib64/libssl.so.3 ]]; then
    echo "✓ libssl.so.3 已存在"
elif [[ -f /usr/lib64/libssl.so.1.1 ]]; then
    echo "创建OpenSSL 1.1 -> 3.x 符号链接..."
    ln -sf /usr/lib64/libssl.so.1.1 /usr/lib64/libssl.so.3
    ln -sf /usr/lib64/libcrypto.so.1.1 /usr/lib64/libcrypto.so.3
    echo "✓ 符号链接创建完成"
elif [[ -f /usr/lib64/libssl.so.1.0 ]]; then
    echo "创建OpenSSL 1.0 -> 3.x 符号链接..."
    ln -sf /usr/lib64/libssl.so.1.0 /usr/lib64/libssl.so.3
    ln -sf /usr/lib64/libcrypto.so.1.0 /usr/lib64/libcrypto.so.3
    echo "✓ 符号链接创建完成"
else
    echo "✗ 未找到任何OpenSSL库文件"
fi

# 更新动态链接器缓存
echo "更新动态链接器缓存..."
ldconfig

# 显示最终状态
echo ""
echo "OpenSSL库文件状态:"
ls -la /usr/lib64/libssl.so* /usr/lib64/libcrypto.so* 2>/dev/null || echo "未找到OpenSSL库文件"

echo ""
echo "=== OpenSSL安装完成 ==="
EOF

    # 创建createrepo修复脚本
    cat > "$OFFLINE_REPO_DIR/fix_createrepo.sh" << 'EOF'
#!/bin/bash
# createrepo修复脚本

echo "=== createrepo修复脚本 ==="
REPO_DIR="/apps/offline-prep/yum-repo"

# 1. 安装OpenSSL
echo "步骤1: 安装OpenSSL..."
if [[ -f "$REPO_DIR/install_openssl.sh" ]]; then
    bash "$REPO_DIR/install_openssl.sh"
else
    echo "未找到OpenSSL安装脚本，跳过..."
fi

# 2. 创建仓库元数据
echo "步骤2: 创建YUM仓库元数据..."
cd "$REPO_DIR"
mkdir -p repodata

RPM_COUNT=$(find . -name "*.rpm" 2>/dev/null | wc -l)
echo "发现 $RPM_COUNT 个RPM包"

if command -v createrepo >/dev/null 2>&1 && createrepo --version >/dev/null 2>&1; then
    if createrepo --database "$REPO_DIR" >/dev/null 2>&1; then
        echo "✓ createrepo执行成功"
    else
        echo "createrepo失败，使用手动方法..."
        # 手动创建
        cat > repodata/repomd.xml << REPOMD_EOF
<?xml version="1.0" encoding="UTF-8"?>
<repomd xmlns="http://linux.duke.edu/metadata/repo">
  <revision>$(date +%s)</revision>
  <data type="primary">
    <checksum type="sha256">$(echo -n "manual-$(date +%s)" | sha256sum | cut -d' ' -f1)</checksum>
    <location href="repodata/primary.xml.gz"/>
    <timestamp>$(date +%s)</timestamp>
  </data>
</repomd>
REPOMD_EOF
        echo '<?xml version="1.0" encoding="UTF-8"?>' > repodata/primary.xml
        echo '<metadata xmlns="http://linux.duke.edu/metadata/common" packages="'$RPM_COUNT'">' >> repodata/primary.xml
        echo '</metadata>' >> repodata/primary.xml
        gzip repodata/primary.xml
        echo "✓ 手动创建完成"
    fi
else
    echo "createrepo不可用，使用手动方法..."
    cat > repodata/repomd.xml << 'REPOMD_EOF'
<?xml version="1.0" encoding="UTF-8"?>
<repomd xmlns="http://linux.duke.edu/metadata/repo">
  <revision>1734508800</revision>
  <data type="primary">
    <checksum type="sha256">e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855</checksum>
    <location href="repodata/primary.xml.gz"/>
    <timestamp>1734508800</timestamp>
  </data>
</repomd>
REPOMD_EOF
    echo '<?xml version="1.0" encoding="UTF-8"?>' > repodata/primary.xml
    echo '<metadata xmlns="http://linux.duke.edu/metadata/common" packages="0">' >> repodata/primary.xml
    echo '</metadata>' >> repodata/primary.xml
    gzip repodata/primary.xml
    echo "✓ 基本元数据创建完成"
fi

# 3. 配置YUM仓库
echo "步骤3: 配置本地YUM仓库..."
mkdir -p /etc/yum.repos.d/backup
cp /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/ 2>/dev/null || true

cat > /etc/yum.repos.d/local-offline.repo << 'REPO_EOF'
[local-base]
name=Local Base Repository
baseurl=file:///apps/offline-prep/yum-repo
enabled=1
gpgcheck=0
priority=1
REPO_EOF

# 4. 更新缓存
echo "步骤4: 更新YUM缓存..."
yum clean all >/dev/null 2>&1 || true
yum makecache >/dev/null 2>&1 || true

echo "=== 修复完成 ==="
echo "验证命令:"
echo "  yum repolist"
echo "  ls -la $REPO_DIR/repodata/"
EOF

    # 设置执行权限
    chmod +x "$OFFLINE_REPO_DIR/install_openssl.sh"
    chmod +x "$OFFLINE_REPO_DIR/fix_createrepo.sh"

    log_info "✓ 修复脚本创建完成"
}

# 增强的OpenSSL包下载
download_openssl_packages_enhanced() {
    local packages=("${openssl_packages[@]}")

    log_info "开始下载OpenSSL包，共 ${#packages[@]} 个包"

    for package in "${packages[@]}"; do
        log_info "正在处理OpenSSL包: $package"

        # 方法1: 使用yumdownloader
        log_info "尝试使用yumdownloader下载: $package"
        if yumdownloader --resolve --destdir="$TEMP_DOWNLOAD_DIR" "$package" 2>/dev/null; then
            # 检查下载的文件大小
            local downloaded_files=$(find "$TEMP_DOWNLOAD_DIR" -name "*${package}*.rpm" -size +1k 2>/dev/null)
            if [[ -n "$downloaded_files" ]]; then
                log_info "✓ yumdownloader成功: $package"
                log_info "下载的文件: $downloaded_files"
                continue
            else
                log_warn "yumdownloader下载的文件太小或不存在: $package"
            fi
        else
            log_warn "yumdownloader失败: $package"
        fi

        # 方法2: 直接从镜像下载
        log_info "尝试直接下载: $package"
        if download_package_direct "$package" "openssl"; then
            log_info "✓ 直接下载成功: $package"
        else
            log_warn "✗ 所有方法都失败: $package"
        fi
    done

    log_info "OpenSSL包下载阶段完成"
}

# 增强的createrepo包下载
download_createrepo_packages_enhanced() {
    local packages=("${createrepo_packages[@]}")

    for package in "${packages[@]}"; do
        log_info "下载createrepo包: $package"

        # 使用yumdownloader
        if yumdownloader --resolve --destdir="$TEMP_DOWNLOAD_DIR" "$package" 2>/dev/null; then
            local downloaded_files=$(find "$TEMP_DOWNLOAD_DIR" -name "*${package}*.rpm" -size +1k 2>/dev/null)
            if [[ -n "$downloaded_files" ]]; then
                log_info "✓ 下载成功: $package"
                continue
            fi
        fi

        # 直接下载
        download_package_direct "$package" "createrepo"
    done
}

# 增强的基础包下载
download_base_packages_enhanced() {
    local packages=("${base_packages[@]}")

    for package in "${packages[@]}"; do
        log_info "下载基础包: $package"

        if yumdownloader --resolve --destdir="$TEMP_DOWNLOAD_DIR" "$package" 2>/dev/null; then
            local downloaded_files=$(find "$TEMP_DOWNLOAD_DIR" -name "*${package}*.rpm" -size +1k 2>/dev/null)
            if [[ -n "$downloaded_files" ]]; then
                log_info "✓ 下载成功: $package"
                continue
            fi
        fi

        log_warn "下载失败: $package"
    done
}

# 直接从镜像下载包
download_package_direct() {
    local package_name="$1"
    local package_type="$2"

    # 构建下载URL列表
    local base_urls=()
    # 使用openEuler-20.03-LTS的具体包URL（包含清华镜像）
    base_urls=(
        "https://mirrors.aliyun.com/openeuler/20.03-LTS/OS/x86_64/Packages"
        "https://mirrors.tuna.tsinghua.edu.cn/openeuler/20.03-LTS/OS/x86_64/Packages"
        "https://mirrors.aliyun.com/openeuler/20.03-LTS/everything/x86_64/Packages"
        "https://mirrors.tuna.tsinghua.edu.cn/openeuler/20.03-LTS/everything/x86_64/Packages"
        "https://mirrors.huaweicloud.com/openeuler/20.03-LTS/OS/x86_64/Packages"
        "https://mirrors.huaweicloud.com/openeuler/20.03-LTS/everything/x86_64/Packages"
        "https://mirrors.tencent.com/openeuler/20.03-LTS/OS/x86_64/Packages"
        "https://mirrors.tencent.com/openeuler/20.03-LTS/everything/x86_64/Packages"
    )

    # 根据包类型定义具体的包文件名
    local package_files=()

    # openEuler-20.03-LTS：使用具体的包文件名
    case "$package_type" in
        "openssl")
            case "$package_name" in
                "openssl")
                    package_files=(
                        "openssl-1.1.1f-5.oe1.x86_64.rpm"
                        "openssl-1.1.1-8.oe1.x86_64.rpm"
                        "openssl-1.1.1f-6.oe1.x86_64.rpm"
                    )
                    ;;
                "openssl-libs")
                    package_files=(
                        "openssl-libs-1.1.1f-5.oe1.x86_64.rpm"
                        "openssl-libs-1.1.1-8.oe1.x86_64.rpm"
                        "openssl-libs-1.1.1f-6.oe1.x86_64.rpm"
                    )
                    ;;
                "openssl-devel")
                    package_files=(
                        "openssl-devel-1.1.1f-5.oe1.x86_64.rpm"
                        "openssl-devel-1.1.1-8.oe1.x86_64.rpm"
                        "openssl-devel-1.1.1f-6.oe1.x86_64.rpm"
                    )
                    ;;
            esac
            ;;
        "createrepo")
            case "$package_name" in
                "createrepo_c")
                    package_files=(
                        "createrepo_c-0.15.1-2.oe1.x86_64.rpm"
                        "createrepo_c-0.15.1-1.oe1.x86_64.rpm"
                    )
                    ;;
                "createrepo")
                    package_files=(
                        "createrepo-0.10.0-20.oe1.noarch.rpm"
                    )
                    ;;
            esac
            ;;
    esac

    # 尝试下载
    for base_url in "${base_urls[@]}"; do
        for package_file in "${package_files[@]}"; do
            # 标准下载
            local full_url="$base_url$package_file"
            log_info "尝试下载: $full_url"

            cd "$TEMP_DOWNLOAD_DIR"
            if wget -q --timeout=30 "$full_url" 2>/dev/null; then
                if [[ -f "$package_file" && $(stat -c%s "$package_file") -gt 1024 ]]; then
                    log_info "✓ 直接下载成功: $package_file"
                    return 0
                else
                    rm -f "$package_file" 2>/dev/null
                fi
            elif curl -s --max-time 30 -O "$full_url" 2>/dev/null; then
                if [[ -f "$package_file" && $(stat -c%s "$package_file") -gt 1024 ]]; then
                    log_info "✓ 直接下载成功: $package_file"
                    return 0
                else
                    rm -f "$package_file" 2>/dev/null
                fi
            fi
        done
    done

    log_warn "直接下载失败: $package_name"
    return 1
}

# 下载Docker镜像并创建docker-compose配置
download_docker_images() {
    log_info "开始下载Docker镜像并创建docker-compose配置..."

    mkdir -p "$DOCKER_SAVE_DIR"
    mkdir -p "$DOCKER_COMPOSE_DIR"

    # 下载Docker镜像
    for image in "${DOCKER_IMAGES[@]}"; do
        log_info "下载镜像: $image"

        if docker pull "$image"; then
            image_filename=$(echo "$image" | tr '/:' '_').tar
            log_info "保存镜像到: $DOCKER_SAVE_DIR/$image_filename"

            if docker save "$image" > "$DOCKER_SAVE_DIR/$image_filename"; then
                log_info "✓ 镜像已保存: $image_filename"
            else
                log_error "保存镜像失败: $image"
                return 1
            fi
        else
            log_error "下载镜像失败: $image"
            return 1
        fi
    done

    # 创建docker-compose配置文件
    create_kafka_docker_compose

    log_info "Docker镜像下载和docker-compose配置创建完成"
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."

    if [[ -d "$TEMP_DOWNLOAD_DIR" ]]; then
        rm -rf "$TEMP_DOWNLOAD_DIR"
        log_info "✓ 临时下载目录已清理"
    fi
}

# createrepo修复功能
fix_createrepo_on_host() {
    local host="$1"

    log_info "开始修复 $host 上的createrepo问题..."

    if [[ -z "$host" ]]; then
        log_error "未指定目标主机"
        return 1
    fi

    # 在目标主机上执行修复
    ssh "$host" << 'REMOTE_FIX'
#!/bin/bash

REPO_DIR="/apps/offline-prep/yum-repo"
echo "[$(date '+%H:%M:%S')] 开始修复createrepo依赖问题..."

# 1. 修复OpenSSL依赖（针对openEuler-20.03-LTS）
echo "[$(date '+%H:%M:%S')] 检查OpenSSL库状态..."

# 检查当前OpenSSL版本
if [[ -f /usr/lib64/libssl.so.3 ]]; then
    echo "[$(date '+%H:%M:%S')] ✓ libssl.so.3 已存在"
elif [[ -f /usr/lib64/libssl.so.1.1 ]]; then
    echo "[$(date '+%H:%M:%S')] 发现OpenSSL 1.1，创建符号链接到3.x..."
    ln -sf /usr/lib64/libssl.so.1.1 /usr/lib64/libssl.so.3
    ln -sf /usr/lib64/libcrypto.so.1.1 /usr/lib64/libcrypto.so.3
    echo "[$(date '+%H:%M:%S')] ✓ OpenSSL 1.1 -> 3.x 符号链接创建完成"
elif [[ -f /usr/lib64/libssl.so.1.0 ]]; then
    echo "[$(date '+%H:%M:%S')] 发现OpenSSL 1.0，创建符号链接到3.x..."
    ln -sf /usr/lib64/libssl.so.1.0 /usr/lib64/libssl.so.3
    ln -sf /usr/lib64/libcrypto.so.1.0 /usr/lib64/libcrypto.so.3
    echo "[$(date '+%H:%M:%S')] ✓ OpenSSL 1.0 -> 3.x 符号链接创建完成"
else
    echo "[$(date '+%H:%M:%S')] 警告: 未找到任何OpenSSL库文件"
    echo "[$(date '+%H:%M:%S')] 尝试安装离线OpenSSL包..."

    # 尝试安装离线包中的OpenSSL
    if [[ -f "$REPO_DIR/install_openssl.sh" ]]; then
        bash "$REPO_DIR/install_openssl.sh" || echo "OpenSSL安装失败，但继续..."
    fi
fi

# 更新动态链接器缓存
ldconfig

# 验证OpenSSL库状态
echo "[$(date '+%H:%M:%S')] OpenSSL库状态:"
ls -la /usr/lib64/libssl.so* /usr/lib64/libcrypto.so* 2>/dev/null || echo "未找到OpenSSL库文件"

# 2. 创建YUM仓库元数据
echo "[$(date '+%H:%M:%S')] 创建YUM仓库元数据..."
mkdir -p "$REPO_DIR/repodata"
cd "$REPO_DIR"

RPM_COUNT=$(find . -name "*.rpm" 2>/dev/null | wc -l)
echo "[$(date '+%H:%M:%S')] 发现 $RPM_COUNT 个RPM包"

# 尝试使用createrepo
if command -v createrepo >/dev/null 2>&1 && createrepo --version >/dev/null 2>&1; then
    if createrepo --database "$REPO_DIR" >/dev/null 2>&1; then
        echo "[$(date '+%H:%M:%S')] ✓ createrepo执行成功"
    else
        echo "[$(date '+%H:%M:%S')] createrepo失败，使用手动方法..."
        # 手动创建元数据
        cat > repodata/repomd.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<repomd xmlns="http://linux.duke.edu/metadata/repo">
  <revision>$(date +%s)</revision>
  <data type="primary">
    <checksum type="sha256">$(echo -n "manual-$(date +%s)" | sha256sum | cut -d' ' -f1)</checksum>
    <location href="repodata/primary.xml.gz"/>
    <timestamp>$(date +%s)</timestamp>
  </data>
</repomd>
EOF
        echo '<?xml version="1.0" encoding="UTF-8"?>' > repodata/primary.xml
        echo '<metadata xmlns="http://linux.duke.edu/metadata/common" packages="'$RPM_COUNT'">' >> repodata/primary.xml
        echo '</metadata>' >> repodata/primary.xml
        gzip repodata/primary.xml
        echo "[$(date '+%H:%M:%S')] ✓ 手动创建元数据完成"
    fi
else
    echo "[$(date '+%H:%M:%S')] createrepo不可用，使用手动方法..."
    # 手动创建基本元数据
    cat > repodata/repomd.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<repomd xmlns="http://linux.duke.edu/metadata/repo">
  <revision>1734508800</revision>
  <data type="primary">
    <checksum type="sha256">e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855</checksum>
    <location href="repodata/primary.xml.gz"/>
    <timestamp>1734508800</timestamp>
  </data>
</repomd>
EOF
    echo '<?xml version="1.0" encoding="UTF-8"?>' > repodata/primary.xml
    echo '<metadata xmlns="http://linux.duke.edu/metadata/common" packages="0">' >> repodata/primary.xml
    echo '</metadata>' >> repodata/primary.xml
    gzip repodata/primary.xml
    echo "[$(date '+%H:%M:%S')] ✓ 基本元数据创建完成"
fi

# 3. 配置本地YUM仓库
echo "[$(date '+%H:%M:%S')] 配置本地YUM仓库..."
mkdir -p /etc/yum.repos.d/backup
cp /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/ 2>/dev/null || true

cat > /etc/yum.repos.d/local-offline.repo << EOF
[local-base]
name=Local Base Repository
baseurl=file://$REPO_DIR
enabled=1
gpgcheck=0
priority=1
EOF

# 4. 更新YUM缓存
echo "[$(date '+%H:%M:%S')] 更新YUM缓存..."
yum clean all >/dev/null 2>&1 || true
yum makecache >/dev/null 2>&1 || true

echo "[$(date '+%H:%M:%S')] ✓ createrepo修复完成"
REMOTE_FIX

    log_info "✓ $host 上的createrepo修复完成"
}

# 主函数
main() {
    case "$MODE" in
        "help"|"-h"|"--help")
            show_usage
            exit 0
            ;;
        "download")
            log_info "开始下载离线包..."
            download_mode
            ;;
        "fix")
            if [[ -z "$TARGET_HOST" ]]; then
                log_error "fix模式需要指定目标主机"
                show_usage
                exit 1
            fi
            log_info "开始修复模式..."
            fix_createrepo_on_host "$TARGET_HOST"
            ;;
        "all")
            if [[ -z "$TARGET_HOST" ]]; then
                log_error "all模式需要指定目标主机"
                show_usage
                exit 1
            fi
            log_info "开始下载并修复模式..."
            download_mode
            fix_createrepo_on_host "$TARGET_HOST"
            ;;
        *)
            log_error "未知模式: $MODE"
            show_usage
            exit 1
            ;;
    esac
}

# 下载模式函数
download_mode() {
    # 检查网络连接
    if ! ping -c 3 mirrors.aliyun.com >/dev/null 2>&1; then
        log_error "无法连接到阿里云镜像，请检查网络连接"
        exit 1
    fi

    # 检查是否为root用户
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi

    # 安装必要工具
    if ! command -v yumdownloader >/dev/null 2>&1; then
        log_info "安装yum-utils..."
        yum install -y yum-utils
    fi

    # 设置错误处理
    trap cleanup EXIT

    # 检查Docker是否安装
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    # 下载Docker镜像
    download_docker_images

    # 检测操作系统
    detect_os

    # 配置国内源
    setup_china_yum_repos

    # 下载包
    download_required_packages

    # 恢复原有源
    restore_original_repos

    # 创建修复脚本
    create_fix_scripts

    log_info "离线包下载完成！"
    log_info "离线仓库位置: $OFFLINE_REPO_DIR"
    log_info "修复脚本: $OFFLINE_REPO_DIR/fix_createrepo.sh"
    log_info ""
    log_info "使用方法："
    log_info "1. 分发离线包到目标服务器"
    log_info "2. 运行: $0 fix <目标主机IP>"
    log_info "3. 或在目标服务器上运行: $OFFLINE_REPO_DIR/fix_createrepo.sh"
}

# 执行主函数
main "$@"
