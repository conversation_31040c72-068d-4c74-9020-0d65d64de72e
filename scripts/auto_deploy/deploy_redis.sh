#!/bin/bash
# Redis集群部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化Redis集群部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="Redis集群部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# Redis特定配置
# =============================================================================

# Redis配置
REDIS_USER="redis"
REDIS_GROUP="redis"
REDIS_HOME="/apps/redis"
REDIS_DATA_DIR="/apps/data/redis"
REDIS_LOG_DIR="/var/log/redis"
REDIS_CONFIG_DIR="$REDIS_HOME/conf"

# 二进制包配置
REDIS_PACKAGE_DIR="/apps/software/redis"
REDIS_BINARY_PACKAGE="redis-7.0.8.tar.gz"
# Redis集群端口配置（从hosts.conf中获取）
# REDIS_CLUSTER_PORTS 和 REDIS_HOST_PORTS 在hosts.conf中定义

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INSTALL=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false
UNINSTALL=false
BACKUP_DATA=true
CUSTOM_PASSWORD=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --uninstall)
            UNINSTALL=true
            shift
            ;;
        --no-backup)
            BACKUP_DATA=false
            shift
            ;;
        --password)
            CUSTOM_PASSWORD="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install      跳过二进制包安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过集群初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  --uninstall         卸载Redis集群"
            echo "  --no-backup         卸载时不备份数据"
            echo "  --password <密码>   指定自定义Redis密码"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# Redis依赖安装函数
# =============================================================================

# 恢复原始YUM仓库配置
restore_original_yum_repos() {
    local host=$1

    log_info "在 $host 上恢复原始YUM仓库配置..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上恢复原始YUM仓库配置"
        return 0
    fi

    remote_execute "$host" "
        # 检查并禁用local.repo（如果存在）
        if [[ -f '/etc/yum.repos.d/local.repo' ]]; then
            echo '检测到local.repo文件，先禁用以避免仓库冲突...'
            # 备份local.repo
            cp /etc/yum.repos.d/local.repo /etc/yum.repos.d/local.repo.disabled.\$(date +%s)
            # 禁用local.repo
            mv /etc/yum.repos.d/local.repo /etc/yum.repos.d/local.repo.disabled
            echo '✓ local.repo已禁用并备份'
        fi

        # 检查是否存在备份的仓库配置
        backup_found=false

        # 查找backup目录
        if [[ -d '/etc/yum.repos.d/backup' ]]; then
            echo '找到backup目录: /etc/yum.repos.d/backup'
            if [[ -f '/etc/yum.repos.d/backup/net.repo' ]]; then
                echo '找到net.repo文件，恢复原始仓库配置...'
                cp /etc/yum.repos.d/backup/net.repo /etc/yum.repos.d/
                backup_found=true
            fi
        fi

        # 查找以backup为前缀的目录
        if [[ \"\$backup_found\" == \"false\" ]]; then
            for backup_dir in /etc/yum.repos.d/backup*; do
                if [[ -d \"\$backup_dir\" && -f \"\$backup_dir/net.repo\" ]]; then
                    echo \"找到备份目录: \$backup_dir\"
                    echo \"恢复net.repo文件...\"
                    cp \"\$backup_dir/net.repo\" /etc/yum.repos.d/
                    backup_found=true
                    break
                fi
            done
        fi

        if [[ \"\$backup_found\" == \"true\" ]]; then
            echo '✓ 原始仓库配置已恢复'

            # 验证恢复的仓库配置
            if [[ -f '/etc/yum.repos.d/net.repo' ]]; then
                echo '验证恢复的net.repo配置...'
                # 检查仓库配置是否有效
                if grep -q '\\[.*\\]' /etc/yum.repos.d/net.repo; then
                    echo '✓ net.repo配置格式正确'
                else
                    echo '警告: net.repo配置格式可能有问题'
                fi
            fi

            # 清理YUM缓存并重建
            echo '清理并重建YUM缓存...'
            yum clean all
            yum makecache
            echo '✓ YUM缓存重建完成'
        else
            echo '警告: 未找到备份的net.repo文件'
            # 如果没有找到备份，重新启用local.repo
            if [[ -f '/etc/yum.repos.d/local.repo.disabled' ]]; then
                echo '未找到原始仓库备份，重新启用local.repo...'
                mv /etc/yum.repos.d/local.repo.disabled /etc/yum.repos.d/local.repo
                echo '✓ local.repo已重新启用'
            fi
        fi
    "
}

# 尝试从本地仓库安装Redis
install_redis_from_local_repo() {
    local host=$1
    local target_version="7.2.7"

    log_info "在 $host 上尝试从本地仓库安装Redis $target_version..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上从本地仓库安装Redis"
        return 0
    fi

    # 首先恢复原始仓库配置
    restore_original_yum_repos "$host"

    # 尝试安装Redis
    local install_result
    install_result=$(remote_execute "$host" "
        echo '检查可用的Redis版本...'
        available_versions=\$(yum list available redis --showduplicates 2>/dev/null | grep redis | awk '{print \$2}' | sort -V)
        echo \"可用版本: \$available_versions\"

        # 查找目标版本或最接近的版本
        target_version='$target_version'
        best_version=''

        # 检查是否有完全匹配的版本
        if echo \"\$available_versions\" | grep -q \"^\$target_version\"; then
            best_version=\$target_version
        else
            # 查找最接近的版本（优先选择7.2.x系列）
            best_version=\$(echo \"\$available_versions\" | grep '^7\.2\.' | tail -1)
            if [[ -z \"\$best_version\" ]]; then
                # 如果没有7.2.x，选择最新的7.x版本
                best_version=\$(echo \"\$available_versions\" | grep '^7\.' | tail -1)
            fi
            if [[ -z \"\$best_version\" ]]; then
                # 如果没有7.x，选择最新版本
                best_version=\$(echo \"\$available_versions\" | tail -1)
            fi
        fi

        if [[ -n \"\$best_version\" ]]; then
            echo \"选择安装版本: \$best_version\"
            if yum install -y redis-\$best_version; then
                echo \"✓ Redis安装成功: \$best_version\"
                redis-server --version
                exit 0
            else
                echo \"✗ Redis安装失败\"
                exit 1
            fi
        else
            echo \"✗ 未找到可用的Redis版本\"
            exit 1
        fi
    " 2>&1)

    local exit_code=$?

    if [[ $exit_code -eq 0 ]]; then
        log_info "✓ Redis从本地仓库安装成功"
        log_info "$install_result"
        return 0
    else
        log_warn "✗ Redis从本地仓库安装失败"
        log_warn "$install_result"
        return 1
    fi
}

# 安装Redis（优先本地仓库）
install_redis_dependencies() {
    local host=$1

    log_info "在 $host 上安装Redis（优先本地仓库）..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装Redis"
        return 0
    fi

    # 首先尝试从本地仓库安装Redis
    if install_redis_from_local_repo "$host"; then
        log_info "✓ Redis已从本地仓库安装完成"

        # 安装Redis运行所需的额外依赖包
        local additional_packages=(
            "openssl-libs" "zlib" "systemd-libs"
            "net-tools" "procps-ng" "util-linux"
        )

        log_info "安装Redis运行所需的额外依赖包..."
        for package in "${additional_packages[@]}"; do
            if remote_execute "$host" "yum install -y $package" 2>/dev/null; then
                log_debug "✓ 额外依赖包安装成功: $package"
            else
                log_debug "- 额外依赖包安装失败: $package (可能已存在)"
            fi
        done

        return 0
    else
        log_error "✗ 无法从本地仓库安装Redis，放弃安装"
        log_error "请确保本地虚拟机镜像包含Redis 7.2.7或相近版本"
        return 1
    fi
}

# 注意：离线包安装函数已移至 common.sh 中统一实现

# 安装可选包
install_optional_packages() {
    local host=$1
    shift
    local packages=("$@")

    log_info "尝试安装可选包（失败不影响主流程）: ${packages[*]}"

    for package in "${packages[@]}"; do
        if remote_execute "$host" "yum install -y $package" 2>/dev/null; then
            log_debug "✓ 可选包安装成功: $package"
        else
            log_debug "- 可选包安装失败: $package (忽略)"
        fi
    done
}

# =============================================================================
# Redis编译安装函数
# =============================================================================

prepare_redis_environment() {
    local host=$1
    
    log_info "在 $host 上准备Redis环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备Redis环境"
        return 0
    fi
    
    # 创建Redis用户
    remote_execute "$host" "
        if ! id $REDIS_USER >/dev/null 2>&1; then
            groupadd -g 1002 $REDIS_GROUP
            useradd -u 1002 -g $REDIS_GROUP -r -s /bin/false -d /var/lib/redis $REDIS_USER
        fi
    "
    
    # 检查离线环境
    check_offline_environment "$host"

    # 配置离线YUM仓库（如果是离线模式）
    setup_offline_yum_repo "$host"
    log_info "在 $host 创建Redis部署目录..."

    # 获取该主机的Redis端口列表
    local host_ports=""
    if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
        host_ports="${REDIS_HOST_PORTS[$host]}"
    else
        log_warn "未找到主机 $host 的Redis端口配置，跳过"
        return 0
    fi

    log_info "主机 $host 的Redis端口: $host_ports"

    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $REDIS_HOME/{bin,conf,logs}
        mkdir -p $REDIS_DATA_DIR/{data,log,backup}
        mkdir -p /var/run/redis
        # 为该主机的每个端口创建数据目录
        IFS=',' read -r -a HOST_PORTS <<< '$host_ports'
        for port in \"\${HOST_PORTS[@]}\"; do
            mkdir -p $REDIS_DATA_DIR/\$port
        done
        chown -R $REDIS_USER:$REDIS_GROUP $REDIS_HOME $REDIS_DATA_DIR /var/run/redis
        chmod 755 $REDIS_HOME $REDIS_DATA_DIR
        chmod 750 /var/run/redis
    "

    # 验证目录创建和权限设置
    log_info "验证 $host 上的Redis目录结构..."

    local validation_failed=false

    # 检查基础目录是否存在
    local base_dirs=("$REDIS_HOME" "$REDIS_HOME/bin" "$REDIS_HOME/conf" "$REDIS_HOME/logs"
                     "$REDIS_DATA_DIR" "$REDIS_DATA_DIR/data" "$REDIS_DATA_DIR/log"
                     "$REDIS_DATA_DIR/backup" "/var/run/redis")

    for dir in "${base_dirs[@]}"; do
        if ! remote_execute "$host" "test -d '$dir'"; then
            log_error "目录不存在: $dir"
            validation_failed=true
        else
            log_debug "✓ 目录存在: $dir"
        fi
    done

    # 检查端口特定目录
    remote_execute "$host" "
        IFS=',' read -r -a HOST_PORTS <<< '$host_ports'
        for port in \"\${HOST_PORTS[@]}\"; do
            if [[ ! -d \"$REDIS_DATA_DIR/\$port\" ]]; then
                echo \"错误: 端口目录不存在: $REDIS_DATA_DIR/\$port\"
                exit 1
            else
                echo \"✓ 端口目录存在: $REDIS_DATA_DIR/\$port\"
            fi
        done
    " || validation_failed=true

    # 检查目录权限和所有者
    remote_execute "$host" "
        # 检查所有者
        for dir in '$REDIS_HOME' '$REDIS_DATA_DIR' '/var/run/redis'; do
            owner=\$(stat -c '%U:%G' \"\$dir\" 2>/dev/null)
            if [[ \"\$owner\" != \"$REDIS_USER:$REDIS_GROUP\" ]]; then
                echo \"错误: 目录 \$dir 所有者不正确，期望: $REDIS_USER:$REDIS_GROUP，实际: \$owner\"
                exit 1
            else
                echo \"✓ 目录所有者正确: \$dir (\$owner)\"
            fi
        done

        # 检查权限
        redis_home_perm=\$(stat -c '%a' '$REDIS_HOME' 2>/dev/null)
        data_dir_perm=\$(stat -c '%a' '$REDIS_DATA_DIR' 2>/dev/null)
        run_dir_perm=\$(stat -c '%a' '/var/run/redis' 2>/dev/null)

        if [[ \"\$redis_home_perm\" != \"755\" ]]; then
            echo \"错误: $REDIS_HOME 权限不正确，期望: 755，实际: \$redis_home_perm\"
            exit 1
        fi

        if [[ \"\$data_dir_perm\" != \"755\" ]]; then
            echo \"错误: $REDIS_DATA_DIR 权限不正确，期望: 755，实际: \$data_dir_perm\"
            exit 1
        fi

        if [[ \"\$run_dir_perm\" != \"750\" ]]; then
            echo \"错误: /var/run/redis 权限不正确，期望: 750，实际: \$run_dir_perm\"
            exit 1
        fi

        echo \"✓ 所有目录权限设置正确\"
    " || validation_failed=true

    # 测试Redis用户的写权限
    remote_execute "$host" "
        # 测试写权限
        test_dirs=('$REDIS_HOME/logs' '$REDIS_DATA_DIR/data' '$REDIS_DATA_DIR/log'
                   '$REDIS_DATA_DIR/backup' '/var/run/redis')

        for test_dir in \"\${test_dirs[@]}\"; do
            test_file=\"\$test_dir/.write_test_\$(date +%s)\"
            if sudo -u $REDIS_USER touch \"\$test_file\" 2>/dev/null; then
                sudo -u $REDIS_USER rm -f \"\$test_file\" 2>/dev/null
                echo \"✓ Redis用户对 \$test_dir 有写权限\"
            else
                echo \"错误: Redis用户对 \$test_dir 没有写权限\"
                exit 1
            fi
        done

        # 测试端口目录写权限
        IFS=',' read -r -a HOST_PORTS <<< '$host_ports'
        for port in \"\${HOST_PORTS[@]}\"; do
            port_dir=\"$REDIS_DATA_DIR/\$port\"
            test_file=\"\$port_dir/.write_test_\$(date +%s)\"
            if sudo -u $REDIS_USER touch \"\$test_file\" 2>/dev/null; then
                sudo -u $REDIS_USER rm -f \"\$test_file\" 2>/dev/null
                echo \"✓ Redis用户对端口目录 \$port_dir 有写权限\"
            else
                echo \"错误: Redis用户对端口目录 \$port_dir 没有写权限\"
                exit 1
            fi
        done
    " || validation_failed=true

    # 检查磁盘空间
    remote_execute "$host" "
        # 检查数据目录磁盘空间（至少需要1GB可用空间）
        available_space=\$(df '$REDIS_DATA_DIR' | awk 'NR==2 {print \$4}')
        min_space=1048576  # 1GB in KB

        if [[ \$available_space -lt \$min_space ]]; then
            echo \"警告: $REDIS_DATA_DIR 可用磁盘空间不足1GB (当前: \$((\$available_space/1024))MB)\"
        else
            echo \"✓ $REDIS_DATA_DIR 磁盘空间充足 (可用: \$((\$available_space/1024))MB)\"
        fi
    "

    if [[ "$validation_failed" == "true" ]]; then
        log_error "Redis目录结构验证失败: $host"
        return 1
    else
        log_info "✓ Redis目录结构验证成功: $host"
    fi

    # 安装运行时依赖 - 使用混合安装策略
    install_redis_dependencies "$host"
    
    log_info "Redis环境准备完成: $host"
}

install_redis_binary() {
    local host=$1

    log_info "在 $host 上配置Redis..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上配置Redis"
        return 0
    fi

    # 检查Redis是否已通过YUM安装
    local redis_installed=false
    if remote_execute "$host" "which redis-server" 2>/dev/null; then
        redis_installed=true
        log_info "Redis已通过YUM安装"
    fi

    # 如果没有通过YUM安装，检查是否有二进制安装
    if [[ "$redis_installed" != "true" ]]; then
        if remote_execute "$host" "test -f $REDIS_HOME/bin/redis-server" 2>/dev/null; then
            if [[ "$FORCE_REINSTALL" != "true" ]]; then
                log_info "Redis二进制版本已安装在 $host，跳过安装"
                return 0
            else
                log_info "强制重新安装Redis在 $host"
                # 停止现有服务
                remote_execute "$host" "
                    for port in \$(ls /etc/systemd/system/redis-*.service 2>/dev/null | sed 's/.*redis-\\([0-9]*\\)\\.service/\\1/'); do
                        systemctl stop redis-\$port 2>/dev/null || true
                        systemctl disable redis-\$port 2>/dev/null || true
                    done
                "
            fi
        else
            log_error "Redis未安装，请先运行install_redis_dependencies函数"
            return 1
        fi
    fi

    # 配置Redis环境
    remote_execute "$host" "
        # 创建Redis配置目录
        mkdir -p '$REDIS_CONFIG_DIR'

        # 如果是YUM安装的Redis，设置符号链接以保持兼容性
        if [[ '$redis_installed' == 'true' ]]; then
            echo '配置YUM安装的Redis环境...'

            # 创建Redis home目录结构
            mkdir -p '$REDIS_HOME/bin'

            # 创建符号链接到系统安装的Redis
            ln -sf /usr/bin/redis-server '$REDIS_HOME/bin/redis-server'
            ln -sf /usr/bin/redis-cli '$REDIS_HOME/bin/redis-cli'
            ln -sf /usr/bin/redis-sentinel '$REDIS_HOME/bin/redis-sentinel'
            ln -sf /usr/bin/redis-benchmark '$REDIS_HOME/bin/redis-benchmark'

            # 检查是否存在其他Redis工具
            if [[ -f /usr/bin/redis-check-aof ]]; then
                ln -sf /usr/bin/redis-check-aof '$REDIS_HOME/bin/redis-check-aof'
            fi
            if [[ -f /usr/bin/redis-check-rdb ]]; then
                ln -sf /usr/bin/redis-check-rdb '$REDIS_HOME/bin/redis-check-rdb'
            fi

            echo '✓ YUM安装的Redis环境配置完成'
        fi

        # 设置目录权限
        chown -R $REDIS_USER:$REDIS_GROUP '$REDIS_HOME' '$REDIS_CONFIG_DIR'
        chmod +x '$REDIS_HOME/bin/'* 2>/dev/null || true

        # 创建配置文件模板（如果不存在）
        if [[ ! -f '$REDIS_CONFIG_DIR/redis-template.conf' ]]; then
            # 尝试从系统默认配置复制
            if [[ -f /etc/redis.conf ]]; then
                cp /etc/redis.conf '$REDIS_CONFIG_DIR/redis-template.conf'
            elif [[ -f /etc/redis/redis.conf ]]; then
                cp /etc/redis/redis.conf '$REDIS_CONFIG_DIR/redis-template.conf'
            else
                # 创建基础配置模板
                cat > '$REDIS_CONFIG_DIR/redis-template.conf' << 'TEMPLATE_EOF'
# Redis基础配置模板 - YUM安装版本
port 6379
bind 0.0.0.0
protected-mode no
daemonize yes
pidfile /var/run/redis/redis.pid
logfile /apps/data/redis/log/redis.log
loglevel notice
dir /apps/data/redis/data
dbfilename dump.rdb
maxmemory 4gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes
appendonly yes
appendfilename \"appendonly.aof\"
appendfsync everysec
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 15000
cluster-require-full-coverage no
# 密码配置将在实际配置文件中设置
# requirepass <将被替换>
# masterauth <将被替换>
tcp-keepalive 300
timeout 0
tcp-backlog 511
maxclients 10000
slowlog-log-slower-than 10000
slowlog-max-len 128
TEMPLATE_EOF
            fi
            chown $REDIS_USER:$REDIS_GROUP '$REDIS_CONFIG_DIR/redis-template.conf'
        fi

        # 设置环境变量
        if ! grep -q 'REDIS_HOME' /etc/profile; then
            echo 'export REDIS_HOME=$REDIS_HOME' >> /etc/profile
            echo 'export PATH=\$PATH:\$REDIS_HOME/bin' >> /etc/profile
        fi

        echo 'Redis环境配置完成'
    "

    # 验证安装
    if remote_execute "$host" "$REDIS_HOME/bin/redis-server --version"; then
        log_info "Redis二进制包安装成功: $host"

        # 显示版本信息
        local redis_version
        redis_version=$(remote_execute "$host" "$REDIS_HOME/bin/redis-server --version")
        log_info "Redis版本: $redis_version"
    else
        log_error "Redis二进制包安装失败: $host"
        return 1
    fi
}

# =============================================================================
# Redis配置函数
# =============================================================================
generate_redis_config() {
    local host=$1
    local port=$2
    local password=$3
    local master_password=$4
    local is_master=${5:-true}
    
    log_info "为 $host:$port 生成Redis配置文件..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host:$port 生成Redis配置文件"
        return 0
    fi
    
    # 生成Redis配置文件
    local config_content="# Redis配置文件 - $host:$port
# 基础配置
port $port
bind 0.0.0.0
protected-mode no
daemonize yes
pidfile /var/run/redis/redis-$port.pid
logfile $REDIS_DATA_DIR/log/redis-$port.log
loglevel notice

# 数据目录
dir $REDIS_DATA_DIR/$port
dbfilename dump-$port.rdb

# 内存配置
maxmemory $REDIS_MAX_MEMORY
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# AOF配置
appendonly yes
appendfilename \"appendonly-$port.aof\"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 集群配置
cluster-enabled yes
cluster-config-file nodes-$port.conf
cluster-node-timeout 15000
cluster-require-full-coverage no

# 安全配置
requirepass $password
masterauth $master_password

# 网络优化
tcp-keepalive 300
timeout 0
tcp-backlog 511

# 客户端配置
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 内存优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
"
    
    # 写入配置文件
    remote_execute "$host" "cat > $REDIS_CONFIG_DIR/redis-$port.conf << 'EOF'
$config_content
EOF"
    
    log_info "Redis配置文件生成完成: $host:$port"
}

create_redis_service() {
    local host=$1
    local port=$2
    
    log_info "在 $host 上创建Redis-$port systemd服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上创建Redis-$port服务"
        return 0
    fi
    
    local service_content="[Unit]
Description=Redis In-Memory Data Store (Port $port)
After=network.target

[Service]
User=$REDIS_USER
Group=$REDIS_GROUP
Type=forking
PIDFile=/var/run/redis/redis-$port.pid
ExecStart=$REDIS_HOME/bin/redis-server $REDIS_CONFIG_DIR/redis-$port.conf
ExecReload=/bin/kill -HUP \$MAINPID
ExecStop=$REDIS_HOME/bin/redis-cli -p $port shutdown
Restart=always
RestartSec=10
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target"
    
    remote_execute "$host" "
        cat > /etc/systemd/system/redis-$port.service << 'EOF'
$service_content
EOF
        systemctl daemon-reload
        systemctl enable redis-$port
    "
    
    log_info "Redis-$port服务创建完成: $host"
}

# =============================================================================
# Redis卸载函数
# =============================================================================

# 备份Redis数据
backup_redis_data() {
    local host=$1
    local backup_dir="/backup/redis/$(date +%Y%m%d_%H%M%S)"

    log_info "在 $host 上备份Redis数据..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上备份Redis数据到 $backup_dir"
        return 0
    fi

    # 创建备份目录
    remote_execute "$host" "
        mkdir -p $backup_dir
        chown -R $REDIS_USER:$REDIS_GROUP $backup_dir 2>/dev/null || true
    " || {
        log_warn "创建备份目录失败: $host"
        return 1
    }

    # 备份该主机的Redis数据
    local host_ports=""
    if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
        host_ports="${REDIS_HOST_PORTS[$host]}"
        log_info "备份主机 $host 的Redis端口: $host_ports"

        # 解析端口列表
        IFS=',' read -ra ports <<< "$host_ports"
        for port in "${ports[@]}"; do
            log_info "备份Redis端口 $port 的数据..."

            # 执行BGSAVE命令生成RDB快照
            remote_execute "$host" "
                if systemctl is-active --quiet redis-$port; then
                    $REDIS_HOME/bin/redis-cli -p $port BGSAVE 2>/dev/null || echo '备份命令执行失败，继续...'
                    sleep 5  # 等待备份完成
                fi

                # 复制数据文件
                if [[ -d '$REDIS_DATA_DIR/$port' ]]; then
                    cp -r '$REDIS_DATA_DIR/$port' '$backup_dir/redis-$port-data' 2>/dev/null || echo '数据文件复制失败，继续...'
                fi

                # 复制配置文件
                if [[ -f '$REDIS_CONFIG_DIR/redis-$port.conf' ]]; then
                    cp '$REDIS_CONFIG_DIR/redis-$port.conf' '$backup_dir/redis-$port.conf' 2>/dev/null || echo '配置文件复制失败，继续...'
                fi

                # 复制日志文件
                if [[ -f '$REDIS_DATA_DIR/log/redis-$port.log' ]]; then
                    cp '$REDIS_DATA_DIR/log/redis-$port.log' '$backup_dir/redis-$port.log' 2>/dev/null || echo '日志文件复制失败，继续...'
                fi
            " || log_warn "端口 $port 数据备份失败，继续卸载..."
        done
    else
        log_warn "未找到主机 $host 的Redis端口配置，跳过数据备份"
    fi

    # 创建备份信息文件
    remote_execute "$host" "
        cat > '$backup_dir/backup_info.txt' << EOF
Redis数据备份信息
备份时间: $(date)
备份主机: $host
Redis版本: \$($REDIS_HOME/bin/redis-server --version 2>/dev/null || echo '未知')
备份目录: $backup_dir
EOF
    " || log_warn "创建备份信息文件失败: $host"

    log_info "Redis数据备份完成: $host -> $backup_dir"
}

# 停止Redis服务
stop_redis_services() {
    local host=$1

    log_info "在 $host 上停止Redis服务..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上停止Redis服务"
        return 0
    fi

    # 停止所有Redis服务
    remote_execute "$host" "
        # 查找所有Redis服务
        for service_file in /etc/systemd/system/redis-*.service; do
            if [[ -f \"\$service_file\" ]]; then
                service_name=\$(basename \"\$service_file\" .service)
                echo \"停止服务: \$service_name\"

                # 停止服务
                systemctl stop \"\$service_name\" 2>/dev/null || true

                # 等待服务完全停止
                timeout 30 bash -c \"while systemctl is-active --quiet \$service_name; do sleep 1; done\" || true

                # 禁用服务
                systemctl disable \"\$service_name\" 2>/dev/null || true

                echo \"✓ 服务 \$service_name 已停止并禁用\"
            fi
        done

        # 强制杀死可能残留的Redis进程
        pkill -f redis-server 2>/dev/null || true
        pkill -f redis-cli 2>/dev/null || true

        # 等待进程完全退出
        sleep 3

        # 检查是否还有Redis进程
        if pgrep -f redis-server >/dev/null; then
            echo \"警告: 仍有Redis进程在运行，强制终止...\"
            pkill -9 -f redis-server 2>/dev/null || true
        fi

        echo \"所有Redis服务已停止\"
        exit 0  # 确保命令成功退出
    " || log_warn "停止Redis服务时出现错误: $host"

    log_info "Redis服务停止完成: $host"
}

# 删除Redis服务文件
remove_redis_services() {
    local host=$1

    log_info "在 $host 上删除Redis服务文件..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上删除Redis服务文件"
        return 0
    fi

    remote_execute "$host" "
        # 删除systemd服务文件
        rm -f /etc/systemd/system/redis-*.service

        # 重新加载systemd配置
        systemctl daemon-reload

        echo \"Redis服务文件已删除\"
        exit 0  # 确保命令成功退出
    " || log_warn "删除Redis服务文件时出现错误: $host"

    log_info "Redis服务文件删除完成: $host"
}

# 删除Redis文件和目录
remove_redis_files() {
    local host=$1

    log_info "在 $host 上删除Redis文件和目录..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上删除Redis文件和目录"
        return 0
    fi

    remote_execute "$host" "
        echo \"检查Redis安装方式...\"

        # 检查是否通过YUM安装
        if rpm -q redis >/dev/null 2>&1; then
            echo \"检测到YUM安装的Redis，执行YUM卸载...\"
            yum remove -y redis
            echo \"✓ YUM安装的Redis已卸载\"
        else
            echo \"未检测到YUM安装的Redis\"
        fi

        echo \"删除Redis安装目录...\"
        if [[ -d '$REDIS_HOME' ]]; then
            rm -rf '$REDIS_HOME'
            echo \"✓ 已删除: $REDIS_HOME\"
        fi

        echo \"删除Redis数据目录...\"
        if [[ -d '$REDIS_DATA_DIR' ]]; then
            rm -rf '$REDIS_DATA_DIR'
            echo \"✓ 已删除: $REDIS_DATA_DIR\"
        fi

        echo \"删除Redis日志目录...\"
        if [[ -d '$REDIS_LOG_DIR' ]]; then
            rm -rf '$REDIS_LOG_DIR'
            echo \"✓ 已删除: $REDIS_LOG_DIR\"
        fi

        echo \"删除Redis配置目录...\"
        if [[ -d '$REDIS_CONFIG_DIR' ]]; then
            rm -rf '$REDIS_CONFIG_DIR'
            echo \"✓ 已删除: $REDIS_CONFIG_DIR\"
        fi

        echo \"删除Redis运行时目录...\"
        if [[ -d '/var/run/redis' ]]; then
            rm -rf '/var/run/redis'
            echo \"✓ 已删除: /var/run/redis\"
        fi

        echo \"删除系统Redis配置文件...\"
        rm -f /etc/redis.conf 2>/dev/null || true
        rm -rf /etc/redis/ 2>/dev/null || true

        echo \"删除Redis临时文件...\"
        rm -f /tmp/redis-*.sock 2>/dev/null || true
        rm -f /tmp/redis_*.tmp 2>/dev/null || true

        echo \"Redis文件和目录删除完成\"
        exit 0  # 确保命令成功退出
    " || log_warn "删除Redis文件和目录时出现错误: $host"

    log_info "Redis文件和目录删除完成: $host"
}

# 删除Redis用户和组
remove_redis_user() {
    local host=$1

    log_info "在 $host 上删除Redis用户和组..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上删除Redis用户和组"
        return 0
    fi

    remote_execute "$host" "
        # 删除Redis用户
        if id '$REDIS_USER' >/dev/null 2>&1; then
            userdel '$REDIS_USER' 2>/dev/null || true
            echo \"✓ 已删除用户: $REDIS_USER\"
        fi

        # 删除Redis组
        if getent group '$REDIS_GROUP' >/dev/null 2>&1; then
            groupdel '$REDIS_GROUP' 2>/dev/null || true
            echo \"✓ 已删除组: $REDIS_GROUP\"
        fi

        echo \"Redis用户和组删除完成\"
        exit 0  # 确保命令成功退出
    " || log_warn "删除Redis用户和组时出现错误: $host"

    log_info "Redis用户和组删除完成: $host"
}

# 清理环境变量和恢复仓库配置
cleanup_redis_environment() {
    local host=$1

    log_info "在 $host 上清理Redis环境变量和恢复仓库配置..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上清理Redis环境变量和恢复仓库配置"
        return 0
    fi

    remote_execute "$host" "
        # 从/etc/profile中移除Redis相关的环境变量
        if [[ -f /etc/profile ]]; then
            # 创建临时文件
            temp_file=\$(mktemp)

            # 过滤掉Redis相关的环境变量
            grep -v 'REDIS_HOME' /etc/profile > \"\$temp_file\" 2>/dev/null || true

            # 替换原文件
            if [[ -s \"\$temp_file\" ]]; then
                mv \"\$temp_file\" /etc/profile
                echo \"✓ 已从/etc/profile中移除Redis环境变量\"
            else
                rm -f \"\$temp_file\"
            fi
        fi

        # 清理可能的Redis相关的cron任务
        crontab -l 2>/dev/null | grep -v redis | crontab - 2>/dev/null || true

        # 恢复local.repo（如果之前被禁用）
        echo \"检查是否需要恢复local.repo...\"
        if [[ -f '/etc/yum.repos.d/local.repo.disabled' ]]; then
            echo \"发现被禁用的local.repo，正在恢复...\"
            mv /etc/yum.repos.d/local.repo.disabled /etc/yum.repos.d/local.repo
            echo \"✓ local.repo已恢复\"

            # 清理YUM缓存
            yum clean all
            echo \"✓ YUM缓存已清理\"
        fi

        # 移除Redis部署时添加的net.repo（如果存在且不是原始的）
        if [[ -f '/etc/yum.repos.d/net.repo' ]]; then
            echo \"检查net.repo是否为Redis部署时添加...\"
            # 如果存在local.repo，说明net.repo可能是Redis部署时恢复的
            if [[ -f '/etc/yum.repos.d/local.repo' ]]; then
                echo \"移除Redis部署时恢复的net.repo...\"
                mv /etc/yum.repos.d/net.repo /etc/yum.repos.d/net.repo.redis_backup.\$(date +%s)
                echo \"✓ net.repo已备份并移除\"
            fi
        fi

        echo \"Redis环境变量和仓库配置清理完成\"
        exit 0  # 确保命令成功退出
    " || log_warn "清理Redis环境变量时出现错误: $host"

    log_info "Redis环境变量和仓库配置清理完成: $host"
}

# 主卸载函数
uninstall_redis() {
    log_info "开始卸载Redis集群..."

    # 获取锁
    if ! acquire_lock "uninstall_redis"; then
        log_error "无法获取锁，可能有其他Redis操作正在运行"
        exit 1
    fi

    # 检查Redis主机配置
    if [[ ${#REDIS_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置Redis主机"
        exit 1
    fi

    # 确认卸载操作
    if [[ "$DRY_RUN" != "true" ]]; then
        echo
        echo "⚠️  警告: 即将卸载Redis集群"
        echo "影响的主机: ${REDIS_HOSTS[*]}"
        echo "数据备份: $([ "$BACKUP_DATA" == "true" ] && echo "是" || echo "否")"
        echo
        read -p "确认继续卸载? (输入 'YES' 确认): " confirm

        if [[ "$confirm" != "YES" ]]; then
            log_info "用户取消卸载操作"
            release_lock
            exit 0
        fi
    fi

    log_info "Redis集群卸载主机: ${REDIS_HOSTS[*]}"

    # 对每个主机执行卸载
    local failed_hosts=()
    for host in "${REDIS_HOSTS[@]}"; do
        log_info "开始卸载 $host 上的Redis..."

        # 使用子shell执行卸载操作，避免单个主机失败导致整个脚本退出
        (
            # 备份数据（如果启用）
            if [[ "$BACKUP_DATA" == "true" ]]; then
                backup_redis_data "$host" || log_warn "备份数据失败: $host"
            fi

            # 停止Redis服务
            stop_redis_services "$host" || log_warn "停止Redis服务失败: $host"

            # 删除服务文件
            remove_redis_services "$host" || log_warn "删除服务文件失败: $host"

            # 删除文件和目录
            remove_redis_files "$host" || log_warn "删除文件和目录失败: $host"

            # 删除用户和组
            remove_redis_user "$host" || log_warn "删除用户和组失败: $host"

            # 清理环境变量
            cleanup_redis_environment "$host" || log_warn "清理环境变量失败: $host"
        )

        # 检查卸载是否成功
        if [[ $? -eq 0 ]]; then
            log_info "✓ $host 上的Redis卸载完成"
        else
            log_error "✗ $host 上的Redis卸载失败"
            failed_hosts+=("$host")
        fi
    done

    # 报告卸载结果
    if [[ ${#failed_hosts[@]} -gt 0 ]]; then
        log_warn "以下主机卸载过程中出现错误: ${failed_hosts[*]}"
        log_warn "请手动检查这些主机的Redis状态"
    fi

    # 释放锁
    release_lock

    log_info "Redis集群卸载完成"

    if [[ "$BACKUP_DATA" == "true" ]]; then
        echo
        echo "📁 数据备份位置: /backup/redis/"
        echo "💡 提示: 备份数据保留在各主机上，如需要可手动清理"
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 检查是否为卸载模式
    if [[ "$UNINSTALL" == "true" ]]; then
        uninstall_redis
        exit 0
    fi

    log_info "开始Redis集群部署..."

    # 获取锁
    if ! acquire_lock "deploy_redis"; then
        log_error "无法获取锁，可能有其他Redis部署实例正在运行"
        exit 1
    fi
    
    # 检查Redis主机配置
    if [[ ${#REDIS_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置Redis主机"
        exit 1
    fi
    # 使用自定义密码或生成随机密码
    local password
    local master_password

    if [[ -n "$CUSTOM_PASSWORD" ]]; then
        password="$CUSTOM_PASSWORD"
        master_password="$CUSTOM_PASSWORD"
        log_info "使用自定义Redis密码"
    else
        password=$(generate_password)
        master_password="$password"  # 修复：使用相同密码确保集群节点间认证成功
        log_info "生成随机Redis密码"
    fi

    log_info "Redis密码配置: requirepass和masterauth使用相同密码"
    log_info "密码长度: ${#password}"

    # 获取唯一主机列表（去重）
    local unique_hosts=($(printf '%s\n' "${REDIS_HOSTS[@]}" | sort -u))

    log_info "Redis集群主机: ${unique_hosts[*]}"
    log_info "Redis集群端口: ${REDIS_CLUSTER_PORTS[*]}"
    log_info "Redis与NebulaGraph共享节点资源"

    # 对每个唯一主机进行部署
    for host in "${unique_hosts[@]}"; do
        log_info "开始在主机 $host 上部署Redis..."

        # 获取该主机的端口列表
        local host_ports=""
        if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
            host_ports="${REDIS_HOST_PORTS[$host]}"
        else
            log_warn "未找到主机 $host 的Redis端口配置，跳过"
            continue
        fi

        log_info "主机 $host 的Redis端口: $host_ports"

        # 检查是否已安装
        local already_installed=false
        if remote_execute "$host" "test -f $REDIS_HOME/bin/redis-server" 2>/dev/null; then
            if [[ "$FORCE_REINSTALL" != "true" ]]; then
                log_info "Redis已安装在 $host，跳过安装"
                already_installed=true
            else
                log_info "强制重新安装Redis在 $host"
            fi
        fi

        # Redis安装阶段
        if [[ "$already_installed" != "true" && "$SKIP_INSTALL" != "true" ]]; then
            # 准备环境
            prepare_redis_environment "$host"

            # 安装Redis（优先本地仓库）
            if ! install_redis_dependencies "$host"; then
                log_error "Redis安装失败，跳过主机 $host"
                continue
            fi

            # 配置Redis环境
            install_redis_binary "$host"
        elif [[ "$SKIP_INSTALL" == "true" ]]; then
            log_warn "跳过Redis安装"
        fi

        # 配置阶段 - 为该主机的每个端口创建配置和服务
        if [[ "$SKIP_CONFIG" != "true" ]]; then
            IFS=',' read -ra ports <<< "$host_ports"
            for port in "${ports[@]}"; do
                log_info "为 $host:$port 生成配置..."
                generate_redis_config "$host" "$port" "$password" "$master_password"
                create_redis_service "$host" "$port"
            done
        else
            log_warn "跳过Redis配置"
        fi

        log_info "✓ 主机 $host 上的Redis部署完成"
    done


    # 检查所有Redis实例是否已安装
    local all_installed=true
    for host in "${unique_hosts[@]}"; do
        local host_ports=""
        if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
            host_ports="${REDIS_HOST_PORTS[$host]}"
            IFS=',' read -ra ports <<< "$host_ports"
            for port in "${ports[@]}"; do
                if ! check_redis_installed "$host" "$port"; then
                    all_installed=false
                    log_info "$host:$port 节点未检测到完整Redis安装"
                fi
            done
        fi
    done

    if [[ "$all_installed" == "true" ]]; then
        log_info "所有Redis实例已安装并运行"
    fi
    # 启动服务
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_redis_cluster
        initialize_redis_cluster "$password"
    else
        log_warn "跳过Redis集群初始化"
    fi

    log_info "Redis集群部署完成"
    release_lock
    exit 0
}

check_redis_installed() {
    local host=$1
    local port=$2
    local service_running=false

    # 检查服务是否运行
    if remote_execute "$host" "systemctl is-active --quiet redis-$port"; then
        log_info "redis服务已在运行: $host:$port"
        service_running=true
    fi

    # 如果服务已完整安装
    if [[ "$service_running" == "true" ]]; then
        return 0  # 已安装
    fi

    return 1  # 未安装
}

# =============================================================================
# Redis集群初始化函数
# =============================================================================

start_redis_cluster() {
    log_info "启动Redis集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动Redis集群"
        return 0
    fi

    # 获取唯一主机列表
    local unique_hosts=($(printf '%s\n' "${REDIS_HOSTS[@]}" | sort -u))

    # 启动所有Redis实例
    for host in "${unique_hosts[@]}"; do
        local host_ports=""
        if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
            host_ports="${REDIS_HOST_PORTS[$host]}"
            IFS=',' read -ra ports <<< "$host_ports"
            for port in "${ports[@]}"; do
                log_info "启动Redis服务: $host:$port"
                start_service "$host" "redis-$port"

                # 等待服务启动
                if ! wait_for_port "$host" "$port" 60; then
                    log_error "Redis服务启动失败: $host:$port"
                    return 1
                fi
            done
        fi
    done

    log_info "所有Redis节点启动完成"
}

initialize_redis_cluster() {
    local cluster_password=$1

    log_info "初始化Redis集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将初始化Redis集群"
        return 0
    fi

    # 如果没有传入密码，尝试从配置文件获取
    if [[ -z "$cluster_password" ]]; then
        log_warn "未传入集群密码，尝试从配置文件获取..."
    fi

    # 构建集群节点列表（所有主机:端口组合）
    local cluster_nodes=()
    local unique_hosts=($(printf '%s\n' "${REDIS_HOSTS[@]}" | sort -u))

    # 为每个主机构建节点列表
    for host in "${unique_hosts[@]}"; do
        local host_ports=""
        if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
            host_ports="${REDIS_HOST_PORTS[$host]}"
            # 解析该主机的端口列表（支持逗号分隔的多端口）
            IFS=',' read -ra ports <<< "$host_ports"
            for port in "${ports[@]}"; do
                cluster_nodes+=("$host:$port")
            done
        fi
    done

    # 验证集群节点数量
    if [[ ${#cluster_nodes[@]} -lt 6 ]]; then
        log_error "Redis集群节点数量不足，需要至少6个节点（3主3从），当前只有${#cluster_nodes[@]}个节点"
        log_error "集群节点列表: ${cluster_nodes[*]}"
        log_error "请检查hosts.conf中的REDIS_HOSTS和REDIS_HOST_PORTS配置"
        return 1
    fi

    log_info "Redis集群节点列表 (${#cluster_nodes[@]}个节点): ${cluster_nodes[*]}"

    # 使用第一个节点执行集群初始化
    local first_node="${cluster_nodes[0]}"
    local first_host="${first_node%:*}"
    local first_port="${first_node#*:}"

    # 验证所有节点是否可达并正常运行
    log_info "验证所有Redis节点状态..."
    local failed_nodes=()
    for node in "${cluster_nodes[@]}"; do
        local node_host="${node%:*}"
        local node_port="${node#*:}"

        if ! wait_for_port "$node_host" "$node_port" 10; then
            log_warn "节点 $node 不可达或未启动"
            failed_nodes+=("$node")
        else
            log_debug "✓ 节点 $node 端口状态正常"
        fi
    done

    if [[ ${#failed_nodes[@]} -gt 0 ]]; then
        log_error "以下节点状态异常，无法创建集群: ${failed_nodes[*]}"
        log_error "请检查这些节点的Redis服务状态"
        return 1
    fi

    log_info "在 $first_host:$first_port 上执行集群初始化..."

    # 如果没有传入密码，从配置文件获取
    if [[ -z "$cluster_password" ]]; then
        local config_file="$REDIS_CONFIG_DIR/redis-$first_port.conf"
        local password_command="grep '^requirepass' \"$config_file\" | awk '{print \$2}'"
        cluster_password=$(ssh -o ConnectTimeout=$DEPLOY_TIMEOUT -o BatchMode=yes "$DEPLOY_USER@$first_host" "$password_command" 2>&1)
        log_info "从配置文件获取Redis集群密码"
    else
        log_info "使用传入的Redis集群密码"
    fi

    # 检查密码是否有效
    if [[ -z "$cluster_password" ]]; then
        log_error "无法获取Redis集群密码"
        log_error "请检查以下项目："
        log_error "1. 配置文件是否正确生成: $REDIS_CONFIG_DIR/redis-$first_port.conf"
        log_error "2. requirepass配置是否存在"
        log_error "3. SSH连接是否正常"
        return 1
    fi

    log_info "Redis集群密码已获取，长度: ${#cluster_password}"

    # 验证密码是否正确
    log_info "验证Redis节点密码认证..."
    local auth_failed_nodes=()
    for node in "${cluster_nodes[@]}"; do
        local node_host="${node%:*}"
        local node_port="${node#*:}"

        log_info "检查节点 $node 的Redis服务状态..."

        # 首先检查Redis服务是否运行
        if ! remote_execute "$node_host" "systemctl is-active --quiet redis-$node_port"; then
            log_warn "节点 $node 的Redis服务未运行，尝试启动..."
            if remote_execute "$node_host" "systemctl start redis-$node_port"; then
                log_info "✓ 节点 $node 的Redis服务启动成功"
                # 等待服务完全启动
                sleep 5
            else
                log_error "✗ 节点 $node 的Redis服务启动失败"
                auth_failed_nodes+=("$node")
                continue
            fi
        fi

        # 检查端口是否监听
        if ! remote_execute "$node_host" "netstat -tlnp | grep :$node_port" >/dev/null 2>&1; then
            log_warn "节点 $node 端口 $node_port 未监听"
            auth_failed_nodes+=("$node")
            continue
        fi

        # 测试无密码连接（检查是否需要密码）
        local no_auth_result
        no_auth_result=$(remote_execute "$node_host" "$REDIS_HOME/bin/redis-cli -h $node_host -p $node_port ping" 2>/dev/null || echo "FAILED")

        if [[ "$no_auth_result" == "PONG" ]]; then
            log_warn "节点 $node 未启用密码认证，这可能是配置问题"
            # 检查配置文件
            local config_file="$REDIS_CONFIG_DIR/redis-$node_port.conf"
            log_info "检查配置文件: $config_file"
            remote_execute "$node_host" "grep -E '^(requirepass|masterauth)' '$config_file' || echo '未找到密码配置'"

            # 重新加载配置
            log_info "重新启动Redis服务以加载配置..."
            remote_execute "$node_host" "systemctl restart redis-$node_port"
            sleep 10
        fi

        # 测试密码认证
        local auth_result
        auth_result=$(remote_execute "$node_host" "REDISCLI_AUTH='$cluster_password' $REDIS_HOME/bin/redis-cli -h $node_host -p $node_port ping" 2>/dev/null || echo "FAILED")

        if [[ "$auth_result" == "PONG" ]]; then
            log_info "✓ 节点 $node 密码认证成功"
        else
            log_warn "节点 $node 密码认证失败，尝试其他认证方式..."

            # 尝试使用 -a 参数
            local alt_auth_result
            alt_auth_result=$(remote_execute "$node_host" "$REDIS_HOME/bin/redis-cli -h $node_host -p $node_port -a '$cluster_password' ping" 2>/dev/null || echo "FAILED")

            if [[ "$alt_auth_result" == "PONG" ]]; then
                log_info "✓ 节点 $node 使用 -a 参数认证成功"
            else
                log_error "✗ 节点 $node 所有认证方式都失败"
                log_error "密码长度: ${#cluster_password}"
                log_error "尝试的密码: [已隐藏]"

                # 显示详细的调试信息
                log_error "调试信息："
                remote_execute "$node_host" "
                    echo '=== Redis进程状态 ==='
                    ps aux | grep redis-server | grep -v grep
                    echo '=== 端口监听状态 ==='
                    netstat -tlnp | grep :$node_port
                    echo '=== Redis日志 ==='
                    tail -20 $REDIS_DATA_DIR/log/redis-$node_port.log 2>/dev/null || echo '日志文件不存在'
                    echo '=== 配置文件密码设置 ==='
                    grep -E '^(requirepass|masterauth)' '$REDIS_CONFIG_DIR/redis-$node_port.conf' || echo '未找到密码配置'
                "

                auth_failed_nodes+=("$node")
            fi
        fi
    done

    if [[ ${#auth_failed_nodes[@]} -gt 0 ]]; then
        log_error "以下节点密码认证失败，无法创建集群: ${auth_failed_nodes[*]}"
        log_error "请检查以下项目："
        log_error "1. Redis配置文件中的requirepass设置"
        log_error "2. 所有节点是否使用相同的密码"
        log_error "3. Redis服务是否正确重启以加载新配置"
        log_error "4. 网络连接是否正常"
        log_error "5. Redis服务是否正常启动"

        # 提供修复建议
        log_error ""
        log_error "修复建议："
        log_error "1. 手动重启失败的Redis服务："
        for failed_node in "${auth_failed_nodes[@]}"; do
            local failed_host="${failed_node%:*}"
            local failed_port="${failed_node#*:}"
            log_error "   ssh $failed_host 'systemctl restart redis-$failed_port'"
        done
        log_error ""
        log_error "2. 检查Redis配置和日志："
        for failed_node in "${auth_failed_nodes[@]}"; do
            local failed_host="${failed_node%:*}"
            local failed_port="${failed_node#*:}"
            log_error "   ssh $failed_host 'grep requirepass $REDIS_CONFIG_DIR/redis-$failed_port.conf'"
            log_error "   ssh $failed_host 'tail -50 $REDIS_DATA_DIR/log/redis-$failed_port.log'"
        done
        log_error ""
        log_error "3. 手动测试密码认证："
        log_error "   export REDISCLI_AUTH='<密码>'"
        for failed_node in "${auth_failed_nodes[@]}"; do
            local failed_host="${failed_node%:*}"
            local failed_port="${failed_node#*:}"
            log_error "   ssh $failed_host '$REDIS_HOME/bin/redis-cli -h $failed_host -p $failed_port ping'"
        done

        return 1
    fi

    log_info "所有节点密码认证验证通过"

    # 创建集群（3主3从）
    # 注意：--cluster create 命令不支持 -a 选项，需要使用环境变量或其他方式处理认证
    local cluster_command="echo 'yes' | REDISCLI_AUTH='$cluster_password' $REDIS_HOME/bin/redis-cli --cluster create ${cluster_nodes[*]} --cluster-replicas 1"
    log_info "执行集群创建命令: $cluster_command"

    # 验证集群创建命令参数
    local node_count=${#cluster_nodes[@]}
    local expected_replicas=1
    local min_nodes=$((3 * (expected_replicas + 1)))  # 3主 * (1主 + 1从) = 6

    if [[ $node_count -lt $min_nodes ]]; then
        log_error "节点数量不足以创建${expected_replicas}个副本的集群"
        log_error "需要至少${min_nodes}个节点，当前只有${node_count}个节点"
        return 1
    fi

    # 创建集群
    if ! remote_execute "$first_host" "$cluster_command"; then
        log_error "Redis集群创建命令执行失败"
        log_error "请检查以下项目："
        log_error "1. 所有Redis节点是否正常启动"
        log_error "2. 节点之间网络连接是否正常"
        log_error "3. Redis密码配置是否正确"
        log_error "4. 防火墙是否阻止了集群通信端口"
        return 1
    fi

    # 等待集群稳定
    log_info "等待集群稳定..."
    sleep 30

    # 验证集群状态
    if remote_execute "$first_host" "REDISCLI_AUTH='$cluster_password' $REDIS_HOME/bin/redis-cli -p $first_port cluster info"; then
        log_info "Redis集群初始化成功"
    else
        log_error "Redis集群初始化失败"
        return 1
    fi

    # 显示集群节点信息
    log_info "Redis集群节点信息:"
    remote_execute "$first_host" "REDISCLI_AUTH='$cluster_password' $REDIS_HOME/bin/redis-cli -p $first_port cluster nodes"
}

# 执行主函数
main "$@"
