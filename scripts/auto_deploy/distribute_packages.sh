#!/bin/bash
# 安装包分发脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化软件包分发和校验

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="安装包分发脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 全局变量
# =============================================================================

# 分发状态跟踪
declare -A DISTRIBUTION_STATUS=()

# 软件包信息
declare -A PACKAGE_INFO=(
    # MongoDB
    ["mongodb"]="mongodb-linux-x86_64-rhel70-4.4.18.tgz"
#    ["mongodb"]="mongodb-without-avx.tar.gz"
    # Redis
    ["redis"]="redis-7.0.8.tar.gz"
    
    # Kafka
    ["kafka"]="kafka_2.13-2.8.1.tgz"
    ["kafka_jdk"]="bisheng-jdk-11.0.16-linux-x64.tar.gz"
    
    # TDEngine
    ["tdengine"]="TDengine-server-*******-Linux-x64.tar.gz"
    
    # NebulaGraph
    ["nebula"]="nebula-graph-3.4.0.el7.x86_64.tar.gz"
    ["nebula-console"]="nebula-console-linux-amd64-v3.4.0"
    ["yum-repo"]="yum-repo"  # 添加为特殊条目
    ["pip-repo"]="pip-repo"
    ["docker-images"]="docker-images"  # Docker镜像目录
    # 达梦数据库
#    ["dameng"]="dm8_20220822_x86_rh6_64.tar"
    
    # 监控组件
#    ["prometheus"]="prometheus-2.45.0.linux-amd64.tar.gz"
#    ["grafana"]="grafana-enterprise-10.0.0.linux-amd64.tar.gz"
#    ["alertmanager"]="alertmanager-0.25.0.linux-amd64.tar.gz"
#    ["node_exporter"]="node_exporter-1.6.0.linux-amd64.tar.gz"
#    ["mongodb_exporter"]="mongodb_exporter-0.39.0.linux-amd64.tar.gz"
#    ["redis_exporter"]="redis_exporter-v1.52.0.linux-amd64.tar.gz"
)

# 目标目录映射
declare -A TARGET_DIRS=(
    ["mongodb"]="/apps/software/mongodb"
    ["redis"]="/apps/software/redis"
    ["kafka"]="/apps/software/kafka"
    ["kafka_jdk"]="/apps/software/jdk"
    ["tdengine"]="/apps/software/tdengine"
    ["nebula"]="/apps/software/nebula"
    ["nebula-console"]="/apps/software/nebula-console"
    ["yum-repo"]="/apps/offline-prep/yum-repo"  # 目标路径
    ["pip-repo"]="/apps/offline-prep/pip-repo"  # 目标路径
    ["docker-images"]="/apps/software/docker-images"  # Docker镜像目标路径
#    ["dameng"]="/apps/software/dameng"
#    ["prometheus"]="/apps/software/prometheus"
#    ["grafana"]="/apps/software/grafana"
#    ["alertmanager"]="/apps/software/alertmanager"
#    ["node_exporter"]="/apps/software/exporters"
#    ["mongodb_exporter"]="/apps/software/exporters"
#    ["redis_exporter"]="/apps/software/exporters"
)

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_CHECKSUM=true
FORCE_OVERWRITE=false
DRY_RUN=false
PARALLEL_DISTRIBUTION=true
SPECIFIC_HOSTS=""
SPECIFIC_PACKAGES=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-checksum)
            SKIP_CHECKSUM=true
            shift
            ;;
        --force-overwrite)
            FORCE_OVERWRITE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --sequential)
            PARALLEL_DISTRIBUTION=false
            shift
            ;;
        --hosts)
            SPECIFIC_HOSTS="$2"
            shift 2
            ;;
        --packages)
            SPECIFIC_PACKAGES="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-checksum         跳过校验和检查"
            echo "  --force-overwrite       强制覆盖已存在的文件"
            echo "  --dry-run               仅显示将要执行的操作"
            echo "  --sequential            顺序分发（默认并行）"
            echo "  --hosts HOST_LIST       指定目标主机（逗号分隔）"
            echo "  --packages PKG_LIST     指定要分发的包（逗号分隔）"
            echo "  -h, --help              显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# 校验和管理函数
# =============================================================================

generate_checksums() {
    log_info "生成软件包校验和..."

    local checksum_file="$SOFTWARE_REPO/checksums.sha256"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将生成校验和文件: $checksum_file"
        return 0
    fi

    # 清空现有校验和文件
    > "$checksum_file"

    # 统计需要生成校验和的软件包数量
    local packages_to_process=()
    for package in "${!PACKAGE_INFO[@]}"; do
        # 跳过目录类型的包
        if [[ "$package" == "yum-repo" || "$package" == "pip-repo" || "$package" == "docker-images" ]]; then
            continue
        fi
        packages_to_process+=("$package")
    done

    local total_packages=${#packages_to_process[@]}
    local processed_packages=0

    log_info "需要生成校验和的软件包数量: $total_packages"

    # 为每个软件包生成校验和
    for package in "${packages_to_process[@]}"; do
        local package_file="${PACKAGE_INFO[$package]}"
        local package_path="$SOFTWARE_REPO/$package_file"

        if [[ -f "$package_path" ]]; then
            log_debug "生成校验和: $package_file"
            sha256sum "$package_path" >> "$checksum_file"
            processed_packages=$((processed_packages + 1))
        else
            log_warn "软件包文件不存在: $package_path"
            processed_packages=$((processed_packages + 1))
        fi

        show_progress $processed_packages $total_packages "生成校验和"
    done

    log_info "校验和文件生成完成: $checksum_file"
}

verify_checksums() {
    local host=$1

    if [[ "$SKIP_CHECKSUM" == "true" ]]; then
        log_info "跳过校验和验证"
        return 0
    fi

    log_info "验证 $host 上的软件包校验和..."

    # 首先检查校验和文件是否存在
    if ! remote_execute "$host" "test -f /apps/software/checksums.sha256" 2>/dev/null; then
        log_error "校验和文件不存在: $host:/apps/software/checksums.sha256"
        return 1
    fi

    # 获取校验和文件中的条目数量
    local checksum_count
    checksum_count=$(remote_execute "$host" "wc -l < /apps/software/checksums.sha256" 2>/dev/null | tr -d ' ')

    if [[ -z "$checksum_count" || "$checksum_count" -eq 0 ]]; then
        log_warn "校验和文件为空或无法读取: $host"
        return 0  # 空文件不算错误
    fi

    log_info "开始验证 $checksum_count 个文件的校验和..."

    # 执行校验和验证
    if remote_execute "$host" "cd /apps/software && sha256sum -c checksums.sha256 --quiet"; then
        log_info "✓ 校验和验证通过: $host ($checksum_count 个文件)"
        return 0
    else
        log_error "✗ 校验和验证失败: $host"
        # 显示详细的验证结果
        log_info "获取详细验证结果..."
        remote_execute "$host" "cd /apps/software && sha256sum -c checksums.sha256" 2>&1 | while read -r line; do
            if [[ "$line" =~ "FAILED" ]]; then
                log_error "  $line"
            elif [[ "$line" =~ "OK" ]]; then
                log_debug "  $line"
            fi
        done
        return 1
    fi
}

# =============================================================================
# 软件包分发函数
# =============================================================================

create_target_directories() {
    local host=$1

    log_debug "在 $host 上创建目标目录..."

    # 获取所有唯一的目标目录
    local unique_dirs=($(printf '%s\n' "${TARGET_DIRS[@]}" | sort -u))
    local total_dirs=${#unique_dirs[@]}
    local created_dirs=0

    for dir in "${unique_dirs[@]}"; do
        created_dirs=$((created_dirs + 1))
        if [[ "$DRY_RUN" == "true" ]]; then
            log_debug "[DRY RUN] 将在 $host 上创建目录: $dir"
        else
            log_debug "创建目录: $host:$dir"
            remote_execute "$host" "mkdir -p $dir"
        fi

        if [[ $total_dirs -gt 3 ]]; then  # 只有目录较多时才显示进度
            show_progress $created_dirs $total_dirs "创建目录"
        fi
    done

    log_debug "目标目录创建完成: $host ($created_dirs 个目录)"
}

distribute_package() {
    local host=$1
    local package=$2
    local package_file="${PACKAGE_INFO[$package]}"
    local target_dir="${TARGET_DIRS[$package]}"
    
    log_info "分发软件包到 $host: $package_file -> $target_dir"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将分发 $package_file 到 $host:$target_dir"
        return 0
    fi
    
    local source_path="$SOFTWARE_REPO/$package_file"
    local target_path="$target_dir/$package_file"
    
    # 检查源文件是否存在
    if [[ ! -f "$source_path" ]]; then
        log_error "源文件不存在: $source_path"
        return 1
    fi
    
    # 检查目标文件是否已存在
    if remote_execute "$host" "test -f $target_path" 2>/dev/null; then
        if [[ "$FORCE_OVERWRITE" != "true" ]]; then
            log_info "文件已存在，跳过: $host:$target_path"
            return 0
        else
            log_info "强制覆盖已存在的文件: $host:$target_path"
        fi
    fi
    
    # 执行文件传输
    if remote_copy "$source_path" "$target_path" "$host"; then
        log_info "文件传输成功: $package_file -> $host"
        return 0
    else
        log_error "文件传输失败: $package_file -> $host"
        return 1
    fi
}


# 添加分发目录函数
distribute_directory() {
    local host=$1
    local dir_name=$2
    local source_dir="/apps/offline-prep/$dir_name"
    local target_dir="${TARGET_DIRS[$dir_name]}"

    log_info "分发目录到 $host: $dir_name -> $target_dir"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将分发 $dir_name 目录到 $host:$target_dir"
        return 0
    fi

    # 检查源目录是否存在
    if [[ ! -d "$source_dir" ]]; then
        log_error "源目录不存在: $source_dir"
        return 1
    fi

    # 检查目标目录是否已存在
    if remote_execute "$host" "! \$(ls -A $target_dir)" 2>/dev/null; then
        if [[ "$FORCE_OVERWRITE" != "true" ]]; then
            log_info "目录已存在，跳过: $host:$target_dir"
            return 0
        else
            log_info "强制覆盖已存在的目录: $host:$target_dir"
            # 安全检查：如果是本机IP，禁止清空目录
            if is_local_ip "$host"; then
                log_error "安全检查失败: 目标IP ($host) 是本机IP，禁止清空目录以防止意外删除本地文件"
                log_error "如需清空本机目录，请手动执行: rm -rf $target_dir/*"
                return 1
            fi
            # 清空目标目录
            log_warn "即将清空远程目录: $host:$target_dir"
            remote_execute "$host" "rm -rf $target_dir/*"
        fi
    fi

    # 执行递归目录传输
    log_info "开始递归传输目录内容..."

    # 检查目录大小，决定使用哪种传输方式
    local file_count=$(find "$source_dir" -type f 2>/dev/null | wc -l)

    if [[ $file_count -gt 100 ]]; then
        # 文件数量较多，使用批量传输方式
        log_info "目录包含 $file_count 个文件，使用批量传输方式"
        if remote_copy_dir "$source_dir" "$target_dir" "$host"; then
            log_info "目录传输成功: $dir_name -> $host"
            return 0
        else
            log_error "目录传输失败: $dir_name -> $host"
            return 1
        fi
    else
        # 文件数量较少，使用逐个传输方式以显示详细进度
        log_info "目录包含 $file_count 个文件，使用逐个传输方式"

        local files_to_copy=()
        while IFS= read -r -d $'\0' file; do
            files_to_copy+=("$file")
        done < <(find "$source_dir" -type f -print0)

        local total_files=${#files_to_copy[@]}
        local copied_files=0

        log_info "需要传输 $total_files 个文件"

        # 传输每个文件
        for file in "${files_to_copy[@]}"; do
            # 计算相对路径
            local relative_path="${file#$source_dir/}"
            local remote_path="$target_dir/$relative_path"

            # 创建远程目录结构
            remote_execute "$host" "mkdir -p $(dirname "$remote_path")"

            # 传输单个文件
            if remote_copy "$file" "$remote_path" "$host"; then
                copied_files=$((copied_files + 1))
                show_progress $copied_files $total_files "传输 $dir_name"
            else
                log_error "文件传输失败: $file -> $host:$remote_path"
                copied_files=$((copied_files + 1))  # 即使失败也要更新进度
                show_progress $copied_files $total_files "传输 $dir_name"
            fi
        done

        if [[ "$copied_files" -eq "$total_files" ]]; then
            log_info "目录传输完成: $dir_name -> $host ($copied_files/$total_files 文件)"
            return 0
        else
            log_warn "目录传输部分完成: $dir_name -> $host ($copied_files/$total_files 文件)"
            return 0  # 部分成功也返回0，让上层决定是否继续
        fi
    fi
}

# 检查是否为本机IP的函数
is_local_ip() {
    local target_ip=$1

    # 获取本机所有IP地址
    local local_ips=()

    # 方法1: 使用hostname -I (如果可用)
    if command -v hostname >/dev/null 2>&1; then
        local hostname_ips=$(hostname -I 2>/dev/null | tr ' ' '\n' | grep -v '^$')
        if [[ -n "$hostname_ips" ]]; then
            while IFS= read -r ip; do
                [[ -n "$ip" ]] && local_ips+=("$ip")
            done <<< "$hostname_ips"
        fi
    fi

    # 方法2: 使用ip命令 (如果可用)
    if command -v ip >/dev/null 2>&1; then
        local ip_addrs=$(ip addr show 2>/dev/null | grep -oP 'inet \K[0-9.]+' | grep -v '127.0.0.1')
        if [[ -n "$ip_addrs" ]]; then
            while IFS= read -r ip; do
                [[ -n "$ip" ]] && local_ips+=("$ip")
            done <<< "$ip_addrs"
        fi
    fi

    # 方法3: 使用ifconfig命令 (如果可用)
    if command -v ifconfig >/dev/null 2>&1; then
        local ifconfig_ips=$(ifconfig 2>/dev/null | grep -oP 'inet \K[0-9.]+' | grep -v '127.0.0.1')
        if [[ -n "$ifconfig_ips" ]]; then
            while IFS= read -r ip; do
                [[ -n "$ip" ]] && local_ips+=("$ip")
            done <<< "$ifconfig_ips"
        fi
    fi

    # 添加常见的本机地址
    local_ips+=("127.0.0.1" "localhost" "::1")

    # 去重
    local_ips=($(printf '%s\n' "${local_ips[@]}" | sort -u))

    # 检查目标IP是否在本机IP列表中
    for local_ip in "${local_ips[@]}"; do
        if [[ "$target_ip" == "$local_ip" ]]; then
            log_debug "检测到本机IP: $target_ip"
            return 0
        fi
    done

    # 特殊处理：如果目标IP能解析为本机hostname
    local hostname=$(hostname 2>/dev/null || echo "")
    if [[ -n "$hostname" && "$target_ip" == "$hostname" ]]; then
        log_debug "检测到本机hostname: $target_ip"
        return 0
    fi

    log_debug "非本机IP: $target_ip"
    return 1
}

# 分发Docker镜像函数
distribute_docker_images() {
    local host=$1
    local source_dir="/apps/software/docker-images"
    local target_dir="${TARGET_DIRS["docker-images"]}"

    log_info "分发Docker镜像到 $host: $source_dir -> $target_dir"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将分发Docker镜像目录到 $host:$target_dir"
        return 0
    fi

    # 检查源目录是否存在
    if [[ ! -d "$source_dir" ]]; then
        log_warn "Docker镜像源目录不存在: $source_dir，跳过Docker镜像分发"
        return 0
    fi

    # 检查是否有Docker镜像文件
    local image_files=$(find "$source_dir" -name "*.tar.gz" 2>/dev/null | wc -l)
    if [[ $image_files -eq 0 ]]; then
        log_warn "Docker镜像目录中没有找到.tar.gz文件: $source_dir，跳过Docker镜像分发"
        return 0
    fi

    log_info "发现 $image_files 个Docker镜像文件"

    # 检查目标目录状态
    if remote_execute "$host" "test -d $target_dir" 2>/dev/null; then
        # 目录存在，检查是否有内容
        if remote_execute "$host" "[ \"\$(ls -A $target_dir 2>/dev/null)\" ]" 2>/dev/null; then
            # 目录存在且有内容
            if [[ "$FORCE_OVERWRITE" != "true" ]]; then
                log_info "Docker镜像目录已存在且有内容，跳过分发: $host:$target_dir"
                log_info "如需强制覆盖，请使用 --force-overwrite 参数"
                return 0
            else
                log_info "强制覆盖已存在的Docker镜像目录: $host:$target_dir"
                # 安全检查：如果是本机IP，禁止清空目录
                if is_local_ip "$host"; then
                    log_error "安全检查失败: 目标IP ($host) 是本机IP，禁止清空目录以防止意外删除本地文件"
                    log_error "如需清空本机目录，请手动执行: rm -rf $target_dir/*"
                    return 1
                fi
                # 清空目标目录
                log_warn "即将清空远程目录: $host:$target_dir"
                remote_execute "$host" "rm -rf $target_dir/*"
            fi
        else
            # 目录存在但为空
            log_info "Docker镜像目录存在但为空，继续分发: $host:$target_dir"
        fi
    else
        # 目录不存在
        log_info "Docker镜像目录不存在，将创建: $host:$target_dir"
    fi

    # 创建目标目录
    remote_execute "$host" "mkdir -p $target_dir"

    # 分发Docker镜像文件
    log_info "开始分发Docker镜像文件..."

    local docker_files=()
    while IFS= read -r -d $'\0' file; do
        docker_files+=("$file")
    done < <(find "$source_dir" -name "*.tar" -print0)

    local total_files=${#docker_files[@]}
    local copied_files=0

    log_info "需要传输 $total_files 个Docker镜像文件"

    # 传输每个Docker镜像文件
    for file in "${docker_files[@]}"; do
        local filename=$(basename "$file")
        local remote_path="$target_dir/$filename"

        log_info "传输Docker镜像: $filename"
        if remote_copy "$file" "$remote_path" "$host"; then
            copied_files=$((copied_files + 1))
            log_info "✓ Docker镜像传输成功: $filename"
        else
            log_error "✗ Docker镜像传输失败: $filename"
        fi
        show_progress $copied_files $total_files "传输Docker镜像"
    done

    if [[ "$copied_files" -eq "$total_files" ]]; then
        log_info "Docker镜像分发完成: $copied_files/$total_files 文件"

        # 创建Docker镜像加载脚本
        create_docker_load_script "$host" "$target_dir"

        return 0
    else
        log_warn "Docker镜像分发部分完成: $copied_files/$total_files 文件"
        return 0  # 部分成功也返回0，让上层决定是否继续
    fi
}

# 创建Docker镜像加载脚本
create_docker_load_script() {
    local host=$1
    local docker_dir=$2

    log_info "在 $host 上创建Docker镜像加载脚本..."

    remote_execute "$host" "cat > $docker_dir/load_docker_images.sh << 'EOF'
#!/bin/bash
# Docker镜像加载脚本
# 自动加载目录中的所有Docker镜像

echo \"=== Docker镜像加载脚本 ===\"
DOCKER_DIR=\"$docker_dir\"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo \"错误: Docker未安装，请先安装Docker\"
    exit 1
fi

# 检查Docker服务是否运行
if ! systemctl is-active docker >/dev/null 2>&1; then
    echo \"启动Docker服务...\"
    systemctl start docker
    if ! systemctl is-active docker >/dev/null 2>&1; then
        echo \"错误: 无法启动Docker服务\"
        exit 1
    fi
fi

echo \"Docker服务状态: 运行中\"

# 查找所有.tar文件
TAR_FILES=(\\\$(find \"\\\$DOCKER_DIR\" -name \"*.tar\" 2>/dev/null))

if [[ \\\${#TAR_FILES[@]} -eq 0 ]]; then
    echo \"未找到Docker镜像文件(.tar)\"
    exit 0
fi

echo \"发现 \\\${#TAR_FILES[@]} 个Docker镜像文件\"

# 加载每个镜像
SUCCESS_COUNT=0
TOTAL_COUNT=\\\${#TAR_FILES[@]}

for tar_file in \"\\\${TAR_FILES[@]}\"; do
    echo \"\"
    echo \"加载镜像: \\\$(basename \"\\\$tar_file\")\"

    if docker load < \"\\\$tar_file\"; then
        echo \"✓ 镜像加载成功: \\\$(basename \"\\\$tar_file\")\"
        SUCCESS_COUNT=\\\$((SUCCESS_COUNT + 1))
    else
        echo \"✗ 镜像加载失败: \\\$(basename \"\\\$tar_file\")\"
    fi
done

echo \"\"
echo \"=== 加载结果 ===\"
echo \"成功: \\\$SUCCESS_COUNT/\\\$TOTAL_COUNT\"

# 显示已加载的镜像
echo \"\"
echo \"=== 当前Docker镜像列表 ===\"
docker images

echo \"\"
echo \"=== Docker镜像加载完成 ===\"
EOF"

    # 设置执行权限
    remote_execute "$host" "chmod +x $docker_dir/load_docker_images.sh"

    log_info "✓ Docker镜像加载脚本创建完成: $docker_dir/load_docker_images.sh"
}

distribute_to_host() {
    local host=$1
    local packages_to_distribute=()
    
    # 确定要分发的软件包
    if [[ -n "$SPECIFIC_PACKAGES" ]]; then
        IFS=',' read -ra packages_to_distribute <<< "$SPECIFIC_PACKAGES"
    else
        # 根据主机服务类型确定需要的软件包
        local services
        mapfile -t services < <(get_service_by_ip "$host")
        
        for service in "${services[@]}"; do
            case $service in
                "mongodb")
#                    packages_to_distribute+=("mongodb" "node_exporter" "mongodb_exporter")
                    packages_to_distribute+=("mongodb")
                    ;;
                "redis")
#                    packages_to_distribute+=("redis" "node_exporter" "redis_exporter")
                    packages_to_distribute+=("redis")
                    ;;
                "kafka")
#                    packages_to_distribute+=("kafka" "kafka_jdk" "node_exporter")
                    packages_to_distribute+=("kafka" "kafka_jdk")
                    ;;
                "tdengine")
#                    packages_to_distribute+=("tdengine" "node_exporter")
                    packages_to_distribute+=("tdengine")
                    ;;
                "nebula")
#                    packages_to_distribute+=("nebula" "node_exporter")
                     packages_to_distribute+=("nebula" "nebula-console")
                    ;;
#                "dameng")
#                    packages_to_distribute+=("dameng" "node_exporter")
#                    ;;
#                "monitor")
#                    packages_to_distribute+=("prometheus" "grafana" "alertmanager" "node_exporter")
#                    ;;
            esac
        done
        packages_to_distribute+=("yum-repo")
        packages_to_distribute+=("pip-repo")
        packages_to_distribute+=("docker-images")
        # 去重
        packages_to_distribute=($(printf '%s\n' "${packages_to_distribute[@]}" | sort -u))
    fi
    
    log_info "开始分发软件包到 $host..."
    log_info "要分发的软件包: ${packages_to_distribute[*]}"
    # 创建目标目录
    create_target_directories "$host"
    # 分发软件包
    local success_count=0
    local total_count=${#packages_to_distribute[@]}
    log_info "需要分发 $total_count 个软件包到 $host"
    for package in "${packages_to_distribute[@]}"; do
        if [[ -n "${PACKAGE_INFO[$package]:-}" ]]; then
          # 特殊处理目录类型的包
          if [[ "$package" == "yum-repo" || "$package" == "pip-repo" ]]; then
              if distribute_directory "$host" "$package"; then
                  success_count=$((success_count + 1))
              else
                  log_error "分发目录失败: $package"
              fi
          # 特殊处理Docker镜像
          elif [[ "$package" == "docker-images" ]]; then
              if distribute_docker_images "$host"; then
                  success_count=$((success_count + 1))
              else
                  log_error "分发Docker镜像失败: $package"
              fi
          # 处理普通软件包
          elif distribute_package "$host" "$package"; then
            success_count=$((success_count + 1))
          else
            log_error "分发软件包失败: $package"
          fi
        else
            log_warn "未知的软件包: $package"
        fi
        show_progress $success_count $total_count "分发到 $host"
    done

    # 分发校验和文件
    if [[ "$SKIP_CHECKSUM" != "true" ]]; then
        log_debug "分发校验和文件到 $host"
        if [[ "$DRY_RUN" != "true" ]]; then
            remote_copy "$SOFTWARE_REPO/checksums.sha256" "/apps/software/checksums.sha256" "$host"
        fi
    fi

    log_info "软件包分发完成: $success_count/$total_count"
    # 验证分发结果
    if [[ "$success_count" -eq "$total_count" ]]; then
        log_info "软件包分发到 $host 完成: $success_count/$total_count"
        DISTRIBUTION_STATUS["$host"]="success"
        
        # 验证校验和
        if ! verify_checksums "$host"; then
            DISTRIBUTION_STATUS["$host"]="checksum_failed"
            return 1
        fi
        
        return 0
    else
        log_error "软件包分发到 $host 失败: $success_count/$total_count"
        DISTRIBUTION_STATUS["$host"]="failed"
        return 1
    fi
}

# =============================================================================
# 并行分发函数
# =============================================================================

parallel_distribute() {
    local hosts=("$@")
    local pids=()
    
    log_info "开始并行分发到 ${#hosts[@]} 个主机..."
    
    # 启动并行任务
    for host in "${hosts[@]}"; do
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[DRY RUN] 将并行分发到 $host"
            DISTRIBUTION_STATUS["$host"]="success"
        else
            (
                distribute_to_host "$host"
            ) &
            pids+=($!)
        fi
    done
    
    if [[ "$DRY_RUN" == "true" ]]; then
        return 0
    fi
    
    # 等待所有任务完成
    local completed=0
    local total=${#pids[@]}
    
    for pid in "${pids[@]}"; do
        wait $pid
        completed=$((completed + 1))
        show_progress $completed $total "并行分发"
    done
    
    log_info "并行分发完成"
}

sequential_distribute() {
    local hosts=("$@")
    
    log_info "开始顺序分发到 ${#hosts[@]} 个主机..."
    
    local completed=0
    local total=${#hosts[@]}
    
    for host in "${hosts[@]}"; do
        distribute_to_host "$host"
        completed=$((completed + 1))  # 自增1
        show_progress $completed $total "顺序分发"
    done
    
    log_info "顺序分发完成"
}

# =============================================================================
# 分发前检查函数
# =============================================================================

pre_distribution_checks() {
    log_info "执行分发前检查..."

    # 检查软件仓库目录
    if [[ ! -d "$SOFTWARE_REPO" ]]; then
        log_error "软件仓库目录不存在: $SOFTWARE_REPO"
        return 1
    fi

    # 检查必需的软件包
    local missing_packages=()
    local total_packages=${#PACKAGE_INFO[@]}
    local checked_packages=0

    log_info "检查软件包文件完整性..."

    for package in "${!PACKAGE_INFO[@]}"; do
        local package_file="${PACKAGE_INFO[$package]}"
        checked_packages=$((checked_packages + 1))

        # 特殊处理目录类型
        if [[ "$package" == "yum-repo" || "$package" == "pip-repo" ]]; then
            local source_dir="/apps/offline-prep/$package_file"
            if [[ ! -d "$source_dir" ]]; then
                missing_packages+=("$package_file (目录)")
                log_debug "目录不存在: $source_dir"
            else
                log_debug "目录检查通过: $source_dir"
            fi
        elif [[ "$package" == "docker-images" ]]; then
            local source_dir="/apps/software/$package_file"
            if [[ ! -d "$source_dir" ]]; then
                log_debug "Docker镜像目录不存在: $source_dir (可选)"
            else
                local image_count=$(find "$source_dir" -name "*.tar" 2>/dev/null | wc -l)
                log_debug "Docker镜像目录检查通过: $source_dir (包含 $image_count 个镜像文件)"
            fi
        else
            local package_path="$SOFTWARE_REPO/$package_file"
            if [[ ! -f "$package_path" ]]; then
                missing_packages+=("$package_file")
                log_debug "文件不存在: $package_path"
            else
                log_debug "文件检查通过: $package_path"
            fi
        fi

        show_progress $checked_packages $total_packages "检查软件包"
    done

    if [[ ${#missing_packages[@]} -gt 0 ]]; then
        log_error "以下软件包文件缺失:"
        for package in "${missing_packages[@]}"; do
            log_error "  - $package"
        done
        return 1
    fi

    # 检查磁盘空间
    log_info "检查磁盘空间..."
    local total_size=$(du -sb "$SOFTWARE_REPO" | cut -f1)
    local total_size_gb=$((total_size / 1024 / 1024 / 1024))

    log_info "软件仓库总大小: ${total_size_gb}GB"

    if [[ $total_size_gb -gt 50 ]]; then
        log_warn "软件仓库较大，分发可能需要较长时间"
    fi

    log_info "分发前检查通过"
    return 0
}

# =============================================================================
# 结果报告函数
# =============================================================================

print_distribution_summary() {
    echo
    echo "=== 分发结果摘要 ==="
    
    local success_count=0
    local failed_count=0
    local checksum_failed_count=0
    
    for host in "${!DISTRIBUTION_STATUS[@]}"; do
        local status="${DISTRIBUTION_STATUS[$host]}"
        local status_icon
        
        case $status in
            "success")
                status_icon="✓"
                success_count=$((success_count + 1))
                ;;
            "failed")
                status_icon="✗"
                failed_count=$((failed_count + 1))
                ;;
            "checksum_failed")
                status_icon="⚠"
                 checksum_failed_count=$((checksum_failed_count + 1))
                ;;
            *)
                status_icon="?"
                ;;
        esac
        
        printf "%-20s: %s %s\n" "$host" "$status_icon" "$status"
    done
    
    echo
    echo "成功: $success_count"
    echo "失败: $failed_count"
    echo "校验和失败: $checksum_failed_count"
    echo "总计: ${#DISTRIBUTION_STATUS[@]}"
    echo "===================="
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始软件包分发..."

    # 获取锁
    if ! acquire_lock "distribute_packages"; then
        log_error "无法获取锁，可能有其他分发实例正在运行"
        exit 1
    fi

    echo "=== 软件包分发流程 ==="
    echo "1/5 执行分发前检查..."
    # 分发前检查
    if ! pre_distribution_checks; then
        log_error "分发前检查失败"
        exit 1
    fi

    echo "2/5 生成校验和文件..."
    # 生成校验和
    generate_checksums
    
    echo "3/5 确定目标主机..."
    # 确定目标主机
    local target_hosts=()
    if [[ -n "$SPECIFIC_HOSTS" ]]; then
        IFS=',' read -ra target_hosts <<< "$SPECIFIC_HOSTS"
    else
        mapfile -t target_hosts < <(get_all_hosts)
    fi

    log_info "目标主机: ${target_hosts[*]} (共 ${#target_hosts[@]} 台)"

    echo "4/5 检查SSH连接..."
    # 检查SSH连接
    local unreachable_hosts=()
    local checked_hosts=0
    local total_hosts=${#target_hosts[@]}

    for host in "${target_hosts[@]}"; do
        checked_hosts=$((checked_hosts + 1))
        if ! check_ssh_connection "$host"; then
            unreachable_hosts+=("$host")
            log_debug "SSH连接失败: $host"
        else
            log_debug "SSH连接成功: $host"
        fi
        show_progress $checked_hosts $total_hosts "检查SSH连接"
    done
    
    if [[ ${#unreachable_hosts[@]} -gt 0 ]]; then
        log_error "以下主机无法连接: ${unreachable_hosts[*]}"
        exit 1
    fi

    echo "5/5 执行软件包分发..."
    # 执行分发
    if [[ "$PARALLEL_DISTRIBUTION" == "true" ]]; then
        log_info "使用并行分发模式"
        parallel_distribute "${target_hosts[@]}"
    else
        log_info "使用顺序分发模式"
        sequential_distribute "${target_hosts[@]}"
    fi
    
    # 输出结果
    print_distribution_summary
    
    # 检查是否有失败的分发
    local has_failures=false
    for status in "${DISTRIBUTION_STATUS[@]}"; do
        if [[ "$status" != "success" ]]; then
            has_failures=true
            break
        fi
    done
    
    if [[ "$has_failures" == "true" ]]; then
        log_error "软件包分发过程中发生错误"
        exit 1
    else
        log_info "所有软件包分发完成！"
        exit 0
    fi
}

# 设置脚本权限
chmod +x "$0"

# 执行主函数
main "$@"
