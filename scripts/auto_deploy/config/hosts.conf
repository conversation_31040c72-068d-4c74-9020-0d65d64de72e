#!/bin/bash
# 主机配置文件 - 工业场景基础服务集群主机规划
# 遵循DevOps最佳实践，使用数组和关联数组管理主机信息

# =============================================================================
# 主机网络配置
# =============================================================================

# 网络段配置
export NETWORK_SEGMENT="10.81.12"
export NETMASK="*************"
export GATEWAY="**********"

# =============================================================================
# MongoDB集群主机配置
# =============================================================================

# MongoDB主机列表（副本集：1主2从）
declare -a MONGODB_HOSTS=(
    "***********"  # mongodb-01 (Primary)
    "***********"  # mongodb-02 (Secondary)
    "***********"  # mongodb-03 (Secondary)
)

# MongoDB主机名映射
declare -A MONGODB_HOSTNAMES=(
    ["***********"]="mongodb-01"
    ["***********"]="mongodb-02"
    ["***********"]="mongodb-03"
)

# MongoDB角色配置
declare -A MONGODB_ROLES=(
    ["***********"]="primary"
    ["***********"]="secondary"
    ["***********"]="secondary"
)

# MongoDB优先级配置
declare -A MONGODB_PRIORITIES=(
    ["***********"]="2"
    ["***********"]="1"
    ["***********"]="1"
)

# =============================================================================
# Redis集群主机配置（与NebulaGraph共享节点资源）
# =============================================================================

# Redis主机列表（集群模式：3主3从，与NebulaGraph共享节点）
declare -a REDIS_HOSTS=(
    "***********"  # redis-master-01 (Master-1) - 与图数据库共用
    "***********"  # redis-master-02 (Master-2) - 与图数据库共用
    "***********"  # redis-master-03 (Master-3) - 与图数据库共用
    "***********"  # redis-slave-01 (Slave-1)  - 与图数据库共用
    "***********"  # redis-slave-02 (Slave-2)  - 与图数据库共用
    "***********"  # redis-slave-03 (Slave-3)  - 与图数据库共用
)

# Redis Master节点列表
declare -a REDIS_MASTER_HOSTS=(
    "***********"  # redis-master-01
    "***********"  # redis-master-02
    "***********"  # redis-master-03
)

# Redis Slave节点列表
declare -a REDIS_SLAVE_HOSTS=(
    "***********"  # redis-slave-01
    "***********"  # redis-slave-02
    "***********"  # redis-slave-03
)

# Redis主机名映射
declare -A REDIS_HOSTNAMES=(
    ["***********"]="redis-master-01"
    ["***********"]="redis-master-02"
    ["***********"]="redis-master-03"
    ["***********"]="redis-slave-01"
    ["***********"]="redis-slave-02"
    ["***********"]="redis-slave-03"
)

# Redis端口配置（每个主机运行一个Redis实例）
declare -A REDIS_HOST_PORTS=(
    ["***********"]="6379"  # Master-1
    ["***********"]="6379"  # Master-2
    ["***********"]="6379"  # Master-3
    ["***********"]="6379"  # Slave-1
    ["***********"]="6379"  # Slave-2
    ["***********"]="6379"  # Slave-3
)

# Redis集群端口列表（用于集群初始化）
declare -a REDIS_CLUSTER_PORTS=(
    "6379"  # 标准Redis端口
)

# Redis节点角色配置
declare -A REDIS_NODE_ROLES=(
    ["***********:6379"]="master"
    ["***********:6379"]="master"
    ["***********:6379"]="master"
    ["***********:6379"]="slave"
    ["***********:6379"]="slave"
    ["***********:6379"]="slave"
)

# Redis主从关系配置
declare -A REDIS_MASTER_SLAVE_MAP=(
    ["***********:6379"]="***********:6379"  # Slave-1 -> Master-1
    ["***********:6379"]="***********:6379"  # Slave-2 -> Master-2
    ["***********:6379"]="***********:6379"  # Slave-3 -> Master-3
)

# =============================================================================
# Kafka集群主机配置
# =============================================================================

# Kafka主机列表（3节点集群）
declare -a KAFKA_HOSTS=(
    "***********"  # kafka-01
    "***********"  # kafka-02
    "***********"  # kafka-03
)

# Kafka主机名映射
declare -A KAFKA_HOSTNAMES=(
    ["***********"]="kafka-01"
    ["***********"]="kafka-02"
    ["***********"]="kafka-03"
)

# Kafka Broker ID配置
declare -A KAFKA_BROKER_IDS=(
    ["***********"]="1"
    ["***********"]="2"
    ["***********"]="3"
)

# =============================================================================
# TDEngine集群主机配置
# =============================================================================

# TDEngine主机列表（3节点分布式集群）
declare -a TDENGINE_HOSTS=(
    "***********"  # tdengine-01
    "***********"  # tdengine-02
    "***********"  # tdengine-03
)

# TDEngine主机名映射
declare -A TDENGINE_HOSTNAMES=(
    ["***********"]="tdengine-01"
    ["***********"]="tdengine-02"
    ["***********"]="tdengine-03"
)

# TDEngine节点ID配置
declare -A TDENGINE_NODE_IDS=(
    ["***********"]="1"
    ["***********"]="2"
    ["***********"]="3"
)

# =============================================================================
# NebulaGraph集群主机配置（与Redis共享节点资源）
# =============================================================================

# NebulaGraph主机列表（6节点分布式集群，与Redis共享）
declare -a NEBULA_HOSTS=(
    "***********"  # nebula-01 (与Redis Master共享)
    "***********"  # nebula-02 (与Redis Master共享)
    "***********"  # nebula-03 (与Redis Master共享)
    "***********"  # nebula-04 (与Redis Slave共享)
    "***********"  # nebula-05 (与Redis Slave共享)
    "***********"  # nebula-06 (与Redis Slave共享)
)

# NebulaGraph主机名映射
declare -A NEBULA_HOSTNAMES=(
    ["***********"]="nebula-01"
    ["***********"]="nebula-02"
    ["***********"]="nebula-03"
    ["***********"]="nebula-04"
    ["***********"]="nebula-05"
    ["***********"]="nebula-06"
)

# NebulaGraph服务分布配置
declare -A NEBULA_SERVICES=(
    ["***********"]="meta,storage,graph"
    ["***********"]="meta,storage,graph"
    ["***********"]="meta,storage,graph"
    ["***********"]="storage,graph"
    ["***********"]="storage,graph"
    ["***********"]="storage,graph"
)

# =============================================================================
# 堡垒机配置
# =============================================================================

# 堡垒机主机（独立节点，避免与业务服务冲突）
export BASTION_HOST="***********"
export BASTION_HOSTNAME="bastion-01"
export BASTION_USER="admin"

# =============================================================================
# 硬件配置要求
# =============================================================================

# MongoDB硬件配置
declare -A MONGODB_HARDWARE=(
    ["cpu"]="16"
    ["memory"]="32GB"
    ["storage"]="500GB SSD"
    ["network"]="1Gbps"
)

# Redis硬件配置（与NebulaGraph共享节点）
declare -A REDIS_HARDWARE=(
    ["cpu"]="8"      # 共享节点，分配部分CPU
    ["memory"]="16GB" # 共享节点，分配部分内存
    ["storage"]="200GB SSD"
    ["network"]="1Gbps"
)

# Kafka硬件配置
declare -A KAFKA_HARDWARE=(
    ["cpu"]="8"
    ["memory"]="16GB"
    ["storage"]="500GB SSD"
    ["network"]="1Gbps"
)

# TDEngine硬件配置
declare -A TDENGINE_HARDWARE=(
    ["cpu"]="16"
    ["memory"]="32GB"
    ["storage"]="1TB SSD"
    ["network"]="1Gbps"
)

# NebulaGraph硬件配置（与Redis共享节点）
declare -A NEBULA_HARDWARE=(
    ["cpu"]="16"     # 共享节点，分配部分CPU
    ["memory"]="32GB" # 共享节点，分配部分内存
    ["storage"]="500GB SSD" # 共享节点，分配部分存储
    ["network"]="1Gbps"
)

# 共享节点硬件配置（NebulaGraph + Redis）
declare -A SHARED_NEBULA_REDIS_HARDWARE=(
    ["cpu"]="24"     # 总CPU配置
    ["memory"]="48GB" # 总内存配置
    ["storage"]="700GB SSD" # 总存储配置
    ["network"]="1Gbps"
    ["services"]="nebula,redis"
    ["redis_memory_limit"]="16GB"
    ["nebula_memory_limit"]="32GB"
)

# =============================================================================
# 工具函数
# =============================================================================

# 获取所有主机列表
get_all_hosts() {
    local all_hosts=()
    all_hosts+=("${MONGODB_HOSTS[@]}")
    all_hosts+=("${REDIS_HOSTS[@]}")
    all_hosts+=("${KAFKA_HOSTS[@]}")
    all_hosts+=("${TDENGINE_HOSTS[@]}")
    all_hosts+=("${NEBULA_HOSTS[@]}")

    # 去重并排序
    printf '%s\n' "${all_hosts[@]}" | sort -u
}

# 根据IP获取主机名
get_hostname_by_ip() {
    local ip=$1
    local hostname=""

    # 检查各个服务的主机名映射
    for service in MONGODB REDIS KAFKA TDENGINE NEBULA; do
        local -n hostnames="${service}_HOSTNAMES"
        if [[ -n "${hostnames[$ip]}" ]]; then
            hostname="${hostnames[$ip]}"
            break
        fi
    done

    echo "$hostname"
}

# 根据IP获取服务类型
get_service_by_ip() {
    local ip=$1
    local services=()
    
    # 检查IP属于哪个服务
    for host in "${MONGODB_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("mongodb")
    done
    
    for host in "${REDIS_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("redis")
    done
    
    for host in "${KAFKA_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("kafka")
    done
    
    for host in "${TDENGINE_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("tdengine")
    done
    
    for host in "${NEBULA_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("nebula")
    done

    printf '%s\n' "${services[@]}"
}

# 验证主机配置
validate_hosts_config() {
    local errors=0

    # 检查主机数量
    if [[ ${#MONGODB_HOSTS[@]} -ne 3 ]]; then
        echo "错误: MongoDB集群需要3个节点" >&2
        errors=$((errors + 1))
    fi

    if [[ ${#REDIS_HOSTS[@]} -ne 6 ]]; then
        echo "错误: Redis集群需要6个节点" >&2
        errors=$((errors + 1))
    fi

    if [[ ${#KAFKA_HOSTS[@]} -ne 3 ]]; then
        echo "错误: Kafka集群需要3个节点" >&2
        errors=$((errors + 1))
    fi

    if [[ ${#NEBULA_HOSTS[@]} -ne 6 ]]; then
        echo "错误: NebulaGraph集群需要6个节点（与Redis共享）" >&2
        errors=$((errors + 1))
    fi

    # 验证Redis与NebulaGraph节点共享配置
    local redis_hosts_str=$(printf '%s\n' "${REDIS_HOSTS[@]}" | sort)
    local nebula_hosts_str=$(printf '%s\n' "${NEBULA_HOSTS[@]}" | sort)

    if [[ "$redis_hosts_str" != "$nebula_hosts_str" ]]; then
        echo "错误: Redis主机列表与NebulaGraph主机列表不匹配" >&2
        echo "Redis主机: $redis_hosts_str" >&2
        echo "NebulaGraph主机: $nebula_hosts_str" >&2
        errors=$((errors + 1))
    fi

    # 验证Redis Master/Slave配置
    if [[ ${#REDIS_MASTER_HOSTS[@]} -ne 3 ]]; then
        echo "错误: Redis需要3个Master节点" >&2
        errors=$((errors + 1))
    fi

    if [[ ${#REDIS_SLAVE_HOSTS[@]} -ne 3 ]]; then
        echo "错误: Redis需要3个Slave节点" >&2
        errors=$((errors + 1))
    fi

    # 检查IP地址格式
    local all_hosts
    mapfile -t all_hosts < <(get_all_hosts)

    for host in "${all_hosts[@]}"; do
        if ! [[ $host =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            echo "错误: 无效的IP地址格式: $host" >&2
            errors=$((errors + 1))
        fi
    done

    return $errors
}

# 输出主机配置摘要
print_hosts_summary() {
    echo "=== 主机配置摘要 ==="
    echo "MongoDB集群: ${#MONGODB_HOSTS[@]} 节点"
    echo "Redis集群: ${#REDIS_HOSTS[@]} 节点 (3主3从，与NebulaGraph共享)"
    echo "  - Master节点: ${#REDIS_MASTER_HOSTS[@]} 个"
    echo "  - Slave节点: ${#REDIS_SLAVE_HOSTS[@]} 个"
    echo "Kafka集群: ${#KAFKA_HOSTS[@]} 节点"
    echo "TDEngine集群: ${#TDENGINE_HOSTS[@]} 节点"
    echo "NebulaGraph集群: ${#NEBULA_HOSTS[@]} 节点 (与Redis共享)"
    echo "总计: $(get_all_hosts | wc -l) 个物理主机"
    echo ""
    echo "=== 节点共享配置 ==="
    echo "NebulaGraph + Redis 共享节点:"
    echo "Master节点 (Meta+Storage+Graph+Redis):"
    for host in "${REDIS_MASTER_HOSTS[@]}"; do
        echo "  - $host (${REDIS_HOSTNAMES[$host]}) - NebulaGraph + Redis Master"
    done
    echo "Slave节点 (Storage+Graph+Redis):"
    for host in "${REDIS_SLAVE_HOSTS[@]}"; do
        echo "  - $host (${REDIS_HOSTNAMES[$host]}) - NebulaGraph + Redis Slave"
    done
    echo "===================="
}

# 初始化验证（仅在直接执行时进行，source时不验证）
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 只有直接执行脚本时才进行验证和退出
    if ! validate_hosts_config; then
        echo "主机配置验证失败，请检查配置" >&2
        exit 1
    fi
else
    # 被source时只进行验证但不退出
    validate_hosts_config || echo "警告: 主机配置验证失败，但继续加载" >&2
fi
