#!/bin/bash
# Redis配置文件 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，Redis集群配置

# =============================================================================
# Redis基础配置
# =============================================================================

# Redis版本配置
export REDIS_VERSION="7.0.8"

# Redis目录配置
export REDIS_HOME="/apps/redis"
export REDIS_CONFIG_DIR="/apps/redis/conf"
export REDIS_DATA_DIR="/apps/data/redis"
export REDIS_LOG_DIR="/apps/data/redis/log"
export REDIS_PID_DIR="/var/run/redis"

# Redis内存配置（与NebulaGraph共享节点，限制内存使用）
export REDIS_MAX_MEMORY="4gb"  # 减少内存使用，为NebulaGraph留出空间
export REDIS_MAX_MEMORY_POLICY="allkeys-lru"

# Redis持久化配置
export REDIS_SAVE_ENABLED="true"
export REDIS_AOF_ENABLED="true"
export REDIS_AOF_FSYNC="everysec"

# =============================================================================
# Redis集群配置
# =============================================================================

# 集群基础配置
export REDIS_CLUSTER_ENABLED="true"
export REDIS_CLUSTER_NODE_TIMEOUT="15000"  # ms
export REDIS_CLUSTER_REQUIRE_FULL_COVERAGE="no"

# 集群故障转移配置
export REDIS_CLUSTER_MIGRATION_BARRIER="1"
export REDIS_CLUSTER_REPLICA_VALIDITY_FACTOR="10"

# =============================================================================
# Redis性能配置
# =============================================================================

# 网络配置
export REDIS_TCP_KEEPALIVE="300"
export REDIS_TCP_BACKLOG="511"
export REDIS_TIMEOUT="0"

# 客户端配置
export REDIS_MAX_CLIENTS="10000"
export REDIS_CLIENT_OUTPUT_BUFFER_LIMIT="normal 0 0 0"

# 内存优化
export REDIS_HASH_MAX_ZIPLIST_ENTRIES="512"
export REDIS_HASH_MAX_ZIPLIST_VALUE="64"
export REDIS_LIST_MAX_ZIPLIST_SIZE="-2"
export REDIS_SET_MAX_INTSET_ENTRIES="512"
export REDIS_ZSET_MAX_ZIPLIST_ENTRIES="128"
export REDIS_ZSET_MAX_ZIPLIST_VALUE="64"

# =============================================================================
# Redis安全配置
# =============================================================================

# 认证配置
export REDIS_AUTH_ENABLED="true"
export REDIS_REQUIREPASS="$(openssl rand -base64 32)"
export REDIS_MASTERAUTH="$REDIS_REQUIREPASS"

# 网络安全
export REDIS_BIND_ADDRESS="0.0.0.0"
export REDIS_PROTECTED_MODE="no"

# 命令重命名（安全加固）
export REDIS_RENAME_COMMANDS="true"
declare -A REDIS_COMMAND_RENAMES=(
    ["FLUSHDB"]="FLUSHDB_$(openssl rand -hex 8)"
    ["FLUSHALL"]="FLUSHALL_$(openssl rand -hex 8)"
    ["CONFIG"]="CONFIG_$(openssl rand -hex 8)"
    ["DEBUG"]="DEBUG_$(openssl rand -hex 8)"
)

# =============================================================================
# Redis日志配置
# =============================================================================

# 日志级别和格式
export REDIS_LOG_LEVEL="notice"
export REDIS_SYSLOG_ENABLED="no"

# 慢查询日志
export REDIS_SLOWLOG_LOG_SLOWER_THAN="10000"  # 微秒
export REDIS_SLOWLOG_MAX_LEN="128"

# =============================================================================
# Redis持久化配置
# =============================================================================

# RDB配置
export REDIS_RDB_COMPRESSION="yes"
export REDIS_RDB_CHECKSUM="yes"
export REDIS_SAVE_POINTS="900 1 300 10 60 10000"

# AOF配置
export REDIS_AOF_REWRITE_PERCENTAGE="100"
export REDIS_AOF_REWRITE_MIN_SIZE="64mb"
export REDIS_NO_APPENDFSYNC_ON_REWRITE="no"

# =============================================================================
# Redis监控配置
# =============================================================================

# 监控参数
export REDIS_MONITORING_ENABLED="true"
export REDIS_EXPORTER_PORT="9121"
export REDIS_METRICS_COLLECTION_INTERVAL="15"  # seconds

# 告警阈值
export REDIS_CPU_THRESHOLD="80"        # 百分比
export REDIS_MEMORY_THRESHOLD="85"     # 百分比
export REDIS_CONNECTION_THRESHOLD="80" # 百分比
export REDIS_KEYSPACE_HITS_RATIO="0.9" # 命中率

# =============================================================================
# Redis备份配置
# =============================================================================

# 备份策略
export REDIS_BACKUP_ENABLED="true"
export REDIS_BACKUP_SCHEDULE="0 3 * * *"  # 每天凌晨3点
export REDIS_BACKUP_RETENTION_DAYS="7"
export REDIS_BACKUP_COMPRESSION="true"

# 备份路径
export REDIS_BACKUP_PATH="/data/redis/backup"
export REDIS_BACKUP_SCRIPT="/apps/scripts/redis_backup.sh"

# =============================================================================
# Redis高可用配置
# =============================================================================

# 哨兵配置（如果使用哨兵模式）
export REDIS_SENTINEL_ENABLED="false"
export REDIS_SENTINEL_PORT="26379"
export REDIS_SENTINEL_QUORUM="2"
export REDIS_SENTINEL_DOWN_AFTER="30000"  # ms
export REDIS_SENTINEL_FAILOVER_TIMEOUT="180000"  # ms

# 主从复制配置
export REDIS_REPLICATION_ENABLED="true"
export REDIS_REPLICA_READ_ONLY="yes"
export REDIS_REPLICA_SERVE_STALE_DATA="yes"

# =============================================================================
# Redis工具函数（基于hosts.conf中的配置）
# =============================================================================

# 获取Redis唯一主机列表（去重）
get_redis_unique_hosts() {
    printf '%s\n' "${REDIS_HOSTS[@]}" | sort -u
}

# 获取主机的Redis端口列表
get_host_redis_ports() {
    local host=$1
    if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
        echo "${REDIS_HOST_PORTS[$host]}"
    else
        echo ""
    fi
}

# =============================================================================
# Redis配置验证函数
# =============================================================================

# 验证Redis配置
validate_redis_config() {
    local errors=0

    # 检查必需的Redis服务参数
    local required_vars=(
        "REDIS_VERSION" "REDIS_MAX_MEMORY" "REDIS_HOME" "REDIS_DATA_DIR"
    )

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            echo "错误: 必需的Redis变量 $var 未设置" >&2
            errors=$((errors + 1))
        fi
    done

    # 检查Redis主机配置（来自hosts.conf）
    if [[ ${#REDIS_HOSTS[@]} -lt 6 ]]; then
        echo "警告: Redis集群建议至少6个节点（3主3从）以保证高可用"
    fi

    # 检查端口数量（来自hosts.conf）
    if [[ ${#REDIS_CLUSTER_PORTS[@]} -ne 6 ]]; then
        echo "错误: Redis集群需要6个端口（3主3从），当前: ${#REDIS_CLUSTER_PORTS[@]}" >&2
        errors=$((errors + 1))
    fi

    # 检查Redis主机实例数量（来自hosts.conf）
    if [[ ${#REDIS_HOSTS[@]} -ne 6 ]]; then
        echo "错误: Redis集群需要6个实例，当前: ${#REDIS_HOSTS[@]}" >&2
        errors=$((errors + 1))
    fi

    # 检查Redis内存配置合理性
    if [[ "$REDIS_MAX_MEMORY" =~ ^([0-9]+)(gb|GB|mb|MB)$ ]]; then
        local memory_value="${BASH_REMATCH[1]}"
        local memory_unit="${BASH_REMATCH[2],,}"

        if [[ "$memory_unit" == "gb" && $memory_value -gt 8 ]]; then
            echo "警告: Redis内存配置 ${REDIS_MAX_MEMORY} 可能过大，建议不超过8GB（共享节点）" >&2
        fi
    fi

    return $errors
}

# 生成Redis集群节点列表
generate_redis_cluster_nodes() {
    local nodes=()

    # 遍历所有Redis集群端口
    for port in "${REDIS_CLUSTER_PORTS[@]}"; do
        # 找到运行此端口的主机
        for host in $(get_redis_unique_hosts); do
            local host_ports=$(get_host_redis_ports "$host")
            if [[ "$host_ports" == *"$port"* ]]; then
                nodes+=("$host:$port")
                break
            fi
        done
    done

    echo "${nodes[*]}"
}

# 获取Redis主节点列表
get_redis_master_nodes() {
    local masters=()

    # 主节点端口（前3个）
    local master_ports=("7001" "7002" "7003")

    for port in "${master_ports[@]}"; do
        # 找到运行此端口的主机
        for host in $(get_redis_unique_hosts); do
            local host_ports=$(get_host_redis_ports "$host")
            if [[ "$host_ports" == *"$port"* ]]; then
                masters+=("$host:$port")
                break
            fi
        done
    done

    echo "${masters[*]}"
}

# 获取Redis从节点列表
get_redis_slave_nodes() {
    local slaves=()

    # 从节点端口（后3个）
    local slave_ports=("7004" "7005" "7006")

    for port in "${slave_ports[@]}"; do
        # 找到运行此端口的主机
        for host in $(get_redis_unique_hosts); do
            local host_ports=$(get_host_redis_ports "$host")
            if [[ "$host_ports" == *"$port"* ]]; then
                slaves+=("$host:$port")
                break
            fi
        done
    done

    echo "${slaves[*]}"
}

# =============================================================================
# Redis配置初始化
# =============================================================================

# 验证Redis配置（仅在直接执行时进行，source时不验证）
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 只有直接执行脚本时才进行验证和退出
    if ! validate_redis_config; then
        echo "Redis配置验证失败，请检查配置文件" >&2
        exit 1
    fi

    # 输出Redis配置信息
    echo "Redis配置加载完成"
    echo "Redis版本: $REDIS_VERSION"
    echo "集群端口: ${REDIS_CLUSTER_PORTS[*]}"
    echo "最大内存: $REDIS_MAX_MEMORY"
    echo "集群节点: $(generate_redis_cluster_nodes)"
    echo "主节点: $(get_redis_master_nodes)"
else
    # 被source时只进行验证但不退出
    validate_redis_config || echo "警告: Redis配置验证失败，但继续加载" >&2
fi
