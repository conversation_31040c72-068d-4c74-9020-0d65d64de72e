#!/bin/bash
# 测试Kafka UUID生成修复
# 验证生成的UUID格式正确，不包含日志输出

set -euo pipefail

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "[INFO] $*"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $*"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $*"
}

log_test() {
    echo -e "${YELLOW}[TEST]${NC} $*"
}

echo "Kafka UUID生成修复测试"
echo "======================"

# 测试UUID生成函数
test_uuid_generation() {
    log_test "测试UUID生成函数"
    
    # 模拟原始的有问题的方法（包含日志输出）
    local problematic_uuid="[2025-06-19 18:07:43] [INFO] 在 *********** 上执行命令: if command -v uuidgen >/dev/null 2>&1; then uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]'; elif command -v python3 >/dev/null 2>&1; then python3 -c 'import uuid; print(str(uuid.uuid4()).replace(\"-\", \"\"))'; else echo \$(date +%s)\$(shuf -i 1000-9999 -n 1) | md5sum | cut -d' ' -f1; fi c75a66fe582642e48a9d9f4a793b5abc"
    
    # 测试修复后的方法
    local uuid_command="
        if command -v uuidgen >/dev/null 2>&1; then
            uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]'
        elif command -v python3 >/dev/null 2>&1; then
            python3 -c 'import uuid; print(str(uuid.uuid4()).replace(\"-\", \"\"))'
        else
            # 备用方法：使用随机数生成
            echo \$(date +%s)\$(shuf -i 1000-9999 -n 1) | md5sum | cut -d' ' -f1
        fi
    "
    
    # 执行UUID生成命令（本地测试）
    local generated_uuid
    generated_uuid=$(eval "$uuid_command" 2>/dev/null | tr -d '\r\n')
    
    log_info "原始有问题的输出: $problematic_uuid"
    log_info "修复后生成的UUID: $generated_uuid"
    
    # 验证UUID格式（应该是32位十六进制字符）
    if [[ "$generated_uuid" =~ ^[a-f0-9]{32}$ ]]; then
        log_success "UUID格式验证通过: $generated_uuid"
        return 0
    else
        log_error "UUID格式验证失败: $generated_uuid"
        return 1
    fi
}

# 测试UUID验证逻辑
test_uuid_validation() {
    log_test "测试UUID验证逻辑"
    
    # 测试有效的UUID
    local valid_uuid="c75a66fe582642e48a9d9f4a793b5abc"
    if [[ "$valid_uuid" =~ ^[a-f0-9]{32}$ ]]; then
        log_success "有效UUID验证通过: $valid_uuid"
    else
        log_error "有效UUID验证失败: $valid_uuid"
        return 1
    fi
    
    # 测试无效的UUID（包含日志信息）
    local invalid_uuid="[2025-06-19 18:07:43] [INFO] 在 *********** 上执行命令: c75a66fe582642e48a9d9f4a793b5abc"
    if [[ ! "$invalid_uuid" =~ ^[a-f0-9]{32}$ ]]; then
        log_success "无效UUID验证通过（正确拒绝）: $invalid_uuid"
    else
        log_error "无效UUID验证失败（错误接受）: $invalid_uuid"
        return 1
    fi
    
    # 测试备用UUID生成
    local backup_uuid
    if command -v uuidgen >/dev/null 2>&1; then
        backup_uuid=$(uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]')
    else
        backup_uuid=$(echo "$(date +%s)$(shuf -i 1000-9999 -n 1)" | md5sum | cut -d' ' -f1)
    fi
    
    log_info "备用方法生成的UUID: $backup_uuid"
    
    if [[ "$backup_uuid" =~ ^[a-f0-9]{32}$ ]]; then
        log_success "备用UUID验证通过: $backup_uuid"
    else
        log_error "备用UUID验证失败: $backup_uuid"
        return 1
    fi
}

# 测试docker-compose配置生成
test_docker_compose_config() {
    log_test "测试docker-compose配置生成"
    
    # 模拟配置变量
    local KAFKA_IMAGE="apache/kafka-native:3.9.0"
    local node_id=1
    local host="***********"
    local KAFKA_PORT="9092"
    local KAFKA_CONTROLLER_PORT="9093"
    local KAFKA_INTERNAL_PORT="19092"
    local KAFKA_DATA_DIR="/apps/data/kafka"
    local KAFKA_LOGS_DIR="/apps/logs/kafka"
    local quorum_voters="1@***********:9093,2@***********:9093,3@***********:9093"
    
    # 生成一个有效的UUID
    local KAFKA_CLUSTER_ID
    if command -v uuidgen >/dev/null 2>&1; then
        KAFKA_CLUSTER_ID=$(uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]')
    else
        KAFKA_CLUSTER_ID=$(echo "$(date +%s)$(shuf -i 1000-9999 -n 1)" | md5sum | cut -d' ' -f1)
    fi
    
    log_info "使用的集群ID: $KAFKA_CLUSTER_ID"
    
    # 生成docker-compose配置
    local compose_config="version: '3.3'

services:
  kafka:
    image: $KAFKA_IMAGE
    hostname: kafka-$node_id
    container_name: kafka-$node_id
    ports:
      - \"$KAFKA_PORT:9092\"
      - \"$KAFKA_CONTROLLER_PORT:9093\"
    environment:
      KAFKA_NODE_ID: $node_id
      KAFKA_PROCESS_ROLES: 'broker,controller'
      KAFKA_CONTROLLER_QUORUM_VOTERS: '$quorum_voters'
      KAFKA_LISTENERS: 'PLAINTEXT://:$KAFKA_INTERNAL_PORT,CONTROLLER://:$KAFKA_CONTROLLER_PORT,PLAINTEXT_HOST://:$KAFKA_PORT'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'PLAINTEXT'
      KAFKA_ADVERTISED_LISTENERS: 'PLAINTEXT://kafka-$node_id:$KAFKA_INTERNAL_PORT,PLAINTEXT_HOST://$host:$KAFKA_PORT'
      KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
      KAFKA_LOG_DIRS: '/var/lib/kafka/data'
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_NUM_PARTITIONS: 3
      CLUSTER_ID: '$KAFKA_CLUSTER_ID'
    volumes:
      - $KAFKA_DATA_DIR:/var/lib/kafka/data:rw
      - $KAFKA_LOGS_DIR:/var/log/kafka:rw
    restart: unless-stopped

networks:
  default:
    driver: bridge"
    
    # 检查配置中的CLUSTER_ID是否正确
    if echo "$compose_config" | grep -q "CLUSTER_ID: '$KAFKA_CLUSTER_ID'"; then
        log_success "docker-compose配置中的CLUSTER_ID正确"
    else
        log_error "docker-compose配置中的CLUSTER_ID不正确"
        return 1
    fi
    
    # 检查配置中是否包含日志信息
    if echo "$compose_config" | grep -q "\[INFO\]"; then
        log_error "docker-compose配置中包含日志信息"
        return 1
    else
        log_success "docker-compose配置中不包含日志信息"
    fi
    
    # 保存测试配置到临时文件
    local test_file="/tmp/test_docker_compose_$$.yml"
    echo "$compose_config" > "$test_file"
    
    log_info "测试配置已保存到: $test_file"
    log_info "配置内容预览:"
    echo "---"
    head -20 "$test_file"
    echo "..."
    tail -5 "$test_file"
    echo "---"
    
    # 清理测试文件
    rm -f "$test_file"
}

# 运行所有测试
main() {
    local test_count=0
    local pass_count=0
    
    # 测试UUID生成
    test_count=$((test_count + 1))
    if test_uuid_generation; then
        pass_count=$((pass_count + 1))
    fi
    
    echo ""
    
    # 测试UUID验证
    test_count=$((test_count + 1))
    if test_uuid_validation; then
        pass_count=$((pass_count + 1))
    fi
    
    echo ""
    
    # 测试docker-compose配置
    test_count=$((test_count + 1))
    if test_docker_compose_config; then
        pass_count=$((pass_count + 1))
    fi
    
    echo ""
    echo "测试结果总结"
    echo "============"
    echo "总测试数: $test_count"
    echo "通过数: $pass_count"
    echo "失败数: $((test_count - pass_count))"
    
    if [[ $pass_count -eq $test_count ]]; then
        log_success "所有测试通过！Kafka UUID生成修复成功"
        return 0
    else
        log_error "部分测试失败，需要进一步检查"
        return 1
    fi
}

# 执行测试
main "$@"
