#!/bin/bash
# 测试本机IP保护功能

set -euo pipefail

# 测试目录
TEST_DIR="/tmp/test_local_ip_protection_$$"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 清理函数
cleanup() {
    echo "清理测试环境..."
    rm -rf "$TEST_DIR" 2>/dev/null || true
}

# 设置清理陷阱
trap cleanup EXIT

echo "=== 本机IP保护功能测试 ==="

# 创建测试环境
echo "1. 创建测试环境..."
mkdir -p "$TEST_DIR"

# 提取is_local_ip函数进行单独测试
echo "2. 提取并测试is_local_ip函数..."
cat > "$TEST_DIR/test_is_local_ip.sh" << 'EOF'
#!/bin/bash

# 检查是否为本机IP的函数
is_local_ip() {
    local target_ip=$1
    
    # 获取本机所有IP地址
    local local_ips=()
    
    # 方法1: 使用hostname -I (如果可用)
    if command -v hostname >/dev/null 2>&1; then
        local hostname_ips=$(hostname -I 2>/dev/null | tr ' ' '\n' | grep -v '^$')
        if [[ -n "$hostname_ips" ]]; then
            while IFS= read -r ip; do
                [[ -n "$ip" ]] && local_ips+=("$ip")
            done <<< "$hostname_ips"
        fi
    fi
    
    # 方法2: 使用ip命令 (如果可用)
    if command -v ip >/dev/null 2>&1; then
        local ip_addrs=$(ip addr show 2>/dev/null | grep -oP 'inet \K[0-9.]+' | grep -v '127.0.0.1')
        if [[ -n "$ip_addrs" ]]; then
            while IFS= read -r ip; do
                [[ -n "$ip" ]] && local_ips+=("$ip")
            done <<< "$ip_addrs"
        fi
    fi
    
    # 方法3: 使用ifconfig命令 (如果可用)
    if command -v ifconfig >/dev/null 2>&1; then
        local ifconfig_ips=$(ifconfig 2>/dev/null | grep -oP 'inet \K[0-9.]+' | grep -v '127.0.0.1')
        if [[ -n "$ifconfig_ips" ]]; then
            while IFS= read -r ip; do
                [[ -n "$ip" ]] && local_ips+=("$ip")
            done <<< "$ifconfig_ips"
        fi
    fi
    
    # 添加常见的本机地址
    local_ips+=("127.0.0.1" "localhost" "::1")
    
    # 去重
    local_ips=($(printf '%s\n' "${local_ips[@]}" | sort -u))
    
    # 调试输出
    echo "检测到的本机IP列表: ${local_ips[*]}" >&2
    
    # 检查目标IP是否在本机IP列表中
    for local_ip in "${local_ips[@]}"; do
        if [[ "$target_ip" == "$local_ip" ]]; then
            echo "检测到本机IP: $target_ip" >&2
            return 0
        fi
    done
    
    # 特殊处理：如果目标IP能解析为本机hostname
    local hostname=$(hostname 2>/dev/null || echo "")
    if [[ -n "$hostname" && "$target_ip" == "$hostname" ]]; then
        echo "检测到本机hostname: $target_ip" >&2
        return 0
    fi
    
    echo "非本机IP: $target_ip" >&2
    return 1
}

# 测试函数
test_ip() {
    local ip=$1
    local expected=$2
    
    echo -n "测试IP: $ip ... "
    if is_local_ip "$ip" 2>/dev/null; then
        result="本机IP"
        exit_code=0
    else
        result="非本机IP"
        exit_code=1
    fi
    
    if [[ "$result" == "$expected" ]]; then
        echo "✓ 通过 ($result)"
        return 0
    else
        echo "✗ 失败 (期望: $expected, 实际: $result)"
        return 1
    fi
}

echo "=== 本机IP检测测试 ==="

# 测试用例
test_count=0
pass_count=0

# 测试localhost
test_count=$((test_count + 1))
if test_ip "127.0.0.1" "本机IP"; then
    pass_count=$((pass_count + 1))
fi

test_count=$((test_count + 1))
if test_ip "localhost" "本机IP"; then
    pass_count=$((pass_count + 1))
fi

# 测试明显的非本机IP
test_count=$((test_count + 1))
if test_ip "*******" "非本机IP"; then
    pass_count=$((pass_count + 1))
fi

test_count=$((test_count + 1))
if test_ip "***********" "非本机IP"; then
    pass_count=$((pass_count + 1))
fi

test_count=$((test_count + 1))
if test_ip "********" "非本机IP"; then
    pass_count=$((pass_count + 1))
fi

# 获取实际的本机IP进行测试
if command -v hostname >/dev/null 2>&1; then
    local_hostname=$(hostname 2>/dev/null || echo "")
    if [[ -n "$local_hostname" && "$local_hostname" != "localhost" ]]; then
        test_count=$((test_count + 1))
        if test_ip "$local_hostname" "本机IP"; then
            pass_count=$((pass_count + 1))
        fi
    fi
fi

echo ""
echo "=== 测试结果 ==="
echo "总测试数: $test_count"
echo "通过数: $pass_count"
echo "失败数: $((test_count - pass_count))"

if [[ $pass_count -eq $test_count ]]; then
    echo "✓ 所有测试通过"
    exit 0
else
    echo "✗ 部分测试失败"
    exit 1
fi
EOF

chmod +x "$TEST_DIR/test_is_local_ip.sh"

# 运行IP检测测试
echo "3. 运行IP检测测试..."
if bash "$TEST_DIR/test_is_local_ip.sh"; then
    echo "✓ IP检测功能测试通过"
else
    echo "✗ IP检测功能测试失败"
    exit 1
fi

# 测试脚本中的安全检查
echo "4. 测试脚本语法..."
if bash -n "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ 脚本语法检查通过"
else
    echo "✗ 脚本语法检查失败"
    exit 1
fi

# 检查安全检查代码是否存在
echo "5. 检查安全检查代码..."
if grep -q "is_local_ip.*host" "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ 安全检查代码存在"
else
    echo "✗ 安全检查代码缺失"
    exit 1
fi

# 检查错误消息
echo "6. 检查安全错误消息..."
if grep -q "禁止清空目录以防止意外删除本地文件" "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ 安全错误消息存在"
else
    echo "✗ 安全错误消息缺失"
    exit 1
fi

# 检查手动操作提示
echo "7. 检查手动操作提示..."
if grep -q "如需清空本机目录，请手动执行" "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ 手动操作提示存在"
else
    echo "✗ 手动操作提示缺失"
    exit 1
fi

echo ""
echo "=== 测试结果 ==="
echo "✓ 所有测试通过！"
echo ""
echo "本机IP保护功能已成功集成到 distribute_packages.sh 中"
echo ""
echo "安全特性："
echo "1. 自动检测目标IP是否为本机IP"
echo "2. 如果是本机IP，禁止自动清空目录"
echo "3. 提供清晰的错误消息和手动操作指导"
echo "4. 支持多种IP检测方法（hostname、ip、ifconfig）"
echo "5. 包含常见本机地址（127.0.0.1、localhost、::1）"
echo ""
echo "保护范围："
echo "- Docker镜像分发时的目录清空操作"
echo "- 普通目录分发时的目录清空操作"
echo "- 所有使用 --force-overwrite 参数的清空操作"
