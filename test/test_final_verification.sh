#!/bin/bash
# Redis密码认证问题最终验证脚本

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "[INFO] $*"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $*"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $*"
}

log_test() {
    echo -e "${YELLOW}[TEST]${NC} $*"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $*"
}

echo "Redis密码认证问题修复最终验证"
echo "================================"

# 验证计数器
total_tests=0
passed_tests=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((total_tests++))
    log_test "$test_name"
    
    if eval "$test_command"; then
        log_success "$test_name - 通过"
        ((passed_tests++))
        return 0
    else
        log_error "$test_name - 失败"
        return 1
    fi
}

# 测试1: 脚本语法检查
log_header "1. 脚本语法检查"

run_test "deploy_redis.sh语法检查" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/deploy_redis.sh'"
run_test "redis_quick_fix.sh语法检查" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/tools/redis_quick_fix.sh'"
run_test "fix_redis_auth.sh语法检查" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/tools/fix_redis_auth.sh'"
run_test "verify_redis_passwords.sh语法检查" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/tools/verify_redis_passwords.sh'"
run_test "get_redis_password.sh语法检查" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/tools/get_redis_password.sh'"

echo

# 测试2: 工具文件存在性检查
log_header "2. 工具文件存在性检查"

tools=(
    "scripts/auto_deploy/tools/redis_quick_fix.sh"
    "scripts/auto_deploy/tools/fix_redis_auth.sh"
    "scripts/auto_deploy/tools/verify_redis_passwords.sh"
    "scripts/auto_deploy/tools/get_redis_password.sh"
    "scripts/auto_deploy/tools/README_redis_fix.md"
)

for tool in "${tools[@]}"; do
    run_test "$(basename "$tool")存在性检查" "[[ -f '$PROJECT_ROOT/$tool' ]]"
done

echo

# 测试3: 执行权限检查
log_header "3. 执行权限检查"

executable_tools=(
    "scripts/auto_deploy/tools/redis_quick_fix.sh"
    "scripts/auto_deploy/tools/fix_redis_auth.sh"
    "scripts/auto_deploy/tools/verify_redis_passwords.sh"
    "scripts/auto_deploy/tools/get_redis_password.sh"
)

for tool in "${executable_tools[@]}"; do
    run_test "$(basename "$tool")执行权限检查" "[[ -x '$PROJECT_ROOT/$tool' ]]"
done

echo

# 测试4: 帮助功能检查
log_header "4. 帮助功能检查"

help_tools=(
    "scripts/auto_deploy/tools/fix_redis_auth.sh"
    "scripts/auto_deploy/tools/verify_redis_passwords.sh"
    "scripts/auto_deploy/tools/get_redis_password.sh"
)

for tool in "${help_tools[@]}"; do
    run_test "$(basename "$tool")帮助功能检查" "'$PROJECT_ROOT/$tool' --help >/dev/null 2>&1"
done

echo

# 测试5: 部署脚本修复验证
log_header "5. 部署脚本修复验证"

deploy_script="$PROJECT_ROOT/scripts/auto_deploy/deploy_redis.sh"

run_test "密码生成逻辑修复检查" "grep -q 'master_password=\"\$password\"' '$deploy_script'"
run_test "密码配置说明检查" "grep -q 'requirepass和masterauth使用相同密码' '$deploy_script'"
run_test "改进的服务状态检查" "grep -q '检查节点.*的Redis服务状态' '$deploy_script'"
run_test "详细错误诊断检查" "grep -q '显示详细的调试信息' '$deploy_script'"
run_test "修复建议检查" "grep -q '修复建议' '$deploy_script'"

echo

# 测试6: sed命令修复验证
log_header "6. sed命令修复验证"

run_test "redis_quick_fix.sh中sed命令检查" "grep -q 'sed -i \"s/\^masterauth.*/masterauth \$requirepass/\"' '$PROJECT_ROOT/scripts/auto_deploy/tools/redis_quick_fix.sh'"
run_test "verify_redis_passwords.sh中sed命令检查" "grep -q 'sed -i \"s/\^masterauth.*/masterauth \$requirepass/\"' '$PROJECT_ROOT/scripts/auto_deploy/tools/verify_redis_passwords.sh'"

echo

# 测试7: 文档更新验证
log_header "7. 文档更新验证"

doc_file="$PROJECT_ROOT/scripts/auto_deploy/docs/redis_deployment.md"
readme_file="$PROJECT_ROOT/scripts/auto_deploy/tools/README_redis_fix.md"

run_test "密码配置说明文档检查" "grep -q '必须设置为相同值' '$doc_file'"
run_test "故障排除工具文档检查" "grep -q '故障排除工具' '$doc_file'"
run_test "密码验证工具文档检查" "grep -q 'verify_redis_passwords.sh' '$doc_file'"
run_test "README密码不一致问题检查" "grep -q '密码配置不一致' '$readme_file'"
run_test "README sed修复说明检查" "grep -q 'sed命令引号处理问题' '$readme_file'"

echo

# 测试8: 测试脚本验证
log_header "8. 测试脚本验证"

test_scripts=(
    "test/test_redis_fix_simple.sh"
    "test/test_redis_password_consistency.sh"
    "test/test_sed_fix.sh"
)

for test_script in "${test_scripts[@]}"; do
    if [[ -f "$PROJECT_ROOT/$test_script" ]]; then
        run_test "$(basename "$test_script")语法检查" "bash -n '$PROJECT_ROOT/$test_script'"
    else
        log_error "测试脚本不存在: $test_script"
    fi
done

echo

# 最终总结
log_header "验证总结"
echo "================================"

if [[ $passed_tests -eq $total_tests ]]; then
    log_success "✓ 所有验证都通过了！($passed_tests/$total_tests)"
    echo
    log_info "Redis密码认证问题修复完成，包括："
    echo
    log_info "核心修复："
    log_info "  ✓ 修复了部署脚本中的密码生成逻辑"
    log_info "  ✓ 确保requirepass和masterauth使用相同密码"
    log_info "  ✓ 修复了sed命令的引号处理问题"
    echo
    log_info "新增工具："
    log_info "  ✓ redis_quick_fix.sh - 一键快速修复"
    log_info "  ✓ fix_redis_auth.sh - 详细诊断修复"
    log_info "  ✓ verify_redis_passwords.sh - 密码验证"
    log_info "  ✓ get_redis_password.sh - 密码获取"
    echo
    log_info "增强功能："
    log_info "  ✓ 详细的错误诊断和修复建议"
    log_info "  ✓ 自动密码一致性检查和修复"
    log_info "  ✓ 完整的文档和使用说明"
    echo
    log_success "现在可以安全使用Redis部署和修复工具！"
    echo
    log_info "推荐使用方法："
    log_info "1. 如果遇到密码认证失败："
    log_info "   ./scripts/auto_deploy/tools/redis_quick_fix.sh"
    echo
    log_info "2. 重新部署Redis集群："
    log_info "   ./scripts/auto_deploy/deploy_redis.sh"
    echo
    log_info "3. 验证密码配置："
    log_info "   ./scripts/auto_deploy/tools/verify_redis_passwords.sh"
    
    exit 0
else
    log_error "✗ 有 $((total_tests - passed_tests)) 个验证失败 ($passed_tests/$total_tests)"
    echo
    log_error "请检查失败的项目并修复后重新运行验证"
    exit 1
fi
