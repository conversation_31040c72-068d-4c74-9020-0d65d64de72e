#!/bin/bash
# Kafka Native Dockerfile 测试脚本
# 验证自定义 Dockerfile 和构建过程

set -euo pipefail

# =============================================================================
# 测试初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "=== Kafka Native Dockerfile 测试 ==="
echo "项目根目录: $PROJECT_ROOT"
echo "测试时间: $(date)"
echo ""

# =============================================================================
# 测试配置
# =============================================================================

DOCKERFILE_PATH="$PROJECT_ROOT/Dockerfile.kafka-native-with-scripts"
BUILD_SCRIPT="$PROJECT_ROOT/scripts/auto_deploy/build_kafka_image.sh"
KAFKA_BIN_ZIP="$PROJECT_ROOT/software-repo/kafka-bin.zip"
TEST_IMAGE_NAME="apache/kafka-native-with-scripts:3.9.0"

# =============================================================================
# 辅助函数
# =============================================================================

log_info() {
    echo "[INFO] $*"
}

log_error() {
    echo "[ERROR] $*" >&2
}

test_passed() {
    echo "✓ $*"
}

test_failed() {
    echo "✗ $*"
    exit 1
}

# =============================================================================
# 文件存在性测试
# =============================================================================

echo "1. 检查必要文件是否存在..."

if [[ -f "$DOCKERFILE_PATH" ]]; then
    test_passed "Dockerfile 存在: $DOCKERFILE_PATH"
else
    test_failed "Dockerfile 不存在: $DOCKERFILE_PATH"
fi

if [[ -f "$BUILD_SCRIPT" ]]; then
    test_passed "构建脚本存在: $BUILD_SCRIPT"
else
    test_failed "构建脚本不存在: $BUILD_SCRIPT"
fi

if [[ -f "$KAFKA_BIN_ZIP" ]]; then
    test_passed "Kafka bin 文件存在: $KAFKA_BIN_ZIP"
else
    test_failed "Kafka bin 文件不存在: $KAFKA_BIN_ZIP"
fi

# =============================================================================
# Dockerfile 内容验证
# =============================================================================

echo ""
echo "2. 验证 Dockerfile 内容..."

# 检查基础镜像
if grep -q "FROM apache/kafka:3.9.0" "$DOCKERFILE_PATH"; then
    test_passed "基础镜像配置正确"
else
    test_failed "基础镜像配置错误"
fi

# 检查关键指令
dockerfile_checks=(
    "COPY software-repo/kafka-bin/bin/"
    "chmod +x.*bin/.*\.sh"
    "ENV PATH.*kafka/bin"
    "kafka-broker-api-versions.sh"
)

for check in "${dockerfile_checks[@]}"; do
    if grep -q "$check" "$DOCKERFILE_PATH"; then
        test_passed "包含指令: $check"
    else
        test_failed "缺少指令: $check"
    fi
done

# =============================================================================
# 构建脚本验证
# =============================================================================

echo ""
echo "3. 验证构建脚本..."

# 检查脚本权限
if [[ -x "$BUILD_SCRIPT" ]]; then
    test_passed "构建脚本有执行权限"
else
    chmod +x "$BUILD_SCRIPT"
    test_passed "已设置构建脚本执行权限"
fi

# 检查脚本内容
build_script_checks=(
    "NEW_IMAGE_NAME.*apache/kafka-native-with-scripts"
    "prepare_kafka_bin"
    "unzip.*kafka-bin.zip"
    "docker build"
    "docker save"
    "force-rebuild"
    "dry-run"
)

for check in "${build_script_checks[@]}"; do
    if grep -q "$check" "$BUILD_SCRIPT"; then
        test_passed "构建脚本包含: $check"
    else
        test_failed "构建脚本缺少: $check"
    fi
done

# =============================================================================
# Docker 环境检查
# =============================================================================

echo ""
echo "4. 检查 Docker 环境（可选）..."

if command -v docker >/dev/null 2>&1; then
    test_passed "Docker 已安装"

    if docker info >/dev/null 2>&1; then
        test_passed "Docker 服务运行正常"

        # 检查基础镜像
        if docker image inspect apache/kafka:3.9.0 >/dev/null 2>&1; then
            test_passed "基础镜像已存在"
        else
            log_info "基础镜像不存在，但这不影响基本验证"
            echo "✓ 基础镜像检查跳过（可在实际构建时拉取）"
        fi
    else
        log_info "Docker 服务未运行，跳过 Docker 相关检查"
        echo "✓ Docker 环境检查跳过（可在实际构建时检查）"
    fi
else
    log_info "Docker 未安装，跳过 Docker 相关检查"
    echo "✓ Docker 环境检查跳过（需要在构建环境中安装 Docker）"
fi

# =============================================================================
# kafka-bin.zip 内容检查
# =============================================================================

echo ""
echo "5. 检查 kafka-bin.zip 内容..."

# 创建临时目录检查内容
temp_dir=$(mktemp -d)
cd "$temp_dir"

if unzip -q "$KAFKA_BIN_ZIP"; then
    test_passed "kafka-bin.zip 解压成功"
    
    # 检查关键脚本
    required_scripts=(
        "kafka-topics.sh"
        "kafka-console-producer.sh"
        "kafka-console-consumer.sh"
        "kafka-broker-api-versions.sh"
    )
    
    for script in "${required_scripts[@]}"; do
        if find . -name "$script" | grep -q .; then
            test_passed "包含脚本: $script"
        else
            test_failed "缺少脚本: $script"
        fi
    done
    
    # 显示目录结构
    echo "kafka-bin.zip 目录结构:"
    find . -type f -name "*.sh" | head -10
    
else
    test_failed "kafka-bin.zip 解压失败"
fi

# 清理临时目录
cd "$PROJECT_ROOT"
rm -rf "$temp_dir"

# =============================================================================
# 构建测试（干运行）
# =============================================================================

echo ""
echo "6. 测试构建脚本（干运行）..."

if bash "$BUILD_SCRIPT" --dry-run; then
    test_passed "构建脚本干运行成功"
else
    test_failed "构建脚本干运行失败"
fi

# =============================================================================
# 部署脚本更新验证
# =============================================================================

echo ""
echo "7. 验证部署脚本更新..."

DEPLOY_SCRIPT="$PROJECT_ROOT/scripts/auto_deploy/deploy_kafka.sh"

if [[ -f "$DEPLOY_SCRIPT" ]]; then
    if grep -q "apache/kafka-native-with-scripts:3.9.0" "$DEPLOY_SCRIPT"; then
        test_passed "部署脚本已更新镜像名称"
    else
        test_failed "部署脚本未更新镜像名称"
    fi
else
    test_failed "部署脚本不存在"
fi

# =============================================================================
# 实际构建测试（可选）
# =============================================================================

echo ""
echo "8. 实际构建测试（可选）..."

read -p "是否执行实际构建测试？这将构建 Docker 镜像 (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "开始实际构建测试..."
    
    if bash "$BUILD_SCRIPT" --force-rebuild; then
        test_passed "镜像构建成功"
        
        # 验证镜像
        if docker image inspect "$TEST_IMAGE_NAME" >/dev/null 2>&1; then
            test_passed "镜像创建成功"
            
            # 测试关键脚本
            echo "测试镜像中的脚本..."
            
            test_scripts=(
                "kafka-topics.sh --version"
                "kafka-console-producer.sh --version"
                "kafka-console-consumer.sh --version"
                "kafka-broker-api-versions.sh --version"
            )
            
            for script_cmd in "${test_scripts[@]}"; do
                if docker run --rm "$TEST_IMAGE_NAME" $script_cmd >/dev/null 2>&1; then
                    test_passed "脚本测试成功: $script_cmd"
                else
                    echo "✗ 脚本测试失败: $script_cmd"
                fi
            done
            
            # 显示镜像信息
            echo ""
            echo "镜像信息:"
            docker images "$TEST_IMAGE_NAME"
            
        else
            test_failed "镜像创建失败"
        fi
    else
        test_failed "镜像构建失败"
    fi
else
    log_info "跳过实际构建测试"
fi

# =============================================================================
# 测试总结
# =============================================================================

echo ""
echo "=== 测试结果 ==="
echo "✓ 所有基础测试通过！"
echo ""
echo "Kafka Native Dockerfile 验证完成"
echo ""
echo "主要组件："
echo "1. ✓ Dockerfile.kafka-native-with-scripts - 自定义 Dockerfile"
echo "2. ✓ scripts/auto_deploy/build_kafka_image.sh - 构建脚本"
echo "3. ✓ software-repo/kafka-bin.zip - Kafka 脚本包"
echo "4. ✓ scripts/auto_deploy/deploy_kafka.sh - 更新的部署脚本"
echo ""
echo "下一步操作："
echo "1. 运行构建脚本: bash scripts/auto_deploy/build_kafka_image.sh"
echo "2. 验证镜像: docker run --rm apache/kafka-native-with-scripts:3.9.0 kafka-topics.sh --version"
echo "3. 部署 Kafka: bash scripts/auto_deploy/deploy_kafka.sh --single-mode"
echo ""
echo "文档参考: docs/kafka_native_with_scripts.md"
