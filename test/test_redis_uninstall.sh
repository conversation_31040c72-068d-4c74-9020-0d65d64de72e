#!/bin/bash

# Redis卸载功能测试脚本
# 用于验证Redis卸载过程中的错误处理是否正确

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 设置测试环境变量
export LOG_DIR="$TEST_LOG_DIR"
export DEPLOY_ENV="test"

# 加载配置
source "$PROJECT_ROOT/scripts/auto_deploy/config/hosts.conf"

# 测试配置
TEST_LOG_DIR="$PROJECT_ROOT/test/logs"
mkdir -p "$TEST_LOG_DIR"

# 测试函数
test_redis_uninstall_dry_run() {
    echo "=== 测试Redis卸载 (Dry Run) ==="
    
    # 执行干运行模式的卸载
    cd "$PROJECT_ROOT/scripts/auto_deploy"
    
    echo "执行Redis卸载测试..."
    if bash deploy_redis.sh --uninstall --dry-run --no-backup; then
        echo "✓ Redis卸载干运行测试通过"
        return 0
    else
        echo "✗ Redis卸载干运行测试失败"
        return 1
    fi
}

test_redis_config_validation() {
    echo "=== 测试Redis配置验证 ==="
    
    # 验证Redis主机配置
    if [[ ${#REDIS_HOSTS[@]} -eq 6 ]]; then
        echo "✓ Redis主机数量正确: ${#REDIS_HOSTS[@]}"
    else
        echo "✗ Redis主机数量错误: ${#REDIS_HOSTS[@]}"
        return 1
    fi
    
    # 验证Master节点配置
    if [[ ${#REDIS_MASTER_HOSTS[@]} -eq 3 ]]; then
        echo "✓ Redis Master节点数量正确: ${#REDIS_MASTER_HOSTS[@]}"
    else
        echo "✗ Redis Master节点数量错误: ${#REDIS_MASTER_HOSTS[@]}"
        return 1
    fi
    
    # 验证Slave节点配置
    if [[ ${#REDIS_SLAVE_HOSTS[@]} -eq 3 ]]; then
        echo "✓ Redis Slave节点数量正确: ${#REDIS_SLAVE_HOSTS[@]}"
    else
        echo "✗ Redis Slave节点数量错误: ${#REDIS_SLAVE_HOSTS[@]}"
        return 1
    fi
    
    # 验证主从关系配置
    local master_slave_count=${#REDIS_MASTER_SLAVE_MAP[@]}
    if [[ $master_slave_count -eq 3 ]]; then
        echo "✓ Redis主从关系配置正确: $master_slave_count 对"
    else
        echo "✗ Redis主从关系配置错误: $master_slave_count 对"
        return 1
    fi
    
    echo "Redis配置验证通过"
    return 0
}

test_redis_host_ports() {
    echo "=== 测试Redis主机端口配置 ==="
    
    local all_configured=true
    
    for host in "${REDIS_HOSTS[@]}"; do
        if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
            echo "✓ $host 端口配置: ${REDIS_HOST_PORTS[$host]}"
        else
            echo "✗ $host 缺少端口配置"
            all_configured=false
        fi
    done
    
    if [[ "$all_configured" == "true" ]]; then
        echo "✓ 所有Redis主机端口配置正确"
        return 0
    else
        echo "✗ Redis主机端口配置不完整"
        return 1
    fi
}

test_redis_backup_function() {
    echo "=== 测试Redis备份函数逻辑 ==="
    
    # 模拟备份函数的逻辑测试
    local test_host="***********"
    local host_ports="${REDIS_HOST_PORTS[$test_host]}"
    
    if [[ -n "$host_ports" ]]; then
        echo "✓ 测试主机 $test_host 端口配置: $host_ports"
        
        # 解析端口列表
        IFS=',' read -ra ports <<< "$host_ports"
        for port in "${ports[@]}"; do
            echo "  - 端口: $port"
        done
        
        echo "✓ 备份函数逻辑测试通过"
        return 0
    else
        echo "✗ 测试主机端口配置缺失"
        return 1
    fi
}

# 主测试函数
main() {
    echo "开始Redis卸载功能测试..."
    echo "测试时间: $(date)"
    echo "项目根目录: $PROJECT_ROOT"
    echo ""
    
    local test_results=()
    
    # 执行各项测试
    if test_redis_config_validation; then
        test_results+=("配置验证: 通过")
    else
        test_results+=("配置验证: 失败")
    fi
    
    if test_redis_host_ports; then
        test_results+=("端口配置: 通过")
    else
        test_results+=("端口配置: 失败")
    fi
    
    if test_redis_backup_function; then
        test_results+=("备份逻辑: 通过")
    else
        test_results+=("备份逻辑: 失败")
    fi
    
    if test_redis_uninstall_dry_run; then
        test_results+=("卸载测试: 通过")
    else
        test_results+=("卸载测试: 失败")
    fi
    
    # 输出测试结果
    echo ""
    echo "=== 测试结果汇总 ==="
    for result in "${test_results[@]}"; do
        echo "  $result"
    done
    
    # 检查是否所有测试都通过
    local failed_count=$(printf '%s\n' "${test_results[@]}" | grep -c "失败" || true)
    
    if [[ $failed_count -eq 0 ]]; then
        echo ""
        echo "✅ 所有测试通过！Redis卸载功能修复成功。"
        return 0
    else
        echo ""
        echo "❌ 有 $failed_count 项测试失败，请检查相关配置。"
        return 1
    fi
}

# 执行测试
main "$@"
