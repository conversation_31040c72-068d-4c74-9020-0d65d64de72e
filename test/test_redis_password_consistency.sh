#!/bin/bash
# Redis密码一致性修复测试脚本

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "[INFO] $*"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $*"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $*"
}

log_test() {
    echo -e "${YELLOW}[TEST]${NC} $*"
}

echo "Redis密码一致性修复验证"
echo "========================"

# 测试1: 检查部署脚本中的密码生成逻辑
log_test "检查部署脚本密码生成逻辑..."

deploy_script="$PROJECT_ROOT/scripts/auto_deploy/deploy_redis.sh"
if [[ ! -f "$deploy_script" ]]; then
    log_error "部署脚本不存在"
    exit 1
fi

# 检查是否修复了随机密码生成逻辑
if grep -q 'master_password="\$password"' "$deploy_script"; then
    log_success "部署脚本已修复：随机密码生成时使用相同密码"
else
    log_error "部署脚本未修复：随机密码生成逻辑"
    exit 1
fi

# 检查是否添加了密码配置说明
if grep -q "requirepass和masterauth使用相同密码" "$deploy_script"; then
    log_success "部署脚本包含密码配置说明"
else
    log_error "部署脚本缺少密码配置说明"
    exit 1
fi

echo

# 测试2: 检查密码验证工具
log_test "检查密码验证工具..."

verify_tool="$PROJECT_ROOT/scripts/auto_deploy/tools/verify_redis_passwords.sh"
if [[ -f "$verify_tool" && -x "$verify_tool" ]]; then
    log_success "密码验证工具存在且可执行"
else
    log_error "密码验证工具不存在或不可执行"
    exit 1
fi

# 测试帮助功能
if "$verify_tool" --help >/dev/null 2>&1; then
    log_success "密码验证工具帮助功能正常"
else
    log_error "密码验证工具帮助功能异常"
    exit 1
fi

echo

# 测试3: 检查快速修复工具的更新
log_test "检查快速修复工具更新..."

quick_fix_tool="$PROJECT_ROOT/scripts/auto_deploy/tools/redis_quick_fix.sh"
if [[ ! -f "$quick_fix_tool" ]]; then
    log_error "快速修复工具不存在"
    exit 1
fi

# 检查是否包含密码一致性检查
if grep -q "requirepass和masterauth密码一致" "$quick_fix_tool"; then
    log_success "快速修复工具包含密码一致性检查"
else
    log_error "快速修复工具缺少密码一致性检查"
    exit 1
fi

# 检查是否包含自动修复逻辑
if grep -q "sed -i 's/^masterauth.*/masterauth" "$quick_fix_tool"; then
    log_success "快速修复工具包含自动修复逻辑"
else
    log_error "快速修复工具缺少自动修复逻辑"
    exit 1
fi

echo

# 测试4: 模拟密码配置测试
log_test "模拟密码配置测试..."

# 创建临时配置文件测试
temp_config_dir="/tmp/redis_test_$$"
mkdir -p "$temp_config_dir"

# 测试场景1: 密码一致
cat > "$temp_config_dir/redis-consistent.conf" << EOF
port 6379
requirepass TestPassword123
masterauth TestPassword123
EOF

requirepass1=$(grep '^requirepass' "$temp_config_dir/redis-consistent.conf" | awk '{print $2}')
masterauth1=$(grep '^masterauth' "$temp_config_dir/redis-consistent.conf" | awk '{print $2}')

if [[ "$requirepass1" == "$masterauth1" ]]; then
    log_success "密码一致性检测逻辑正确"
else
    log_error "密码一致性检测逻辑错误"
    rm -rf "$temp_config_dir"
    exit 1
fi

# 测试场景2: 密码不一致
cat > "$temp_config_dir/redis-inconsistent.conf" << EOF
port 6379
requirepass TestPassword123
masterauth DifferentPassword456
EOF

requirepass2=$(grep '^requirepass' "$temp_config_dir/redis-inconsistent.conf" | awk '{print $2}')
masterauth2=$(grep '^masterauth' "$temp_config_dir/redis-inconsistent.conf" | awk '{print $2}')

if [[ "$requirepass2" != "$masterauth2" ]]; then
    log_success "密码不一致检测逻辑正确"
    
    # 测试修复逻辑
    sed -i "s/^masterauth.*/masterauth $requirepass2/" "$temp_config_dir/redis-inconsistent.conf"
    masterauth2_fixed=$(grep '^masterauth' "$temp_config_dir/redis-inconsistent.conf" | awk '{print $2}')
    
    if [[ "$requirepass2" == "$masterauth2_fixed" ]]; then
        log_success "密码修复逻辑正确"
    else
        log_error "密码修复逻辑错误"
        rm -rf "$temp_config_dir"
        exit 1
    fi
else
    log_error "密码不一致检测逻辑错误"
    rm -rf "$temp_config_dir"
    exit 1
fi

# 清理临时文件
rm -rf "$temp_config_dir"

echo

# 测试5: 检查文档更新
log_test "检查文档更新..."

doc_file="$PROJECT_ROOT/scripts/auto_deploy/docs/redis_deployment.md"
if [[ ! -f "$doc_file" ]]; then
    log_error "文档文件不存在"
    exit 1
fi

# 检查密码配置说明
if grep -q "必须设置为相同值" "$doc_file"; then
    log_success "文档包含密码配置重要说明"
else
    log_error "文档缺少密码配置重要说明"
    exit 1
fi

# 检查密码验证工具说明
if grep -q "verify_redis_passwords.sh" "$doc_file"; then
    log_success "文档包含密码验证工具说明"
else
    log_error "文档缺少密码验证工具说明"
    exit 1
fi

# 检查README文件
readme_file="$PROJECT_ROOT/scripts/auto_deploy/tools/README_redis_fix.md"
if grep -q "密码配置不一致" "$readme_file"; then
    log_success "README包含密码不一致问题说明"
else
    log_error "README缺少密码不一致问题说明"
    exit 1
fi

echo

# 总结
log_success "✓ 所有密码一致性修复验证都通过了！"
echo
log_info "修复内容总结："
log_info "1. ✓ 修复了部署脚本中随机密码生成逻辑"
log_info "2. ✓ 添加了密码验证工具 (verify_redis_passwords.sh)"
log_info "3. ✓ 更新了快速修复工具，包含密码一致性检查"
log_info "4. ✓ 更新了文档，说明密码配置的重要性"
log_info "5. ✓ 提供了完整的密码问题诊断和修复流程"
echo
log_info "现在Redis集群部署应该不会再出现密码认证失败问题！"
echo
log_info "使用方法："
log_info "1. 重新部署Redis集群："
log_info "   ./scripts/auto_deploy/deploy_redis.sh"
echo
log_info "2. 验证现有集群的密码配置："
log_info "   ./scripts/auto_deploy/tools/verify_redis_passwords.sh"
echo
log_info "3. 如果发现问题，自动修复："
log_info "   ./scripts/auto_deploy/tools/verify_redis_passwords.sh --fix"
