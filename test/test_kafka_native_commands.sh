#!/bin/bash
# 测试修正后的 Kafka Native 命令

set -euo pipefail

# 测试目录
TEST_DIR="/tmp/test_kafka_native_$$"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 清理函数
cleanup() {
    echo "清理测试环境..."
    rm -rf "$TEST_DIR" 2>/dev/null || true
}

# 设置清理陷阱
trap cleanup EXIT

echo "=== Kafka Native 命令修正验证 ==="

# 创建测试环境
echo "1. 创建测试环境..."
mkdir -p "$TEST_DIR"

# 测试脚本语法
echo "2. 测试脚本语法..."
if bash -n "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 脚本语法检查通过"
else
    echo "✗ 脚本语法检查失败"
    exit 1
fi

# 检查是否移除了 kafka-storage.sh 命令
echo "3. 检查 kafka-storage.sh 命令移除..."
if ! grep -q "kafka-storage.sh" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 已移除 kafka-storage.sh 命令"
else
    echo "✗ 仍包含 kafka-storage.sh 命令"
    echo "发现的 kafka-storage.sh 引用:"
    grep -n "kafka-storage.sh" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"
    exit 1
fi

# 检查集群ID生成方法
echo "4. 检查集群ID生成方法..."
if grep -q "uuidgen\|python3.*uuid\|md5sum" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 使用系统工具生成集群ID"
else
    echo "✗ 集群ID生成方法有问题"
    exit 1
fi

# 检查是否移除了自定义启动命令
echo "5. 检查启动命令简化..."
if ! grep -q "exec.*kafka-server-start.sh" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 已移除自定义启动命令，使用镜像默认配置"
else
    echo "✗ 仍包含自定义启动命令"
    exit 1
fi

# 检查环境变量配置
echo "6. 检查KRaft环境变量..."
kraft_env_vars=(
    "KAFKA_NODE_ID"
    "KAFKA_PROCESS_ROLES"
    "KAFKA_CONTROLLER_QUORUM_VOTERS"
    "KAFKA_LISTENERS"
    "KAFKA_ADVERTISED_LISTENERS"
    "KAFKA_CONTROLLER_LISTENER_NAMES"
    "CLUSTER_ID"
)

for var in "${kraft_env_vars[@]}"; do
    if grep -q "$var" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
        echo "✓ 包含环境变量: $var"
    else
        echo "✗ 缺少环境变量: $var"
        exit 1
    fi
done

# 检查docker-compose配置简化
echo "7. 检查docker-compose配置..."
if grep -q "apache/kafka-native 镜像会根据环境变量自动配置" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 包含镜像自动配置说明"
else
    echo "✗ 缺少镜像自动配置说明"
    exit 1
fi

# 测试集群ID生成逻辑
echo "8. 测试集群ID生成逻辑..."
cat > "$TEST_DIR/test_cluster_id.sh" << 'EOF'
#!/bin/bash

# 模拟集群ID生成逻辑
generate_cluster_id() {
    if command -v uuidgen >/dev/null 2>&1; then
        uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]'
    elif command -v python3 >/dev/null 2>&1; then
        python3 -c 'import uuid; print(str(uuid.uuid4()).replace("-", ""))'
    else
        # 备用方法：使用随机数生成
        echo $(date +%s)$(shuf -i 1000-9999 -n 1 2>/dev/null || echo 1234) | md5sum | cut -d' ' -f1
    fi
}

cluster_id=$(generate_cluster_id)
echo "生成的集群ID: $cluster_id"

# 验证格式（应该是32位十六进制字符）
if [[ ${#cluster_id} -eq 32 && "$cluster_id" =~ ^[a-f0-9]+$ ]]; then
    echo "✓ 集群ID格式正确"
    exit 0
else
    echo "✗ 集群ID格式错误"
    exit 1
fi
EOF

chmod +x "$TEST_DIR/test_cluster_id.sh"

if bash "$TEST_DIR/test_cluster_id.sh"; then
    echo "✓ 集群ID生成逻辑测试通过"
else
    echo "✗ 集群ID生成逻辑测试失败"
    exit 1
fi

# 检查验证命令更新
echo "9. 检查验证命令..."
if grep -q "kafka-broker-api-versions.sh" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 使用正确的验证命令"
else
    echo "✗ 验证命令可能有问题"
    exit 1
fi

# 检查主题管理命令
echo "10. 检查主题管理命令..."
if grep -q "kafka-topics.sh" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 包含主题管理命令"
else
    echo "✗ 缺少主题管理命令"
    exit 1
fi

# 检查容器名称一致性
echo "11. 检查容器名称..."
if grep -q "kafka-\$node_id" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 容器名称配置正确"
else
    echo "✗ 容器名称配置可能有问题"
    exit 1
fi

# 检查端口配置
echo "12. 检查端口配置..."
if grep -q "9092" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh" && grep -q "9093" "$SCRIPT_DIR/scripts/auto_deploy/deploy_kafka.sh"; then
    echo "✓ 端口配置正确"
else
    echo "✗ 端口配置可能有问题"
    exit 1
fi

echo ""
echo "=== 测试结果 ==="
echo "✓ 所有测试通过！"
echo ""
echo "Kafka Native 命令修正验证完成"
echo ""
echo "主要修正："
echo "1. ✓ 移除了不存在的 kafka-storage.sh 命令"
echo "2. ✓ 使用系统工具生成集群ID (uuidgen/python3/md5sum)"
echo "3. ✓ 简化了docker-compose配置，依赖镜像自动配置"
echo "4. ✓ 保留了完整的KRaft环境变量配置"
echo "5. ✓ 使用正确的验证和管理命令"
echo ""
echo "apache/kafka-native:3.9.0 镜像特点："
echo "- 自动处理KRaft模式初始化"
echo "- 根据环境变量自动配置"
echo "- 无需手动格式化存储"
echo "- 使用标准的Kafka命令工具"
echo ""
echo "使用方法："
echo "bash scripts/auto_deploy/deploy_kafka.sh --single-mode"
echo "bash scripts/auto_deploy/deploy_kafka.sh --cluster-mode"
