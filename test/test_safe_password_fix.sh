#!/bin/bash
# 测试安全的密码修复方法

set -euo pipefail

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "[INFO] $*"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $*"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $*"
}

log_test() {
    echo -e "${YELLOW}[TEST]${NC} $*"
}

echo "Redis密码修复安全方法测试"
echo "=========================="

# 创建临时测试目录
test_dir="/tmp/redis_safe_fix_test_$$"
mkdir -p "$test_dir"

# 模拟remote_execute函数
remote_execute() {
    local host=$1
    local command=$2
    # 在本地执行命令（模拟远程执行）
    eval "$command"
}

# 测试1: 普通密码修复
log_test "测试普通密码修复..."

cat > "$test_dir/redis-normal.conf" << EOF
port 6379
bind 0.0.0.0
requirepass NormalPassword123
masterauth DifferentPassword456
cluster-enabled yes
EOF

config_file="$test_dir/redis-normal.conf"
requirepass=$(grep '^requirepass' "$config_file" | awk '{print $2}')
log_info "requirepass: $requirepass"

# 使用新的安全修复方法
remote_execute "localhost" "
    # 备份原配置文件
    cp '$config_file' '$config_file.backup'
    # 删除旧的masterauth行
    grep -v '^masterauth' '$config_file.backup' > '$config_file.tmp'
    # 添加新的masterauth行
    echo 'masterauth $requirepass' >> '$config_file.tmp'
    # 替换原文件
    mv '$config_file.tmp' '$config_file'
"

# 验证修复结果
masterauth_fixed=$(grep '^masterauth' "$config_file" | awk '{print $2}')
log_info "修复后masterauth: $masterauth_fixed"

if [[ "$requirepass" == "$masterauth_fixed" ]]; then
    log_success "普通密码修复成功"
else
    log_error "普通密码修复失败"
    cat "$config_file"
    rm -rf "$test_dir"
    exit 1
fi

echo

# 测试2: 包含特殊字符的密码修复
log_test "测试包含特殊字符的密码修复..."

cat > "$test_dir/redis-special.conf" << 'EOF'
port 6379
bind 0.0.0.0
requirepass Complex@Pass/word&123!
masterauth Simple$Pass#456?
cluster-enabled yes
EOF

config_file="$test_dir/redis-special.conf"
requirepass=$(grep '^requirepass' "$config_file" | awk '{print $2}')
log_info "特殊字符requirepass: $requirepass"

# 使用新的安全修复方法
remote_execute "localhost" "
    # 备份原配置文件
    cp '$config_file' '$config_file.backup'
    # 删除旧的masterauth行
    grep -v '^masterauth' '$config_file.backup' > '$config_file.tmp'
    # 添加新的masterauth行
    echo 'masterauth $requirepass' >> '$config_file.tmp'
    # 替换原文件
    mv '$config_file.tmp' '$config_file'
"

# 验证修复结果
masterauth_fixed=$(grep '^masterauth' "$config_file" | awk '{print $2}')
log_info "修复后masterauth: $masterauth_fixed"

if [[ "$requirepass" == "$masterauth_fixed" ]]; then
    log_success "特殊字符密码修复成功"
else
    log_error "特殊字符密码修复失败"
    cat "$config_file"
    rm -rf "$test_dir"
    exit 1
fi

echo

# 测试3: 包含斜杠的密码修复（sed最容易出错的情况）
log_test "测试包含斜杠的密码修复..."

cat > "$test_dir/redis-slash.conf" << 'EOF'
port 6379
bind 0.0.0.0
requirepass Path/To/Secret/123
masterauth Different/Path/456
cluster-enabled yes
EOF

config_file="$test_dir/redis-slash.conf"
requirepass=$(grep '^requirepass' "$config_file" | awk '{print $2}')
log_info "包含斜杠的requirepass: $requirepass"

# 使用新的安全修复方法
remote_execute "localhost" "
    # 备份原配置文件
    cp '$config_file' '$config_file.backup'
    # 删除旧的masterauth行
    grep -v '^masterauth' '$config_file.backup' > '$config_file.tmp'
    # 添加新的masterauth行
    echo 'masterauth $requirepass' >> '$config_file.tmp'
    # 替换原文件
    mv '$config_file.tmp' '$config_file'
"

# 验证修复结果
masterauth_fixed=$(grep '^masterauth' "$config_file" | awk '{print $2}')
log_info "修复后masterauth: $masterauth_fixed"

if [[ "$requirepass" == "$masterauth_fixed" ]]; then
    log_success "包含斜杠的密码修复成功"
else
    log_error "包含斜杠的密码修复失败"
    cat "$config_file"
    rm -rf "$test_dir"
    exit 1
fi

echo

# 测试4: 验证配置文件完整性
log_test "验证配置文件完整性..."

# 检查所有必要的配置行是否还存在
required_configs=("port 6379" "bind 0.0.0.0" "requirepass" "masterauth" "cluster-enabled yes")
config_ok=true

for config in "${required_configs[@]}"; do
    if grep -q "$config" "$config_file"; then
        log_info "✓ 找到配置: $config"
    else
        log_error "✗ 缺少配置: $config"
        config_ok=false
    fi
done

if [[ "$config_ok" == "true" ]]; then
    log_success "配置文件完整性验证通过"
else
    log_error "配置文件完整性验证失败"
    rm -rf "$test_dir"
    exit 1
fi

echo

# 测试5: 验证备份文件
log_test "验证备份文件功能..."

if [[ -f "$config_file.backup" ]]; then
    log_success "备份文件创建成功"
    
    # 检查备份文件内容
    backup_masterauth=$(grep '^masterauth' "$config_file.backup" | awk '{print $2}')
    log_info "备份文件中的masterauth: $backup_masterauth"
    
    if [[ "$backup_masterauth" != "$requirepass" ]]; then
        log_success "备份文件保留了原始配置"
    else
        log_error "备份文件内容异常"
        rm -rf "$test_dir"
        exit 1
    fi
else
    log_error "备份文件未创建"
    rm -rf "$test_dir"
    exit 1
fi

# 清理测试文件
rm -rf "$test_dir"

echo
log_success "✓ 所有安全密码修复测试都通过了！"
echo
log_info "新的修复方法优势："
log_info "1. ✓ 避免了sed命令的特殊字符问题"
log_info "2. ✓ 支持包含任意特殊字符的密码"
log_info "3. ✓ 自动创建配置文件备份"
log_info "4. ✓ 保持配置文件完整性"
log_info "5. ✓ 更安全可靠的修复过程"
echo
log_info "现在Redis密码修复工具可以处理任何复杂的密码！"
log_info "使用方法："
log_info "  ./scripts/auto_deploy/tools/redis_quick_fix.sh"
log_info "  ./scripts/auto_deploy/tools/verify_redis_passwords.sh --fix"
