#!/bin/bash
# 测试Docker镜像分发功能

set -euo pipefail

# 测试目录
TEST_DIR="/tmp/test_docker_distribution_$$"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 清理函数
cleanup() {
    echo "清理测试环境..."
    rm -rf "$TEST_DIR" 2>/dev/null || true
}

# 设置清理陷阱
trap cleanup EXIT

echo "=== Docker镜像分发功能测试 ==="

# 创建测试环境
echo "1. 创建测试环境..."
mkdir -p "$TEST_DIR"/{software-repo,docker-images,offline-prep/{yum-repo,pip-repo}}

# 创建模拟的Docker镜像文件
echo "2. 创建模拟Docker镜像文件..."
echo "fake docker image content" > "$TEST_DIR/docker-images/apache_kafka-native_3.9.0.tar"
echo "another fake image" > "$TEST_DIR/docker-images/test_image.tar"

# 创建模拟的软件包
echo "3. 创建模拟软件包..."
echo "fake mongodb package" > "$TEST_DIR/software-repo/mongodb-linux-x86_64-rhel70-4.4.18.tgz"
echo "fake redis package" > "$TEST_DIR/software-repo/redis-7.0.8.tar.gz"

# 创建临时配置
echo "4. 创建临时配置..."
cat > "$TEST_DIR/test_config.sh" << 'EOF'
# 临时配置覆盖
SOFTWARE_REPO="$TEST_DIR/software-repo"
DEPLOY_USER="testuser"
SSH_TIMEOUT=5
DEPLOY_TIMEOUT=30

# 模拟函数
log_info() { echo "[INFO] $*"; }
log_warn() { echo "[WARN] $*"; }
log_error() { echo "[ERROR] $*"; }
log_debug() { echo "[DEBUG] $*"; }
show_progress() { echo "Progress: $1/$2 $3"; }
acquire_lock() { return 0; }
get_all_hosts() { echo "testhost1"; echo "testhost2"; }
get_service_by_ip() { echo "mongodb"; echo "redis"; }
check_ssh_connection() { return 0; }
remote_execute() { echo "[REMOTE] $2"; return 0; }
remote_copy() { echo "[COPY] $1 -> $3:$2"; return 0; }
remote_copy_dir() { echo "[COPY_DIR] $1 -> $3:$2"; return 0; }
EOF

# 测试脚本语法
echo "5. 测试脚本语法..."
if bash -n "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ 脚本语法检查通过"
else
    echo "✗ 脚本语法检查失败"
    exit 1
fi

# 测试Docker镜像检测
echo "6. 测试Docker镜像检测..."
cd "$TEST_DIR/docker-images"
IMAGE_COUNT=$(find . -name "*.tar" | wc -l)
if [[ $IMAGE_COUNT -eq 2 ]]; then
    echo "✓ Docker镜像文件检测正确 ($IMAGE_COUNT 个文件)"
else
    echo "✗ Docker镜像文件检测失败 (期望2个，实际$IMAGE_COUNT个)"
    exit 1
fi

# 测试包信息配置
echo "7. 测试包信息配置..."
if grep -q '\["docker-images"\]="docker-images"' "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ Docker镜像包信息配置正确"
else
    echo "✗ Docker镜像包信息配置缺失"
    exit 1
fi

# 测试目标目录配置
echo "8. 测试目标目录配置..."
if grep -q '\["docker-images"\]="/apps/software/docker-images"' "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ Docker镜像目标目录配置正确"
else
    echo "✗ Docker镜像目标目录配置缺失"
    exit 1
fi

# 测试校验和跳过配置
echo "9. 测试校验和跳过配置..."
if grep -q 'docker-images.*]]; then' "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ Docker镜像校验和跳过配置正确"
else
    echo "✗ Docker镜像校验和跳过配置缺失"
    exit 1
fi

# 测试分发函数存在
echo "10. 测试分发函数..."
if grep -q "distribute_docker_images()" "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ Docker镜像分发函数存在"
else
    echo "✗ Docker镜像分发函数缺失"
    exit 1
fi

# 测试加载脚本创建函数
echo "11. 测试加载脚本创建函数..."
if grep -q "create_docker_load_script()" "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ Docker镜像加载脚本创建函数存在"
else
    echo "✗ Docker镜像加载脚本创建函数缺失"
    exit 1
fi

# 测试特殊处理逻辑
echo "12. 测试特殊处理逻辑..."
if grep -q 'distribute_docker_images.*host' "$SCRIPT_DIR/scripts/auto_deploy/distribute_packages.sh"; then
    echo "✓ Docker镜像特殊处理逻辑存在"
else
    echo "✗ Docker镜像特殊处理逻辑缺失"
    exit 1
fi

echo ""
echo "=== 测试结果 ==="
echo "✓ 所有测试通过！"
echo ""
echo "Docker镜像分发功能已成功集成到 distribute_packages.sh 中"
echo ""
echo "功能说明："
echo "1. 自动分发 /apps/software/docker-images 目录中的 .tar 格式Docker镜像文件"
echo "2. 分发到目标主机的 /apps/software/docker-images 目录"
echo "3. 自动创建 load_docker_images.sh 脚本用于加载镜像"
echo "4. 支持 --force-overwrite 参数强制覆盖已存在的镜像"
echo "5. 支持 --dry-run 参数预览分发操作"
echo "6. 如果源目录不存在或没有镜像文件，会跳过分发（不报错）"
echo ""
echo "使用方法："
echo "1. 将Docker镜像文件(.tar格式)放入 /apps/software/docker-images 目录"
echo "2. 运行: bash scripts/auto_deploy/distribute_packages.sh"
echo "3. 在目标主机上运行: bash /apps/software/docker-images/load_docker_images.sh"
