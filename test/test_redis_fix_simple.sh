#!/bin/bash
# Redis修复工具简单测试脚本

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "[INFO] $*"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $*"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $*"
}

log_test() {
    echo -e "${YELLOW}[TEST]${NC} $*"
}

echo "Redis密码认证修复工具验证"
echo "=========================="

# 测试1: 检查工具文件是否存在
log_test "检查修复工具文件..."

tools=(
    "scripts/auto_deploy/tools/fix_redis_auth.sh"
    "scripts/auto_deploy/tools/redis_quick_fix.sh"
    "scripts/auto_deploy/tools/get_redis_password.sh"
    "scripts/auto_deploy/tools/README_redis_fix.md"
)

all_exist=true
for tool in "${tools[@]}"; do
    if [[ -f "$PROJECT_ROOT/$tool" ]]; then
        log_success "存在: $(basename "$tool")"
    else
        log_error "缺失: $(basename "$tool")"
        all_exist=false
    fi
done

if [[ "$all_exist" == "true" ]]; then
    log_success "所有工具文件都存在"
else
    log_error "部分工具文件缺失"
    exit 1
fi

echo

# 测试2: 检查执行权限
log_test "检查执行权限..."

executable_tools=(
    "scripts/auto_deploy/tools/fix_redis_auth.sh"
    "scripts/auto_deploy/tools/redis_quick_fix.sh"
    "scripts/auto_deploy/tools/get_redis_password.sh"
)

all_executable=true
for tool in "${executable_tools[@]}"; do
    if [[ -x "$PROJECT_ROOT/$tool" ]]; then
        log_success "可执行: $(basename "$tool")"
    else
        log_error "不可执行: $(basename "$tool")"
        all_executable=false
    fi
done

if [[ "$all_executable" == "true" ]]; then
    log_success "所有工具都有执行权限"
else
    log_error "部分工具缺少执行权限"
    exit 1
fi

echo

# 测试3: 检查帮助信息
log_test "检查帮助信息..."

help_tools=(
    "scripts/auto_deploy/tools/fix_redis_auth.sh"
    "scripts/auto_deploy/tools/get_redis_password.sh"
)

all_help_ok=true
for tool in "${help_tools[@]}"; do
    if "$PROJECT_ROOT/$tool" --help >/dev/null 2>&1; then
        log_success "帮助正常: $(basename "$tool")"
    else
        log_error "帮助异常: $(basename "$tool")"
        all_help_ok=false
    fi
done

if [[ "$all_help_ok" == "true" ]]; then
    log_success "所有工具帮助信息正常"
else
    log_error "部分工具帮助信息异常"
    exit 1
fi

echo

# 测试4: 检查部署脚本改进
log_test "检查部署脚本改进..."

deploy_script="$PROJECT_ROOT/scripts/auto_deploy/deploy_redis.sh"
if [[ ! -f "$deploy_script" ]]; then
    log_error "部署脚本不存在"
    exit 1
fi

improvements_found=0

# 检查改进的密码认证逻辑
if grep -q "检查节点.*的Redis服务状态" "$deploy_script"; then
    log_success "包含改进的服务状态检查"
    ((improvements_found++))
else
    log_error "缺少改进的服务状态检查"
fi

# 检查详细的错误诊断
if grep -q "显示详细的调试信息" "$deploy_script"; then
    log_success "包含详细的错误诊断"
    ((improvements_found++))
else
    log_error "缺少详细的错误诊断"
fi

# 检查修复建议
if grep -q "修复建议" "$deploy_script"; then
    log_success "包含修复建议"
    ((improvements_found++))
else
    log_error "缺少修复建议"
fi

if [[ $improvements_found -eq 3 ]]; then
    log_success "部署脚本包含所有改进"
else
    log_error "部署脚本缺少部分改进 ($improvements_found/3)"
    exit 1
fi

echo

# 测试5: 检查文档更新
log_test "检查文档更新..."

doc_file="$PROJECT_ROOT/scripts/auto_deploy/docs/redis_deployment.md"
if [[ ! -f "$doc_file" ]]; then
    log_error "文档文件不存在"
    exit 1
fi

doc_updates=0

# 检查故障排除工具章节
if grep -q "故障排除工具" "$doc_file"; then
    log_success "文档包含故障排除工具章节"
    ((doc_updates++))
else
    log_error "文档缺少故障排除工具章节"
fi

# 检查快速修复工具说明
if grep -q "redis_quick_fix.sh" "$doc_file"; then
    log_success "文档包含快速修复工具说明"
    ((doc_updates++))
else
    log_error "文档缺少快速修复工具说明"
fi

if [[ $doc_updates -eq 2 ]]; then
    log_success "文档更新完整"
else
    log_error "文档更新不完整 ($doc_updates/2)"
    exit 1
fi

echo

# 测试6: 模拟密码认证测试
log_test "模拟密码认证测试..."

# 创建临时配置文件
temp_config="/tmp/test_redis_auth.conf"
cat > "$temp_config" << EOF
port 6379
bind 0.0.0.0
requirepass TestPassword123
masterauth TestPassword123
cluster-enabled yes
EOF

# 测试密码提取
extracted_password=$(grep '^requirepass' "$temp_config" | awk '{print $2}')
if [[ "$extracted_password" == "TestPassword123" ]]; then
    log_success "密码提取逻辑正确"
else
    log_error "密码提取逻辑错误"
    rm -f "$temp_config"
    exit 1
fi

# 清理
rm -f "$temp_config"

echo

# 总结
log_success "✓ 所有测试都通过了！"
echo
log_info "Redis密码认证修复工具已准备就绪"
echo
log_info "使用方法："
log_info "1. 快速修复所有Redis节点："
log_info "   ./scripts/auto_deploy/tools/redis_quick_fix.sh"
echo
log_info "2. 详细诊断和修复："
log_info "   ./scripts/auto_deploy/tools/fix_redis_auth.sh --verbose"
log_info "   ./scripts/auto_deploy/tools/fix_redis_auth.sh --fix"
echo
log_info "3. 获取Redis密码："
log_info "   ./scripts/auto_deploy/tools/get_redis_password.sh"
log_info "   ./scripts/auto_deploy/tools/get_redis_password.sh -t"
echo
log_info "4. 查看详细文档："
log_info "   cat ./scripts/auto_deploy/tools/README_redis_fix.md"
echo
log_success "修复工具验证完成！"
