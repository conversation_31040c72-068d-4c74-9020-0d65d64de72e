#!/bin/bash
# Kafka配置测试脚本
# 用于验证Kafka docker-compose配置的正确性

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PARENT_DIR="$(dirname "$SCRIPT_DIR")"
source "$PARENT_DIR/scripts/auto_deploy/lib/common.sh"

# 脚本信息
SCRIPT_NAME="Kafka配置测试脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 测试配置
# =============================================================================

# 测试用的临时目录
TEST_DIR="/tmp/kafka-config-test"
TEST_COMPOSE_DIR="$TEST_DIR/docker-compose"

# 清理测试环境
cleanup_test_env() {
    log_info "清理测试环境..."
    rm -rf "$TEST_DIR"
}

# 设置清理陷阱
trap cleanup_test_env EXIT

# =============================================================================
# 配置验证函数
# =============================================================================

# 验证Kafka配置语法
validate_kafka_config() {
    log_info "验证Kafka配置语法..."
    
    # 创建测试目录
    mkdir -p "$TEST_COMPOSE_DIR"
    
    # 模拟配置参数
    local node_id=1
    local host="***********"
    local kafka_cluster_id="test12345678901234567890123456789012"
    local quorum_voters="1@***********:9093,2@***********:9093,3@***********:9093"
    
    # 生成测试配置
    local compose_config="version: '3.3'

services:
  kafka:
    image: apache/kafka-native:3.9.0
    hostname: kafka-$node_id
    container_name: kafka-$node_id
    ports:
      - \"9092:9092\"
      - \"9093:9093\"
    environment:
      KAFKA_NODE_ID: $node_id
      KAFKA_PROCESS_ROLES: 'broker,controller'
      KAFKA_CONTROLLER_QUORUM_VOTERS: '$quorum_voters'
      KAFKA_LISTENERS: 'PLAINTEXT://:19092,CONTROLLER://:9093,PLAINTEXT_HOST://:9092'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'PLAINTEXT'
      KAFKA_ADVERTISED_LISTENERS: 'PLAINTEXT://kafka-$node_id:19092,PLAINTEXT_HOST://$host:9092'
      KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
      KAFKA_LOG_DIRS: '/var/lib/kafka/data'
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_NUM_PARTITIONS: 3
      CLUSTER_ID: '$kafka_cluster_id'
    volumes:
      - /apps/data/kafka:/var/lib/kafka/data:rw
      - /apps/logs/kafka:/var/log/kafka:rw
    restart: unless-stopped

networks:
  default:
    driver: bridge"
    
    # 写入配置文件
    echo "$compose_config" > "$TEST_COMPOSE_DIR/docker-compose.yml"
    
    # 验证docker-compose配置语法
    if command -v docker-compose >/dev/null 2>&1; then
        log_info "使用docker-compose验证配置..."
        if docker-compose -f "$TEST_COMPOSE_DIR/docker-compose.yml" config >/dev/null 2>&1; then
            log_info "✓ docker-compose配置语法验证通过"
        else
            log_error "✗ docker-compose配置语法验证失败"
            docker-compose -f "$TEST_COMPOSE_DIR/docker-compose.yml" config
            return 1
        fi
    elif command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        log_info "使用docker compose验证配置..."
        if docker compose -f "$TEST_COMPOSE_DIR/docker-compose.yml" config >/dev/null 2>&1; then
            log_info "✓ docker compose配置语法验证通过"
        else
            log_error "✗ docker compose配置语法验证失败"
            docker compose -f "$TEST_COMPOSE_DIR/docker-compose.yml" config
            return 1
        fi
    else
        log_warn "Docker Compose未安装，跳过配置语法验证"
    fi
    
    # 验证关键配置项
    log_info "验证关键配置项..."
    
    # 检查监听器安全协议映射
    if grep -q "KAFKA_LISTENER_SECURITY_PROTOCOL_MAP.*PLAINTEXT_HOST:PLAINTEXT" "$TEST_COMPOSE_DIR/docker-compose.yml"; then
        log_info "✓ PLAINTEXT_HOST监听器安全协议配置正确"
    else
        log_error "✗ PLAINTEXT_HOST监听器安全协议配置缺失"
        return 1
    fi
    
    # 检查集群配置
    if grep -q "KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3" "$TEST_COMPOSE_DIR/docker-compose.yml"; then
        log_info "✓ 集群复制因子配置正确"
    else
        log_error "✗ 集群复制因子配置错误"
        return 1
    fi
    
    # 检查端口配置
    if grep -q "9092:9092" "$TEST_COMPOSE_DIR/docker-compose.yml" && grep -q "9093:9093" "$TEST_COMPOSE_DIR/docker-compose.yml"; then
        log_info "✓ 端口映射配置正确"
    else
        log_error "✗ 端口映射配置错误"
        return 1
    fi
    
    log_info "✓ Kafka配置验证完成"
}

# 验证主机配置
validate_host_config() {
    log_info "验证主机配置..."
    
    # 检查KAFKA_HOSTS数组
    if [[ ${#KAFKA_HOSTS[@]} -eq 0 ]]; then
        log_error "✗ KAFKA_HOSTS配置为空"
        return 1
    fi
    
    if [[ ${#KAFKA_HOSTS[@]} -ne 3 ]]; then
        log_warn "KAFKA_HOSTS配置了${#KAFKA_HOSTS[@]}个节点，建议使用3个节点"
    fi
    
    log_info "Kafka集群主机: ${KAFKA_HOSTS[*]}"
    
    # 验证主机IP格式
    for host in "${KAFKA_HOSTS[@]}"; do
        if [[ ! $host =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            log_error "✗ 主机IP格式错误: $host"
            return 1
        fi
        log_info "✓ 主机IP格式正确: $host"
    done
    
    log_info "✓ 主机配置验证完成"
}

# 验证网络连通性
validate_network_connectivity() {
    log_info "验证网络连通性..."
    
    for host in "${KAFKA_HOSTS[@]}"; do
        log_info "测试连接到 $host..."
        
        # 测试SSH连接
        if timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes "$DEPLOY_USER@$host" "echo 'SSH连接测试成功'" >/dev/null 2>&1; then
            log_info "✓ SSH连接正常: $host"
        else
            log_error "✗ SSH连接失败: $host"
            return 1
        fi
        
        # 测试端口连通性
        for port in 9092 9093; do
            if timeout 5 bash -c "</dev/tcp/$host/$port" >/dev/null 2>&1; then
                log_warn "端口 $port 在 $host 上已被占用"
            else
                log_info "✓ 端口 $port 在 $host 上可用"
            fi
        done
    done
    
    log_info "✓ 网络连通性验证完成"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始Kafka配置测试..."
    
    # 验证配置语法
    validate_kafka_config
    
    # 验证主机配置
    validate_host_config
    
    # 验证网络连通性（可选）
    if [[ "${1:-}" != "--skip-network" ]]; then
        validate_network_connectivity
    else
        log_info "跳过网络连通性验证"
    fi
    
    log_info "=== Kafka配置测试完成 ==="
    log_info "所有测试项目均通过，可以开始部署Kafka集群"
}

# 显示帮助信息
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  --skip-network    跳过网络连通性测试"
    echo "  -h, --help        显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  本脚本用于验证Kafka部署配置的正确性"
    echo "  包括docker-compose配置语法、主机配置和网络连通性测试"
    exit 0
fi

# 执行主函数
main "$@"
