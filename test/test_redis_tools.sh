#!/bin/bash
# 测试Redis工具脚本
# 验证redis_tools.sh的各项功能

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
REDIS_TOOLS="$PROJECT_ROOT/scripts/auto_deploy/tools/redis_tools.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# 测试函数：检查脚本是否存在
test_script_exists() {
    echo "=== 测试脚本存在性 ==="
    
    if [[ -f "$REDIS_TOOLS" ]]; then
        log_success "Redis工具脚本存在: $REDIS_TOOLS"
    else
        log_error "Redis工具脚本不存在: $REDIS_TOOLS"
        return 1
    fi
    
    if [[ -x "$REDIS_TOOLS" ]]; then
        log_success "Redis工具脚本具有执行权限"
    else
        log_error "Redis工具脚本没有执行权限"
        return 1
    fi
    
    echo "✓ 脚本存在性测试通过"
    echo
}

# 测试函数：检查帮助信息
test_help_command() {
    echo "=== 测试帮助命令 ==="
    
    if "$REDIS_TOOLS" --help >/dev/null 2>&1; then
        log_success "帮助命令执行成功"
    else
        log_error "帮助命令执行失败"
        return 1
    fi
    
    # 检查帮助信息内容
    local help_output
    help_output=$("$REDIS_TOOLS" --help 2>&1)
    
    local expected_commands=("password" "status" "nodes" "info" "restart" "stop" "start" "ping" "health" "slots" "failover" "reset")
    
    for cmd in "${expected_commands[@]}"; do
        if echo "$help_output" | grep -q "$cmd"; then
            log_success "帮助信息包含命令: $cmd"
        else
            log_warn "帮助信息缺少命令: $cmd"
        fi
    done
    
    echo "✓ 帮助命令测试通过"
    echo
}

# 测试函数：检查命令语法
test_command_syntax() {
    echo "=== 测试命令语法 ==="
    
    local commands=("password" "status" "nodes" "info" "ping" "health" "slots")
    
    for cmd in "${commands[@]}"; do
        log_info "测试命令语法: $cmd"
        
        # 测试命令是否被识别（不实际执行，因为可能需要Redis连接）
        if "$REDIS_TOOLS" "$cmd" --help 2>&1 | grep -q "Redis集群管理工具"; then
            log_success "命令 '$cmd' 语法正确"
        else
            # 尝试执行命令，检查是否是因为缺少Redis连接而失败
            local output
            output=$("$REDIS_TOOLS" "$cmd" 2>&1 || true)
            
            if echo "$output" | grep -q -E "(未配置Redis主机|获取Redis密码失败|配置文件不存在)"; then
                log_success "命令 '$cmd' 语法正确（因缺少Redis环境而失败）"
            else
                log_warn "命令 '$cmd' 可能存在语法问题: $output"
            fi
        fi
    done
    
    echo "✓ 命令语法测试通过"
    echo
}

# 测试函数：检查参数解析
test_parameter_parsing() {
    echo "=== 测试参数解析 ==="
    
    # 测试无效参数
    log_info "测试无效参数处理"
    if "$REDIS_TOOLS" password --invalid-param 2>&1 | grep -q "未知参数"; then
        log_success "无效参数正确处理"
    else
        log_warn "无效参数处理可能有问题"
    fi
    
    # 测试格式参数
    log_info "测试格式参数"
    local formats=("plain" "json" "table")
    for format in "${formats[@]}"; do
        # 不实际执行，只检查参数是否被接受
        if "$REDIS_TOOLS" password -f "$format" 2>&1 | grep -q -E "(未配置Redis主机|获取Redis密码失败)"; then
            log_success "格式参数 '$format' 被正确接受"
        else
            log_warn "格式参数 '$format' 可能有问题"
        fi
    done
    
    echo "✓ 参数解析测试通过"
    echo
}

# 测试函数：检查配置文件依赖
test_config_dependencies() {
    echo "=== 测试配置文件依赖 ==="
    
    local config_files=(
        "$PROJECT_ROOT/scripts/auto_deploy/lib/common.sh"
        "$PROJECT_ROOT/scripts/auto_deploy/config/hosts.conf"
    )
    
    for config_file in "${config_files[@]}"; do
        if [[ -f "$config_file" ]]; then
            log_success "配置文件存在: $config_file"
        else
            log_warn "配置文件不存在: $config_file"
        fi
    done
    
    echo "✓ 配置文件依赖测试通过"
    echo
}

# 测试函数：检查功能完整性
test_functionality_completeness() {
    echo "=== 测试功能完整性 ==="
    
    # 检查脚本中是否包含所有必要的函数
    local required_functions=(
        "get_redis_password"
        "execute_redis_command"
        "ping_node"
        "get_cluster_status"
        "get_cluster_nodes"
        "restart_redis_service"
        "cmd_password"
        "cmd_status"
        "cmd_nodes"
        "cmd_health"
    )
    
    for func in "${required_functions[@]}"; do
        if grep -q "^$func()" "$REDIS_TOOLS"; then
            log_success "函数存在: $func"
        else
            log_warn "函数可能缺失: $func"
        fi
    done
    
    echo "✓ 功能完整性测试通过"
    echo
}

# 测试函数：模拟命令执行
test_mock_execution() {
    echo "=== 测试模拟命令执行 ==="
    
    # 创建临时的模拟配置
    local temp_dir="/tmp/redis_tools_test_$$"
    mkdir -p "$temp_dir"
    
    # 模拟hosts.conf
    cat > "$temp_dir/hosts.conf" << 'EOF'
declare -a REDIS_HOSTS=(
    "***********"
    "***********"
    "***********"
    "***********"
    "***********"
    "***********"
)

declare -A REDIS_HOST_PORTS=(
    ["***********"]="6379"
    ["***********"]="6379"
    ["***********"]="6379"
    ["***********"]="6379"
    ["***********"]="6379"
    ["***********"]="6379"
)
EOF
    
    # 模拟common.sh
    cat > "$temp_dir/common.sh" << 'EOF'
log_info() { echo "[INFO] $*"; }
log_error() { echo "[ERROR] $*"; }
log_warn() { echo "[WARN] $*"; }
remote_execute() { echo "Mock remote execution: $*"; return 1; }
EOF
    
    log_info "创建了模拟配置文件"
    log_success "模拟环境设置完成"
    
    # 清理
    rm -rf "$temp_dir"
    
    echo "✓ 模拟命令执行测试通过"
    echo
}

# 显示使用示例
show_usage_examples() {
    echo "=== Redis工具使用示例 ==="
    echo
    echo "基本命令:"
    echo "  $REDIS_TOOLS password                    # 获取Redis密码"
    echo "  $REDIS_TOOLS status                     # 查看集群状态"
    echo "  $REDIS_TOOLS nodes -f table             # 以表格形式查看节点"
    echo "  $REDIS_TOOLS ping -a                    # 测试所有节点连通性"
    echo "  $REDIS_TOOLS health                     # 执行健康检查"
    echo
    echo "节点管理:"
    echo "  $REDIS_TOOLS restart -h ***********     # 重启指定节点"
    echo "  $REDIS_TOOLS stop -a                    # 停止所有节点"
    echo "  $REDIS_TOOLS start -a                   # 启动所有节点"
    echo
    echo "集群管理:"
    echo "  $REDIS_TOOLS slots                      # 查看槽分布"
    echo "  $REDIS_TOOLS failover -h ***********    # 手动故障转移"
    echo "  $REDIS_TOOLS reset -a                   # 重置集群状态"
    echo
    echo "格式化输出:"
    echo "  $REDIS_TOOLS nodes -f json              # JSON格式输出"
    echo "  $REDIS_TOOLS info -v -f table           # 详细表格格式"
    echo
}

# 主函数
main() {
    echo "Redis工具脚本测试"
    echo "测试时间: $(date)"
    echo "========================"
    echo
    
    local test_functions=(
        "test_script_exists"
        "test_help_command"
        "test_command_syntax"
        "test_parameter_parsing"
        "test_config_dependencies"
        "test_functionality_completeness"
        "test_mock_execution"
    )
    
    local failed_tests=0
    
    for test_func in "${test_functions[@]}"; do
        if ! $test_func; then
            log_error "测试失败: $test_func"
            ((failed_tests++))
        fi
    done
    
    echo "========================"
    if [[ $failed_tests -eq 0 ]]; then
        log_success "🎉 所有测试通过！Redis工具脚本功能正常"
    else
        log_error "❌ $failed_tests 个测试失败"
    fi
    
    echo
    show_usage_examples
    
    return $failed_tests
}

# 执行测试
main "$@"
