#!/bin/bash
# Redis密码认证问题快速验证脚本

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_success() {
    echo -e "${GREEN}[PASS]${NC} $*"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $*"
}

log_test() {
    echo -e "${YELLOW}[TEST]${NC} $*"
}

echo "Redis密码认证问题修复快速验证"
echo "=============================="

# 验证计数器
passed=0
failed=0

# 测试函数
test_item() {
    local name="$1"
    local condition="$2"
    
    log_test "$name"
    if eval "$condition"; then
        log_success "$name"
        ((passed++))
    else
        log_error "$name"
        ((failed++))
    fi
}

# 1. 检查工具文件存在
echo
echo "1. 工具文件检查"
echo "---------------"

test_item "redis_quick_fix.sh存在" "[[ -f '$PROJECT_ROOT/scripts/auto_deploy/tools/redis_quick_fix.sh' ]]"
test_item "fix_redis_auth.sh存在" "[[ -f '$PROJECT_ROOT/scripts/auto_deploy/tools/fix_redis_auth.sh' ]]"
test_item "verify_redis_passwords.sh存在" "[[ -f '$PROJECT_ROOT/scripts/auto_deploy/tools/verify_redis_passwords.sh' ]]"
test_item "get_redis_password.sh存在" "[[ -f '$PROJECT_ROOT/scripts/auto_deploy/tools/get_redis_password.sh' ]]"

# 2. 检查执行权限
echo
echo "2. 执行权限检查"
echo "---------------"

test_item "redis_quick_fix.sh可执行" "[[ -x '$PROJECT_ROOT/scripts/auto_deploy/tools/redis_quick_fix.sh' ]]"
test_item "fix_redis_auth.sh可执行" "[[ -x '$PROJECT_ROOT/scripts/auto_deploy/tools/fix_redis_auth.sh' ]]"
test_item "verify_redis_passwords.sh可执行" "[[ -x '$PROJECT_ROOT/scripts/auto_deploy/tools/verify_redis_passwords.sh' ]]"
test_item "get_redis_password.sh可执行" "[[ -x '$PROJECT_ROOT/scripts/auto_deploy/tools/get_redis_password.sh' ]]"

# 3. 检查脚本语法
echo
echo "3. 脚本语法检查"
echo "---------------"

test_item "redis_quick_fix.sh语法正确" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/tools/redis_quick_fix.sh'"
test_item "fix_redis_auth.sh语法正确" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/tools/fix_redis_auth.sh'"
test_item "verify_redis_passwords.sh语法正确" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/tools/verify_redis_passwords.sh'"
test_item "get_redis_password.sh语法正确" "bash -n '$PROJECT_ROOT/scripts/auto_deploy/tools/get_redis_password.sh'"

# 4. 检查部署脚本修复
echo
echo "4. 部署脚本修复检查"
echo "-------------------"

deploy_script="$PROJECT_ROOT/scripts/auto_deploy/deploy_redis.sh"
test_item "密码生成逻辑已修复" "grep -q 'master_password=\"\$password\"' '$deploy_script'"
test_item "包含密码配置说明" "grep -q 'requirepass和masterauth使用相同密码' '$deploy_script'"

# 5. 检查sed命令修复
echo
echo "5. sed命令修复检查"
echo "------------------"

test_item "redis_quick_fix.sh中sed命令已修复" "grep -q 'sed -i \"s/\^masterauth.*/masterauth \$requirepass/\"' '$PROJECT_ROOT/scripts/auto_deploy/tools/redis_quick_fix.sh'"
test_item "verify_redis_passwords.sh中sed命令已修复" "grep -q 'sed -i \"s/\^masterauth.*/masterauth \$requirepass/\"' '$PROJECT_ROOT/scripts/auto_deploy/tools/verify_redis_passwords.sh'"

# 6. 检查文档更新
echo
echo "6. 文档更新检查"
echo "---------------"

doc_file="$PROJECT_ROOT/scripts/auto_deploy/docs/redis_deployment.md"
readme_file="$PROJECT_ROOT/scripts/auto_deploy/tools/README_redis_fix.md"

test_item "文档包含密码配置说明" "grep -q '必须设置为相同值' '$doc_file'"
test_item "README包含修复说明" "grep -q '密码配置不一致' '$readme_file'"

# 7. 运行sed修复测试
echo
echo "7. sed修复功能测试"
echo "------------------"

if [[ -f "$PROJECT_ROOT/test/test_sed_fix.sh" ]]; then
    test_item "sed修复功能测试" "'$PROJECT_ROOT/test/test_sed_fix.sh' >/dev/null 2>&1"
else
    log_error "sed测试脚本不存在"
    ((failed++))
fi

# 总结
echo
echo "验证总结"
echo "========"

total=$((passed + failed))
echo "总计: $total"
echo "通过: $passed"
echo "失败: $failed"

if [[ $failed -eq 0 ]]; then
    echo
    log_success "✓ 所有验证都通过了！"
    echo
    echo "Redis密码认证问题修复完成！"
    echo
    echo "主要修复内容："
    echo "1. ✓ 修复了部署脚本中requirepass和masterauth密码不一致问题"
    echo "2. ✓ 修复了sed命令的引号处理问题"
    echo "3. ✓ 添加了完整的密码认证修复工具集"
    echo "4. ✓ 提供了详细的错误诊断和修复建议"
    echo "5. ✓ 更新了文档和使用说明"
    echo
    echo "使用方法："
    echo "• 快速修复: ./scripts/auto_deploy/tools/redis_quick_fix.sh"
    echo "• 密码验证: ./scripts/auto_deploy/tools/verify_redis_passwords.sh"
    echo "• 重新部署: ./scripts/auto_deploy/deploy_redis.sh"
    echo
    echo "现在可以安全使用Redis部署脚本，不会再出现密码认证失败问题！"
    exit 0
else
    echo
    log_error "✗ 有 $failed 个验证失败"
    echo "请检查失败的项目并修复"
    exit 1
fi
