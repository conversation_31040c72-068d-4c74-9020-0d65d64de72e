#!/bin/bash
# 测试Redis集群节点列表生成修复
# 验证集群初始化逻辑是否正确

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 直接定义测试用的配置，避免加载复杂的配置文件
declare -a REDIS_HOSTS=(
    "***********"  # redis-master-01
    "***********"  # redis-master-02
    "***********"  # redis-master-03
    "***********"  # redis-slave-01
    "***********"  # redis-slave-02
    "***********"  # redis-slave-03
)

declare -A REDIS_HOST_PORTS=(
    ["***********"]="6379"
    ["***********"]="6379"
    ["***********"]="6379"
    ["***********"]="6379"
    ["***********"]="6379"
    ["***********"]="6379"
)

declare -a REDIS_CLUSTER_PORTS=(
    "6379"
)

# 测试函数：模拟集群节点列表生成
test_cluster_node_generation() {
    echo "=== 测试Redis集群节点列表生成 ==="
    
    # 模拟原始的错误逻辑
    echo "原始错误逻辑（仅生成一个节点）:"
    local cluster_nodes_old=()
    local unique_hosts=($(printf '%s\n' "${REDIS_HOSTS[@]}" | sort -u))
    
    for port in "${REDIS_CLUSTER_PORTS[@]}"; do
        for host in "${unique_hosts[@]}"; do
            local host_ports=""
            if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
                host_ports="${REDIS_HOST_PORTS[$host]}"
                if [[ "$host_ports" == *"$port"* ]]; then
                    cluster_nodes_old+=("$host:$port")
                    break  # 这里是问题所在 - 只找到第一个就退出
                fi
            fi
        done
    done
    
    echo "错误逻辑生成的节点: ${cluster_nodes_old[*]}"
    echo "节点数量: ${#cluster_nodes_old[@]}"
    echo
    
    # 模拟修复后的正确逻辑
    echo "修复后的正确逻辑（生成所有节点）:"
    local cluster_nodes_new=()
    
    for host in "${unique_hosts[@]}"; do
        local host_ports=""
        if [[ -n "${REDIS_HOST_PORTS[$host]}" ]]; then
            host_ports="${REDIS_HOST_PORTS[$host]}"
            IFS=',' read -ra ports <<< "$host_ports"
            for port in "${ports[@]}"; do
                cluster_nodes_new+=("$host:$port")
            done
        fi
    done
    
    echo "正确逻辑生成的节点: ${cluster_nodes_new[*]}"
    echo "节点数量: ${#cluster_nodes_new[@]}"
    echo
    
    # 验证结果
    if [[ ${#cluster_nodes_new[@]} -eq 6 ]]; then
        echo "✓ 节点数量正确 (6个节点)"
    else
        echo "✗ 节点数量错误，期望6个，实际${#cluster_nodes_new[@]}个"
        return 1
    fi
    
    # 验证所有预期的主机都包含在内
    local expected_hosts=("***********" "***********" "***********" "***********" "***********" "***********")
    for expected_host in "${expected_hosts[@]}"; do
        local found=false
        for node in "${cluster_nodes_new[@]}"; do
            if [[ "$node" == "$expected_host:6379" ]]; then
                found=true
                break
            fi
        done
        
        if [[ "$found" == "true" ]]; then
            echo "✓ 主机 $expected_host 已包含在集群节点中"
        else
            echo "✗ 主机 $expected_host 未包含在集群节点中"
            return 1
        fi
    done
    
    echo "✓ 所有测试通过"
}

# 测试Redis集群命令参数验证
test_cluster_command_validation() {
    echo "=== 测试Redis集群命令参数验证 ==="
    
    local cluster_nodes=("***********:6379" "***********:6379" "***********:6379" 
                         "***********:6379" "***********:6379" "***********:6379")
    
    echo "集群节点: ${cluster_nodes[*]}"
    
    local node_count=${#cluster_nodes[@]}
    local expected_replicas=1
    local min_nodes=$((3 * (expected_replicas + 1)))
    
    echo "节点数量: $node_count"
    echo "期望副本数: $expected_replicas"
    echo "最少需要节点数: $min_nodes"
    
    if [[ $node_count -ge $min_nodes ]]; then
        echo "✓ 节点数量满足集群要求"
    else
        echo "✗ 节点数量不足"
        return 1
    fi
    
    # 模拟集群创建命令
    local cluster_command="redis-cli --cluster create ${cluster_nodes[*]} --cluster-replicas 1"
    echo "集群创建命令: $cluster_command"
    
    # 验证命令参数数量
    local total_args=$((${#cluster_nodes[@]} + 3))  # 节点数 + --cluster + create + --cluster-replicas + 1
    echo "命令参数总数: $total_args"
    
    if [[ ${#cluster_nodes[@]} -gt 0 ]]; then
        echo "✓ 集群命令参数验证通过"
    else
        echo "✗ 集群命令参数验证失败"
        return 1
    fi
}

# 显示配置信息
show_config_info() {
    echo "=== Redis集群配置信息 ==="
    echo "REDIS_HOSTS: ${REDIS_HOSTS[*]}"
    echo "REDIS_CLUSTER_PORTS: ${REDIS_CLUSTER_PORTS[*]}"
    echo "REDIS_HOST_PORTS:"
    for host in "${REDIS_HOSTS[@]}"; do
        echo "  $host: ${REDIS_HOST_PORTS[$host]}"
    done
    echo
}

# 主函数
main() {
    echo "Redis集群修复测试"
    echo "测试时间: $(date)"
    echo "===================="
    echo
    
    show_config_info
    
    if test_cluster_node_generation; then
        echo "✓ 集群节点生成测试通过"
    else
        echo "✗ 集群节点生成测试失败"
        exit 1
    fi
    
    echo
    
    if test_cluster_command_validation; then
        echo "✓ 集群命令验证测试通过"
    else
        echo "✗ 集群命令验证测试失败"
        exit 1
    fi
    
    echo
    echo "🎉 所有测试通过！Redis集群初始化逻辑修复成功"
}

# 执行测试
main "$@"
